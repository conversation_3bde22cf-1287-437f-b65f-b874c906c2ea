# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an ICBC (Industrial and Commercial Bank of China) payment system built with Laravel 12, PHP 8.2, and Vue 3. The system integrates with ICBC's payment gateway to handle RSA2 signature verification, payment processing, and callback handling.

## Development Commands

### Laravel/PHP Commands
```bash
# Start development server
php artisan serve --host=0.0.0.0 --port=8000

# Run tests
php artisan test

# Clear config cache
php artisan config:clear

# Run migrations
php artisan migrate

# Start queue worker
php artisan queue:listen --tries=1

# View logs
php artisan pail --timeout=0

# Network diagnostics
php artisan network:diagnose
```

### Frontend Commands
```bash
# Development server
npm run dev

# Build for production
npm run build

# Build with SSR
npm run build:ssr

# Run linting
npm run lint

# Format code
npm run format
```

### Full Development Environment
```bash
# Start all services (server, queue, logs, vite)
composer dev

# Start with SSR
composer dev:ssr
```

### Testing Commands
```bash
# Run Laravel tests
composer test

# Run specific test
php artisan test --filter=IcbcPaymentTest

# Run ICBC payment tests (custom test scripts)
cd icbc_test && php test_icbc_payment.php
```

## Architecture

### Core Components

- **ICBC Payment SDK** (`packages/icbc-pay/`): Custom Laravel package for ICBC payment integration
- **Payment Controllers**: Handle payment requests and ICBC callback notifications
- **Rate Limiter Service** (`app/Services/IcbcRateLimiter.php`): Prevents concurrent payment requests
- **Frontend**: Vue 3 + Inertia.js with Tailwind CSS and Reka UI components

### Key Files

- **Payment Processing**: `app/Http/Controllers/IcbcNotifyController.php`
- **Configuration**: `config/icbc-pay.php` - Main ICBC payment configuration
- **Certificates**: `storage/keys/` - RSA private/public keys for signature verification
- **Frontend Entry**: `resources/js/app.ts`
- **Payment Views**: `resources/views/icbc-pay/` - Blade templates for payment flows

### Payment Flow

1. User initiates payment through `/parking` endpoint
2. System generates signed payment request to ICBC gateway
3. User completes payment on ICBC interface
4. ICBC sends callback to `IcbcNotifyController@handleNotify`
5. System verifies signature and updates payment status

### Environment Configuration

Key environment variables for ICBC integration:
- `ICBC_APP_ID`: Application ID from ICBC
- `ICBC_MER_ID`: Merchant ID
- `ICBC_MER_PRTCL_NO`: Merchant protocol number
- `ICBC_ENVIRONMENT`: 'sandbox' or 'production'
- `ICBC_SIGN_TYPE`: 'RSA2' (default)

### Testing Infrastructure

- **Test Directory**: `icbc_test/` contains 66+ PHP test scripts for payment functionality
- **Documentation**: `icbc-md/` contains 20+ technical documentation files
- **Laravel Tests**: `tests/Feature/IcbcPaymentTest.php` for integration testing

### Security Features

- RSA2 signature verification for all ICBC communications
- Rate limiting to prevent concurrent payment submissions
- Certificate-based authentication with ICBC gateway
- Input validation and sanitization for payment parameters

### Known Issues & Solutions

- **Signature Verification**: Use `icbc_test/fix_sign_verify_failed.php` for debugging
- **Timestamp Issues**: Beijing time zone handling in `config/icbc-pay.php`
- **Concurrency Control**: `IcbcRateLimiter` prevents error code 500032

## Development Notes

- Payment gateway supports WeChat Pay, Alipay, and UnionPay through ICBC
- All payment amounts must be in cents (multiply by 100)
- Callback URLs must be publicly accessible for production
- Certificate files in `storage/keys/` are required for signature operations
- The system includes comprehensive logging for debugging payment flows