# 工商银行聚合支付配置指南

## 概述

本指南将帮助您正确配置工商银行聚合支付系统。我们的代码已经完全实现，但需要正确的配置参数才能正常工作。

## 当前状态

✅ **代码实现完成**
- 支付服务类 (`IcbcPayService`) 已完整实现
- 支持UI模式和无界面模式
- 完整的签名生成和验证
- 支持微信和支付宝支付
- 完整的错误处理和日志记录

✅ **数据库设计完成**
- 支付记录表结构
- 订单状态管理
- 支付回调处理

✅ **前端界面完成**
- 停车费支付页面
- 支付结果页面
- 订单查询功能

⚠️ **需要配置工商银行商户信息**

## 配置步骤

### 1. 获取工商银行商户信息

您需要联系工商银行获取以下信息：

- **APP_ID**: 应用标识
- **MER_ID**: 商户号
- **MER_PRTCL_NO**: 商户协议号
- **商户私钥**: 用于签名的RSA私钥
- **工商银行公钥**: 用于验证响应的公钥
- **API网关公钥**: 网关验证用公钥

### 2. 更新.env配置

```env
# 工商银行支付配置
ICBC_APP_ID=您的APP_ID
ICBC_MER_ID=您的商户号
ICBC_MER_PRTCL_NO=您的商户协议号
ICBC_GATEWAY_URL=https://gw.open.icbc.com.cn
ICBC_NOTIFY_URL=https://您的域名/icbc-pay/notify
ICBC_RETURN_URL=https://您的域名/icbc-pay/return
ICBC_USE_UI_MODE=true

# 密钥文件路径
ICBC_PRIVATE_KEY_PATH=/您的项目路径/storage/keys/icbc_private_key.pem
ICBC_PUBLIC_KEY_PATH=/您的项目路径/storage/keys/icbc_public_key.pem
ICBC_APIGW_PUBLIC_KEY_PATH=/您的项目路径/storage/keys/icbc_apigw_public_key.pem

# 可选配置
ICBC_SANDBOX=false  # 生产环境设为false
ICBC_LOG_ENABLED=true
ICBC_TIMEOUT=30
```

### 3. 配置密钥文件

将工商银行提供的密钥文件放置到 `storage/keys/` 目录：

```bash
# 创建密钥目录
mkdir -p storage/keys

# 设置文件权限
chmod 700 storage/keys
chmod 600 storage/keys/*.pem
```

**私钥格式示例** (`icbc_private_key.pem`):
```
-----BEGIN RSA PRIVATE KEY-----
您的私钥内容
-----END RSA PRIVATE KEY-----
```

**公钥格式示例** (`icbc_public_key.pem`):
```
-----BEGIN PUBLIC KEY-----
您的公钥内容
-----END PUBLIC KEY-----
```

### 4. 验证配置

运行配置验证脚本：

```bash
php validate_icbc_config.php
```

如果所有项目显示 ✅，则配置正确。

## 测试支付流程

### 1. 快速测试

```bash
php quick_test.php
```

### 2. 详细测试

```bash
php debug_payment.php
```

### 3. Web界面测试

访问以下URL进行测试：
- 主支付页面: `http://您的域名/parking`
- API测试: `php test_icbc_payment.php`

## 常见问题

### Q1: 参数校验失败

**原因**: 
- APP_ID与证书不匹配
- 商户号配置错误
- 签名算法不正确

**解决方案**:
1. 确认工商银行提供的APP_ID和商户号
2. 检查私钥格式是否正确
3. 联系工商银行技术支持确认配置

### Q2: 网络连接失败

**原因**:
- 服务器无法访问工商银行网关
- SSL证书验证失败

**解决方案**:
1. 检查服务器网络连接
2. 确保可以访问 `https://gw.open.icbc.com.cn`
3. 检查防火墙设置

### Q3: 签名验证失败

**原因**:
- 私钥格式错误
- 签名算法不匹配

**解决方案**:
1. 确认私钥为RSA格式
2. 检查签名算法为RSA2 (SHA256)
3. 确认密钥文件编码为UTF-8

## 支持的支付方式

- ✅ 微信支付 (pay_mode=9)
- ✅ 支付宝支付 (pay_mode=10)
- ✅ 扫码支付 (access_type=1)
- ✅ APP支付 (access_type=5)
- ✅ 公众号支付 (access_type=7)
- ✅ 小程序支付 (access_type=9)

## API接口说明

### 创建支付

```php
use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

// 创建支付记录
$paymentRecord = PaymentRecord::create([
    'out_trade_no' => '唯一订单号',
    'total_amount' => 0.01, // 元
    'subject' => '商品描述',
    'payment_method' => 'wechat', // 或 'alipay'
    'car_number' => '车牌号',
    'status' => 'pending'
]);

// 创建支付
$service = new IcbcPayService();
$result = $service->createPayment($paymentRecord);

if ($result['success']) {
    // 跳转到支付页面
    header('Location: ' . $result['payment_url']);
}
```

### 支付回调处理

```php
// 在路由中处理回调
Route::post('/icbc-pay/notify', [ParkingController::class, 'handlePaymentCallback']);
```

### 查询支付状态

```php
$service = new IcbcPayService();
$result = $service->queryPayment($outTradeNo);
```

## 部署检查清单

- [ ] 已获取工商银行商户信息
- [ ] 已配置.env文件
- [ ] 已部署密钥文件
- [ ] 已设置文件权限
- [ ] 已配置回调URL
- [ ] 已测试网络连接
- [ ] 已验证配置正确性
- [ ] 已测试支付流程

## 技术支持

如果遇到配置问题：

1. **检查日志**: `storage/logs/laravel.log`
2. **运行验证**: `php validate_icbc_config.php`
3. **查看文档**: 工商银行API开发文档
4. **联系银行**: 工商银行技术支持

## 生产环境注意事项

1. **安全性**:
   - 私钥文件权限设为600
   - 不要将密钥提交到版本控制系统
   - 使用HTTPS协议

2. **性能**:
   - 启用Redis缓存
   - 配置队列处理异步任务
   - 设置合理的超时时间

3. **监控**:
   - 监控支付成功率
   - 设置错误报警
   - 定期检查日志

## 更新日志

- **v1.0.0**: 完整的工商银行支付集成
- 支持UI模式和API模式
- 完整的错误处理和日志记录
- 支持微信和支付宝支付
- 完整的前端界面

---

**注意**: 本系统代码已完整实现，只需要正确的工商银行商户配置即可正常使用。如果在配置过程中遇到问题，请参考本指南或联系技术支持。 