# 🔧 网络错误故障排除指南

## 问题描述
用户在使用停车费支付系统时遇到"网络错误，请重试"的提示。

## 🔍 诊断步骤

### 1. 访问调试页面
打开浏览器访问：`http://your-domain/debug/network-page`

这个页面会自动运行以下测试：
- ✅ 检查工商银行支付配置
- ✅ 测试网络连接
- ✅ 模拟支付订单创建
- ✅ 完整的支付流程测试

### 2. 检查服务器日志
```bash
# 查看Laravel日志
tail -f storage/logs/laravel.log

# 查看工商银行支付专用日志
tail -f storage/logs/icbc-pay.log

# 查看Nginx错误日志（如果使用Nginx）
tail -f /var/log/nginx/error.log
```

### 3. 手动测试API接口
```bash
# 测试支付创建API
curl -X POST http://your-domain/api/pay \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: your-csrf-token" \
  -d '{
    "car_number": "测试A12345",
    "amount": 1.00,
    "payment_method": "wechat",
    "parking_duration": 60
  }'

# 测试配置检查API
curl -s http://your-domain/debug/icbc-payment | jq .
```

### 4. 检查配置文件
```bash
# 验证环境变量
php artisan tinker --execute="
echo 'Gateway URL: ' . config('icbc-pay.gateway_url') . PHP_EOL;
echo 'UI Mode: ' . (config('icbc-pay.payment_config.use_ui_mode') ? 'true' : 'false') . PHP_EOL;
echo 'App ID: ' . config('icbc-pay.app_id') . PHP_EOL;
"

# 清除配置缓存
php artisan config:clear
```

## 🚨 常见问题及解决方案

### 问题1: CSRF Token错误
**症状**: 前端请求返回419状态码
**解决方案**:
```javascript
// 确保页面包含CSRF token
<meta name="csrf-token" content="{{ csrf_token() }}">

// 在AJAX请求中包含token
headers: {
    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
}
```

### 问题2: 路由不存在
**症状**: 404错误
**解决方案**:
```bash
# 检查路由列表
php artisan route:list | grep pay

# 清除路由缓存
php artisan route:clear
```

### 问题3: 数据库连接问题
**症状**: 数据库相关错误
**解决方案**:
```bash
# 检查数据库连接
php artisan tinker --execute="DB::connection()->getPdo();"

# 运行数据库迁移
php artisan migrate
```

### 问题4: 工商银行API配置错误
**症状**: 签名验证失败或API调用失败
**解决方案**:
```bash
# 检查配置
php artisan tinker --execute="
\$config = config('icbc-pay');
echo 'Gateway: ' . \$config['gateway_url'] . PHP_EOL;
echo 'App ID: ' . \$config['app_id'] . PHP_EOL;
echo 'Merchant ID: ' . \$config['mer_id'] . PHP_EOL;
"
```

### 问题5: 网络连接问题
**症状**: 连接超时或DNS解析失败
**解决方案**:
```bash
# 测试网络连接
curl -I https://gw.open.icbc.com.cn

# 检查DNS解析
nslookup gw.open.icbc.com.cn

# 测试端口连通性
telnet gw.open.icbc.com.cn 443
```

## 🔧 快速修复命令

### 重置配置
```bash
# 清除所有缓存
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# 重新生成配置缓存
php artisan config:cache
```

### 检查权限
```bash
# 确保存储目录可写
chmod -R 775 storage
chmod -R 775 bootstrap/cache

# 确保日志目录存在
mkdir -p storage/logs
chmod 775 storage/logs
```

### 重启服务
```bash
# 重启PHP-FPM（如果使用）
sudo systemctl restart php8.0-fpm

# 重启Nginx（如果使用）
sudo systemctl restart nginx

# 重启队列工作进程（如果使用）
php artisan queue:restart
```

## 📊 监控和调试工具

### 1. 实时日志监控
```bash
# 监控所有相关日志
tail -f storage/logs/laravel.log storage/logs/icbc-pay.log /var/log/nginx/error.log
```

### 2. 性能分析
```bash
# 检查响应时间
time curl -X POST http://your-domain/api/pay \
  -H "Content-Type: application/json" \
  -d '{"car_number":"测试001","amount":1.00,"payment_method":"wechat"}'
```

### 3. 数据库查询
```sql
-- 查看最近的支付记录
SELECT * FROM icbc_payment_records 
ORDER BY created_at DESC 
LIMIT 10;

-- 查看失败的支付记录
SELECT * FROM icbc_payment_records 
WHERE status = 'failed' 
ORDER BY created_at DESC 
LIMIT 5;
```

## 🎯 预防措施

### 1. 健康检查
创建定期健康检查脚本：
```bash
#!/bin/bash
# health_check.sh

echo "检查工商银行支付系统健康状态..."

# 检查API响应
response=$(curl -s -o /dev/null -w "%{http_code}" http://your-domain/debug/icbc-payment)
if [ "$response" = "200" ]; then
    echo "✅ API响应正常"
else
    echo "❌ API响应异常: $response"
fi

# 检查数据库连接
php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'Database: OK'; } catch (Exception \$e) { echo 'Database: ERROR - ' . \$e->getMessage(); }"
```

### 2. 监控告警
设置监控告警，当出现以下情况时发送通知：
- API响应时间超过5秒
- 错误率超过5%
- 数据库连接失败
- 磁盘空间不足

### 3. 备份策略
- 定期备份数据库
- 备份配置文件
- 备份日志文件

## 📞 联系支持

如果以上步骤都无法解决问题，请提供以下信息：

1. **错误截图**：包含完整的错误信息
2. **浏览器控制台日志**：F12 -> Console标签页的内容
3. **服务器日志**：最近的Laravel日志和Nginx日志
4. **系统环境**：PHP版本、Laravel版本、操作系统
5. **复现步骤**：详细的操作步骤

---

**提示**: 大多数"网络错误"问题都是由配置错误、权限问题或缓存问题引起的，按照上述步骤逐一排查通常能够解决问题。 