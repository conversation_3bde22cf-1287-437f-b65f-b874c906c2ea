# 工商银行停车费支付流程指南

## 📋 支付流程概览

### 方式一：API + 前端跳转（推荐）

1. **创建支付订单**（API接口）
2. **获取支付参数**（返回JSON）
3. **前端跳转到支付页面**

### 方式二：直接跳转（简单）

1. **直接访问支付链接**（返回HTML页面）
2. **自动跳转到工行支付**

---

## 🔧 接口使用说明

### 1. 创建支付订单

**接口：** `POST /api/pay`

**请求参数：**
```json
{
    "car_number": "新M16800",
    "amount": 1.00,
    "payment_method": "wechat",
    "parking_duration": 60
}
```

**返回示例：**
```json
{
    "success": true,
    "message": "支付订单创建成功",
    "data": {
        "payment_id": 4,
        "out_trade_no": "PARK_20250523141818723448925",
        "request_params": {
            "app_id": "11000000000000052474",
            "method": "mybank.credit.pay.trade.wap.pay",
            "format": "json",
            "charset": "UTF-8",
            "sign_type": "RSA2",
            "timestamp": "2025-05-23 14:18:18",
            "version": "1.0.0.0",
            "notify_url": "https://your-domain.com/icbc-pay/notify",
            "biz_content": "{\"out_trade_no\":\"PARK_20250523141818723448925\",\"total_amount\":\"1.00\",\"subject\":\"停车费支付\",\"body\":\"停车费支付\",\"timeout_express\":\"30m\"}",
            "sign": "S/Q8+mHfWDUA8TMpLtitZf6M4G..."
        },
        "gateway_url": "https://gw.open.icbc.com.cn/api",
        "payment_form": "<form id=\"icbcPayForm\" action=\"https://gw.open.icbc.com.cn/api\" method=\"POST\" style=\"display:none;\">..."
    }
}
```

### 2. 跳转到支付页面

**方式A：通过payment_id跳转**

**接口：** `POST /api/pay/redirect`

**请求参数：**
```json
{
    "payment_id": 4
}
```

**返回：** HTML支付页面（自动跳转到工行）

---

**方式B：通过订单号直接跳转**

**接口：** `GET /api/pay/{订单号}`

**示例：** `GET /api/pay/PARK_20250523141818723448925`

**返回：** HTML支付页面（自动跳转到工行）

---

## 💻 前端实现示例

### JavaScript Ajax方式

```javascript
// 1. 创建支付订单
async function createPayment() {
    const response = await fetch('/api/pay', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            car_number: '新M16800',
            amount: 1.00,
            payment_method: 'wechat',
            parking_duration: 60
        })
    });
    
    const result = await response.json();
    
    if (result.success) {
        // 方式1：使用payment_form直接插入页面
        document.body.innerHTML = result.data.payment_form;
        
        // 方式2：或者跳转到支付页面
        // window.location.href = `/api/pay/${result.data.out_trade_no}`;
    }
}
```

### 直接链接方式

```html
<!-- 直接生成支付链接 -->
<a href="/api/pay/PARK_20250523141818723448925" target="_blank">
    点击支付
</a>
```

---

## 🔄 支付状态查询

支付完成后，工行会回调notify_url，同时可以通过以下方式查询支付状态：

```php
$paymentRecord = PaymentRecord::where('out_trade_no', 'PARK_20250523141818723448925')->first();
echo $paymentRecord->status; // pending, success, failed, closed
```

---

## ⚡ 快速测试

1. **创建测试订单：**
```bash
curl -X POST http://your-domain.com/api/pay \
  -H "Content-Type: application/json" \
  -d '{
    "car_number": "测试A12345",
    "amount": 0.01,
    "payment_method": "alipay",
    "parking_duration": 30
  }'
```

2. **获取订单号并访问支付页面：**
```
GET http://your-domain.com/api/pay/{返回的订单号}
```

---

## 🛡️ 安全特性

- ✅ 订单号唯一性保证
- ✅ 重复提交检测（同车牌5分钟内）
- ✅ 支付状态验证
- ✅ 签名验证
- ✅ 自动重试机制

---

## 📝 注意事项

1. **支付方式：** 
   - `alipay`：支付宝
   - `wechat`：微信支付

2. **订单状态：**
   - `pending`：待支付
   - `success`：支付成功
   - `failed`：支付失败
   - `closed`：交易关闭

3. **金额格式：** 单位为元，支持小数（如：1.50）

4. **超时时间：** 订单有效期30分钟

5. **回调处理：** 确保notify_url可以正常访问 