# 工商银行聚合支付系统部署指南

## 🚀 部署概览

本系统集成了工商银行聚合支付B2C线上消费下单接口，支持微信支付和支付宝支付。系统包含完整的前端界面、后端API接口、支付回调处理和数据库管理。

## 📋 系统要求

### 服务器要求
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+ 推荐)
- **PHP版本**: PHP 8.0 或更高版本
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+
- **数据库**: MySQL 8.0+ 或 PostgreSQL 13+
- **SSL证书**: 必须支持HTTPS（工商银行强制要求）

### PHP扩展要求
```bash
# 必需扩展
php8.0-curl
php8.0-openssl
php8.0-mbstring
php8.0-json
php8.0-pdo
php8.0-mysql  # 或 php8.0-pgsql
php8.0-bcmath
php8.0-gd
php8.0-xml
php8.0-zip
```

## 🛠️ 部署步骤

### 1. 代码部署

```bash
# 克隆代码到服务器
git clone https://your-repo.git /var/www/html/parking-payment
cd /var/www/html/parking-payment

# 安装依赖
composer install --no-dev --optimize-autoloader

# 设置目录权限
sudo chown -R www-data:www-data /var/www/html/parking-payment
sudo chmod -R 755 /var/www/html/parking-payment
sudo chmod -R 775 storage bootstrap/cache
```

### 2. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 生成应用密钥
php artisan key:generate
```

编辑`.env`文件：

```bash
# 应用基本配置
APP_NAME="停车费支付系统"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=parking_payment
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

# 工商银行支付配置
ICBC_APP_ID=10000000000000052474
ICBC_MER_ID=4402000000000001
ICBC_MER_PRTCL_NO=44020000000000001
ICBC_GATEWAY_URL=https://gw.open.icbc.com.cn/api
ICBC_SIGN_TYPE=RSA2
ICBC_CHARSET=UTF-8
ICBC_FORMAT=json
ICBC_VERSION=V1

# 工商银行密钥配置
ICBC_PRIVATE_KEY_PATH=/var/www/html/parking-payment/storage/keys/icbc_private_key.pem
ICBC_APIGW_PUBLIC_KEY_PATH=/var/www/html/parking-payment/storage/keys/icbc_apigw_public_key.pem

# 工商银行回调配置
ICBC_NOTIFY_URL=https://your-domain.com/icbc-pay/notify
ICBC_RETURN_URL=https://your-domain.com/icbc-pay/return
ICBC_DEVICE_INFO=PARKING_SYSTEM_001

# 微信配置
ICBC_WECHAT_SHOP_APPID=wx8888888888888888
ICBC_WECHAT_SHOP_SECRET=your_wechat_shop_secret

# 支付宝配置
ICBC_ALIPAY_SHOP_APPID=2021000000000000

# 日志配置
ICBC_LOG_ENABLED=true
ICBC_LOG_LEVEL=info
```

### 3. 密钥配置

```bash
# 创建密钥目录
mkdir -p storage/keys
chmod 700 storage/keys

# 将工商银行提供的私钥文件放入目录
# 注意：确保私钥文件权限为600
sudo cp /path/to/icbc_private_key.pem storage/keys/
sudo cp /path/to/icbc_apigw_public_key.pem storage/keys/
sudo chmod 600 storage/keys/*.pem
sudo chown www-data:www-data storage/keys/*.pem
```

### 4. 数据库初始化

```bash
# 运行数据库迁移
php artisan migrate --force

# 发布配置文件（如果需要自定义）
php artisan vendor:publish --tag=icbc-pay-config
```

### 5. Web服务器配置

#### Nginx配置

创建配置文件 `/etc/nginx/sites-available/parking-payment`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    root /var/www/html/parking-payment/public;
    index index.php;

    # SSL配置
    ssl_certificate /path/to/your/ssl/certificate.crt;
    ssl_certificate_key /path/to/your/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';" always;

    # Laravel配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 增加超时时间（支付接口可能较慢）
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 隐藏敏感文件
    location ~ /\. {
        deny all;
    }

    location ~ /(storage|bootstrap|vendor|tests|database) {
        deny all;
    }

    # 日志配置
    access_log /var/log/nginx/parking-payment.access.log;
    error_log /var/log/nginx/parking-payment.error.log;
}
```

启用站点：

```bash
sudo ln -s /etc/nginx/sites-available/parking-payment /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. 性能优化

```bash
# 优化配置缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 安装Redis（可选，用于缓存和队列）
sudo apt install redis-server
php artisan queue:work redis --daemon
```

### 7. 监控和日志

#### 配置日志轮转

创建文件 `/etc/logrotate.d/parking-payment`:

```
/var/www/html/parking-payment/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    copytruncate
    postrotate
        systemctl reload php8.0-fpm
    endscript
}
```

#### 设置监控脚本

创建监控脚本 `/usr/local/bin/check_payment_service.sh`:

```bash
#!/bin/bash

SERVICE_URL="https://your-domain.com/parking"
LOG_FILE="/var/log/parking-payment-monitor.log"

# 检查服务是否可访问
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$SERVICE_URL")

if [ "$HTTP_CODE" != "200" ]; then
    echo "$(date): Payment service is down - HTTP $HTTP_CODE" >> "$LOG_FILE"
    # 发送告警（可接入短信、邮件等）
    # /path/to/send_alert.sh "Payment service is down"
else
    echo "$(date): Payment service is healthy" >> "$LOG_FILE"
fi
```

添加到crontab：

```bash
# 每分钟检查一次
*/1 * * * * /usr/local/bin/check_payment_service.sh
```

## 🔧 生产环境配置

### 1. 数据库优化

#### MySQL配置优化 (`/etc/mysql/mysql.conf.d/mysqld.cnf`)

```ini
[mysqld]
# InnoDB优化
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 1
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 16M

# 连接数配置
max_connections = 500
max_connect_errors = 1000

# 超时配置
wait_timeout = 600
interactive_timeout = 600
```

### 2. PHP-FPM优化

编辑 `/etc/php/8.0/fpm/pool.d/www.conf`:

```ini
; 进程管理
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 15
pm.max_requests = 1000

; 内存限制
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 300
php_admin_value[upload_max_filesize] = 10M
php_admin_value[post_max_size] = 10M
```

### 3. 安全配置

#### 防火墙设置

```bash
# UFW防火墙配置
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 3306/tcp  # 禁止外部访问数据库
```

#### 文件权限加固

```bash
# 设置严格的文件权限
find /var/www/html/parking-payment -type f -exec chmod 644 {} \;
find /var/www/html/parking-payment -type d -exec chmod 755 {} \;
chmod 600 /var/www/html/parking-payment/.env
chmod 600 /var/www/html/parking-payment/storage/keys/*.pem
```

## 🧪 测试验证

### 1. 功能测试

```bash
# 运行完整测试套件
php artisan test

# 测试支付接口
curl -X POST https://your-domain.com/api/pay \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: your-csrf-token" \
  -d '{
    "car_number": "测试A12345",
    "amount": 1.00,
    "payment_method": "wechat",
    "parking_duration": 60
  }'
```

### 2. 性能测试

```bash
# 使用Apache Bench进行压力测试
ab -n 1000 -c 10 https://your-domain.com/parking

# 使用wrk进行负载测试
wrk -t12 -c400 -d30s https://your-domain.com/parking
```

### 3. 安全测试

```bash
# SSL配置检查
ssl-checker https://your-domain.com

# 安全头检查
curl -I https://your-domain.com
```

## 📊 监控指标

### 关键监控指标

1. **业务指标**
   - 支付成功率
   - 平均支付时长
   - 订单处理量
   - 回调处理成功率

2. **技术指标**
   - 响应时间
   - 并发用户数
   - 数据库连接数
   - 内存使用率
   - CPU使用率

3. **错误监控**
   - HTTP 5xx错误率
   - 支付接口调用失败
   - 数据库连接失败
   - 签名验证失败

### 告警配置

```bash
# 示例：当5xx错误率超过1%时告警
if [ error_rate > 0.01 ]; then
    send_alert "High error rate detected"
fi

# 支付成功率低于95%时告警
if [ payment_success_rate < 0.95 ]; then
    send_alert "Payment success rate is low"
fi
```

## 🔄 备份和恢复

### 数据库备份

```bash
#!/bin/bash
# 创建每日数据库备份脚本

BACKUP_DIR="/backup/mysql"
DB_NAME="parking_payment"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"

mysqldump -u backup_user -p"$BACKUP_PASSWORD" \
  --single-transaction \
  --routines \
  --triggers \
  "$DB_NAME" | gzip > "$BACKUP_DIR/parking_payment_$DATE.sql.gz"

# 保留30天的备份
find "$BACKUP_DIR" -name "parking_payment_*.sql.gz" -mtime +30 -delete
```

### 代码备份

```bash
#!/bin/bash
# 代码备份脚本

SOURCE_DIR="/var/www/html/parking-payment"
BACKUP_DIR="/backup/code"
DATE=$(date +%Y%m%d_%H%M%S)

tar -czf "$BACKUP_DIR/parking_payment_code_$DATE.tar.gz" \
  --exclude='storage/logs/*' \
  --exclude='storage/cache/*' \
  --exclude='node_modules' \
  --exclude='.git' \
  "$SOURCE_DIR"

# 保留7天的代码备份
find "$BACKUP_DIR" -name "parking_payment_code_*.tar.gz" -mtime +7 -delete
```

## 🚨 故障排查

### 常见问题及解决方案

1. **支付接口调用失败**
   ```bash
   # 检查网络连接
   curl -v https://gw.open.icbc.com.cn/api
   
   # 检查证书有效性
   openssl x509 -in storage/keys/icbc_private_key.pem -text -noout
   
   # 查看详细日志
   tail -f storage/logs/laravel.log | grep ICBC
   ```

2. **数据库连接问题**
   ```bash
   # 检查数据库状态
   systemctl status mysql
   
   # 检查连接数
   mysql -e "SHOW PROCESSLIST;"
   
   # 检查数据库配置
   php artisan tinker
   >>> DB::connection()->getPdo();
   ```

3. **性能问题**
   ```bash
   # 检查PHP-FPM状态
   systemctl status php8.0-fpm
   
   # 查看慢查询日志
   tail -f /var/log/mysql/mysql-slow.log
   
   # 检查内存使用
   free -h
   ps aux --sort=-%mem | head
   ```

## 📞 技术支持

- **系统日志**: `/var/www/html/parking-payment/storage/logs/`
- **Nginx日志**: `/var/log/nginx/parking-payment.*.log`
- **工商银行技术支持**: 95588
- **系统管理员**: [your-admin-contact]

---

**注意**: 
1. 所有密钥文件必须妥善保管，严禁泄露
2. 定期更新系统和依赖包
3. 建议定期进行安全审计
4. 生产环境部署前务必在测试环境充分验证

部署完成后，请访问 `https://your-domain.com/parking` 验证系统是否正常运行。 