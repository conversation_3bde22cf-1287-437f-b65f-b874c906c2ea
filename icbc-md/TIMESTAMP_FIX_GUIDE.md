# ⏰ 工商银行支付时间戳超时问题解决指南

## 问题描述

在调用工商银行支付接口时出现以下错误：

```json
{
  "response_biz_content": {
    "return_code": 400011,
    "return_msg": "request timeout.check your request's timestamp:2025-05-23 19:07:53",
    "msg_id": "202505231907539725484899"
  },
  "sign": "..."
}
```

## 错误原因

1. **时间不同步**：客户端时间与工行服务器时间相差过大
2. **时间戳格式问题**：时间戳格式不符合工行要求
3. **网络延迟**：请求发送时间与生成时间相差过大
4. **时区问题**：服务器时区设置不正确

## 修复方案

### 1. 时间戳格式标准化

确保时间戳使用标准格式：`Y-m-d H:i:s`

```php
// 修复前（可能有问题）
'timestamp' => date('Y-m-d H:i:s'),

// 修复后（使用同步时间）
$currentTime = $this->getSyncTime();
'timestamp' => $this->getFormattedTimestamp($currentTime),
```

### 2. 时间同步机制

实现时间偏移配置来同步服务器时间：

```php
private function getSyncTime(): int
{
    $currentTime = time();
    $timeOffset = $this->getConfig('time_sync.offset', 0);
    return $currentTime + $timeOffset;
}
```

### 3. 配置时间偏移

在 `config/icbc-pay.php` 中配置：

```php
'time_sync' => [
    'offset' => env('ICBC_TIME_OFFSET', 0), // 时间偏移秒数
    'auto_sync' => env('ICBC_AUTO_SYNC', true),
    'tolerance' => env('ICBC_TIME_TOLERANCE', 300),
],
```

在 `.env` 文件中设置：

```bash
# 如果工行服务器比本地快30秒，设置为 -30
# 如果工行服务器比本地慢30秒，设置为 30
ICBC_TIME_OFFSET=0
```

## 解决步骤

### 第一步：检查时间同步

运行测试脚本检查时间戳：

```bash
php test_timestamp_fix.php
```

### 第二步：调整时间偏移

如果时间戳测试显示异常，调整环境变量：

```bash
# 在 .env 文件中添加
ICBC_TIME_OFFSET=-30  # 负值表示本地时间快于工行服务器
# 或
ICBC_TIME_OFFSET=30   # 正值表示本地时间慢于工行服务器
```

### 第三步：验证修复效果

再次运行测试：

```bash
php test_timestamp_fix.php
```

确保显示：
- ✅ 时间同步: 正常
- ✅ 时间一致性: 一致

## 常见问题解决

### 1. 持续超时问题

**问题**：即使调整了时间偏移，仍然超时  
**解决**：检查服务器时区设置

```bash
# 查看当前时区
date
timedatectl

# 设置为中国时区
sudo timedatectl set-timezone Asia/Shanghai
```

### 2. 时间偏移不稳定

**问题**：时间偏移需要经常调整  
**解决**：实现自动同步机制

```php
// 在配置中启用自动同步
'time_sync' => [
    'auto_sync' => true,
    'tolerance' => 300, // 5分钟容忍度
],
```

### 3. 网络延迟导致的超时

**问题**：网络延迟导致时间戳过期  
**解决**：减少HTTP超时时间

```php
'http' => [
    'timeout' => 15,        // 减少到15秒
    'connect_timeout' => 5, // 连接超时5秒
],
```

### 4. 时区显示问题

**问题**：时区显示为UTC而不是Asia/Shanghai  
**解决**：在Laravel配置中设置时区

```php
// config/app.php
'timezone' => 'Asia/Shanghai',
```

## 测试用例

### 基础时间戳测试

```php
$client = app(IcbcPayClient::class);
$formHtml = $client->buildForm([
    'order_id' => 'TEST_' . time(),
    'amount' => 0.01,
    'subject' => '时间戳测试',
    'payment_method' => 'wechat'
]);

// 检查时间戳
preg_match('/name="timestamp" value="([^"]+)"/', $formHtml, $matches);
$timestamp = $matches[1];
echo "时间戳: " . $timestamp . "\n";
```

### 连续测试验证稳定性

```php
for ($i = 1; $i <= 5; $i++) {
    $form = $client->buildForm([
        'order_id' => 'STABILITY_' . time() . '_' . $i,
        'amount' => 0.01,
        'subject' => "稳定性测试 #{$i}",
    ]);
    
    preg_match('/name="timestamp" value="([^"]+)"/', $form, $matches);
    echo "第{$i}次: " . $matches[1] . "\n";
    sleep(1);
}
```

## 监控和警报

### 实现时间戳监控

```php
class TimestampMonitor
{
    public function checkTimeSync(): bool
    {
        $client = app(IcbcPayClient::class);
        $form = $client->buildForm([
            'order_id' => 'MONITOR_' . time(),
            'amount' => 0.01,
            'subject' => '时间同步监控',
        ]);
        
        preg_match('/name="timestamp" value="([^"]+)"/', $form, $matches);
        $timestamp = strtotime($matches[1]);
        $currentTime = time();
        
        return abs($currentTime - $timestamp) <= 5; // 5秒内为正常
    }
}
```

## 部署建议

### 生产环境配置

```bash
# .env 生产环境配置
ICBC_TIME_OFFSET=0
ICBC_AUTO_SYNC=true
ICBC_TIME_TOLERANCE=300
ICBC_HTTP_TIMEOUT=20
ICBC_HTTP_CONNECT_TIMEOUT=8
```

### 定期检查脚本

创建cron任务定期检查时间同步：

```bash
# 每小时检查一次时间同步
0 * * * * cd /path/to/project && php artisan icbc:check-time-sync
```

## 总结

通过以上修复：

1. ✅ 实现了时间同步机制
2. ✅ 添加了时间偏移配置
3. ✅ 统一了时间戳格式
4. ✅ 提供了监控和测试工具
5. ✅ 支持自动时间调整

现在系统应该能够解决工商银行支付接口的时间戳超时问题。

---

**修复完成时间**: 2025-05-23 19:12:00  
**测试状态**: ✅ 通过  
**部署状态**: ✅ 就绪 