# 工商银行聚合支付API集成指南

## 📋 概述

本文档详细说明如何集成工商银行聚合支付B2C线上消费下单接口，实现微信支付和支付宝支付功能。

系统支持两种支付模式：
- **UI界面模式**（推荐）：用户跳转到工商银行官方支付页面
- **无界面模式**：返回支付参数，在商户页面调用支付SDK

## 🔗 API接口信息

### UI界面模式（推荐）
- **接口名称**: 聚合支付B2C线上消费下单（有界面）
- **接口地址**: `/ui/cardbusiness/aggregatepay/b2c/online/ui/consumepurchaseshowpay/V1`
- **完整URL**: `https://gw.open.icbc.com.cn/ui/cardbusiness/aggregatepay/b2c/online/ui/consumepurchaseshowpay/V1`
- **请求方式**: GET（通过URL参数传递）
- **特点**: 跳转到工商银行官方支付界面

### 无界面模式
- **接口名称**: 聚合支付B2C线上消费下单（无界面）
- **API ID**: 10000000000000137000
- **接口地址**: `/cardbusiness/aggregatepay/b2c/online/consumepurchase/V1`
- **请求方式**: POST
- **服务编号**: P0067
- **版本号**: V1
- **特点**: 返回支付参数供商户调用SDK

## 🛠️ 环境配置

### 1. 配置环境变量

在`.env`文件中添加以下配置：

```bash
# 工商银行应用配置
ICBC_APP_ID=10000000000000052474
ICBC_MER_ID=****************
ICBC_MER_PRTCL_NO=*****************

# 网关配置
ICBC_GATEWAY_URL=https://gw.open.icbc.com.cn/api
ICBC_SIGN_TYPE=RSA2
ICBC_CHARSET=UTF-8
ICBC_FORMAT=json
ICBC_VERSION=V1

# 支付模式选择（true=使用工银UI界面, false=无界面模式）
ICBC_USE_UI_MODE=true

# 密钥配置（方式一：直接配置）
ICBC_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwgg...
-----END PRIVATE KEY-----"

ICBC_APIGW_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC...
-----END PUBLIC KEY-----"

# 或者使用文件路径（方式二：文件路径）
ICBC_PRIVATE_KEY_PATH=storage/keys/icbc_private_key.pem
ICBC_APIGW_PUBLIC_KEY_PATH=storage/keys/icbc_apigw_public_key.pem

# 回调配置
ICBC_NOTIFY_URL=https://your-domain.com/icbc-pay/notify
ICBC_RETURN_URL=https://your-domain.com/icbc-pay/return

# 微信配置
ICBC_WECHAT_SHOP_APPID=wx8888888888888888
ICBC_WECHAT_SHOP_SECRET=your_wechat_shop_secret

# 支付宝配置
ICBC_ALIPAY_SHOP_APPID=2021000000000000

# 设备信息
ICBC_DEVICE_INFO=PARKING_SYSTEM_001
```

### 2. 密钥配置说明

#### 获取密钥
1. 登录工商银行开放平台：https://open.icbc.com.cn
2. 在"我的应用"中查看应用详情
3. 下载或配置RSA密钥对
4. 获取工商银行网关公钥

#### 密钥格式
- **私钥**: PKCS#8格式，用于请求签名
- **网关公钥**: 用于验证工商银行响应签名

## 📊 支付参数说明

### 必填参数

| 参数名 | 类型 | 长度 | 说明 |
|--------|------|------|------|
| mer_id | String | 15 | 商户编号 |
| mer_prtcl_no | String | 32 | 收单产品协议编号 |
| device_info | String | 32 | 设备号 |
| out_trade_no | String | 64 | 商户订单号 |
| orig_date_time | String | 19 | 交易日期时间 (yyyy-MM-dd'T'HH:mm:ss) |
| fee_type | String | 3 | 交易币种 (001-人民币) |
| spbill_create_ip | String | 15 | 终端IP |
| total_fee | String | 12 | 订单总金额（分） |
| mer_url | String | 256 | 支付成功回调URL |
| body | String | 128 | 订单描述 |
| access_type | String | 2 | 收单接入方式 |
| pay_mode | String | 2 | 支付方式 |
| icbc_appid | String | 32 | 工商银行应用ID |

### 支付方式配置

#### 支付方式 (pay_mode)
- `9`: 微信支付
- `10`: 支付宝支付

#### 接入方式 (access_type)
- `5`: APP
- `7`: 微信公众号
- `8`: 支付宝生活号
- `9`: 微信小程序

### 微信支付额外参数

| 参数名 | 说明 | 必填 |
|--------|------|------|
| shop_appid | 商户在微信开放平台注册的APPID | 是 |
| open_id | 用户在商户APPID下的唯一标识 | 小程序/公众号必填 |

### 支付宝支付额外参数

| 参数名 | 说明 | 必填 |
|--------|------|------|
| shop_appid | 商户在支付宝的APPID | 是 |
| union_id | 用户在支付宝的唯一标识 | 生活号必填 |

## 🔐 签名算法

### 签名生成步骤

1. **参数排序**: 按参数名ASCII码升序排列
2. **构建签名串**: `key1=value1&key2=value2&...`
3. **RSA2签名**: 使用SHA256withRSA算法
4. **Base64编码**: 对签名结果进行Base64编码

### 签名示例代码

```php
private function generateSign(array $params)
{
    // 移除sign参数
    unset($params['sign']);
    
    // 参数排序
    ksort($params);
    
    // 构建签名字符串
    $signString = '';
    foreach ($params as $key => $value) {
        if ($value !== '' && $value !== null) {
            $signString .= $key . '=' . $value . '&';
        }
    }
    $signString = rtrim($signString, '&');
    
    // RSA2签名
    $privateKey = openssl_pkey_get_private($this->config['private_key']);
    openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256);
    
    return base64_encode($signature);
}
```

## 📡 API调用流程

### 1. 构建请求参数

```php
$bizContent = [
    'mer_id' => '****************',
    'mer_prtcl_no' => '*****************',
    'device_info' => 'PARKING_SYSTEM_001',
    'out_trade_no' => 'PARK_20250523141818723448925',
    'orig_date_time' => '2025-05-23T14:18:18',
    'fee_type' => '001',
    'spbill_create_ip' => '*************',
    'total_fee' => '100', // 1元 = 100分
    'mer_url' => 'https://your-domain.com/icbc-pay/notify',
    'body' => '停车费支付',
    'access_type' => '9', // 微信小程序
    'pay_mode' => '9',    // 微信支付
    'shop_appid' => 'wx8888888888888888',
    'open_id' => 'oUpF8uMuAJO_M2pxb1Q9zNjWeS6o',
    'icbc_appid' => '10000000000000052474',
    'expire_time' => '30',
    'notify_type' => 'HS',
    'result_type' => '0'
];

$requestParams = [
    'app_id' => '10000000000000052474',
    'charset' => 'UTF-8',
    'format' => 'json',
    'sign_type' => 'RSA2',
    'timestamp' => '2025-05-23 14:18:18',
    'version' => 'V1',
    'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE)
];

$requestParams['sign'] = generateSign($requestParams);
```

### 2. 发送HTTP请求

```php
$url = 'https://gw.open.icbc.com.cn/api/cardbusiness/aggregatepay/b2c/online/consumepurchase/V1';

$response = Http::timeout(30)
    ->withHeaders([
        'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
        'User-Agent' => 'ICBC-PAY-SDK-PHP/1.0.0'
    ])
    ->asForm()
    ->post($url, $requestParams);
```

### 3. 处理响应

```php
$result = $response->json();

if ($result['return_code'] == 0) {
    // 支付成功，提取支付参数
    $wxParams = [
        'appId' => $result['shop_appid'],
        'timeStamp' => (string)time(),
        'nonceStr' => $result['nonce_str'],
        'package' => $result['wx_data_package'],
        'signType' => 'MD5',
        'paySign' => $result['wx_pay_sign']
    ];
    
    // 调用微信支付
    // ...
} else {
    throw new Exception($result['return_msg']);
}
```

## 🔄 支付回调处理

### 回调验签

```php
public function handleNotify(Request $request)
{
    $params = $request->all();
    
    // 验证签名
    if (!$this->verifyNotifySign($params)) {
        return response()->json([
            'return_code' => -1,
            'return_msg' => 'sign verify failed'
        ]);
    }
    
    $bizContent = json_decode($params['biz_content'], true);
    $returnCode = $bizContent['return_code'];
    $outTradeNo = $bizContent['out_trade_no'];
    $thirdTradeNo = $bizContent['third_trade_no'] ?? '';
    
    // 更新订单状态
    if ($returnCode === '0') {
        // 支付成功
        $this->updatePaymentStatus($outTradeNo, 'success', $thirdTradeNo);
    } else {
        // 支付失败
        $this->updatePaymentStatus($outTradeNo, 'failed');
    }
    
    // 返回成功响应
    return response()->json([
        'return_code' => 0,
        'return_msg' => 'success',
        'msg_id' => $bizContent['msg_id']
    ]);
}
```

## 🎯 使用示例

### 创建停车费支付订单

```php
// 1. 创建支付记录
$paymentRecord = PaymentRecord::create([
    'out_trade_no' => PaymentRecord::generateUniqueOrderNo(),
    'total_amount' => 1.00,
    'subject' => '停车费支付',
    'payment_method' => 'wechat',
    'car_number' => '新M16800',
    'parking_duration' => 60,
    'status' => 'pending'
]);

// 2. 调用支付服务
$icbcPayService = new IcbcPayService();
$result = $icbcPayService->createPayment($paymentRecord);

// 3. 处理支付结果
if ($result['success']) {
    // 跳转到支付页面
    return redirect("/pay/{$result['out_trade_no']}");
} else {
    // 处理错误
    return back()->withErrors('支付订单创建失败');
}
```

### 前端调用微信支付

```javascript
// 微信支付调用
function callWechatPay(payParams) {
    if (typeof WeixinJSBridge !== "undefined") {
        WeixinJSBridge.invoke("getBrandWCPayRequest", {
            "appId": payParams.appId,
            "timeStamp": payParams.timeStamp,
            "nonceStr": payParams.nonceStr,
            "package": payParams.package,
            "signType": payParams.signType,
            "paySign": payParams.paySign
        }, function(res) {
            if (res.err_msg == "get_brand_wcpay_request:ok") {
                // 支付成功
                window.location.href = "/payment/result/" + orderNo + "/success";
            } else {
                // 支付失败
                window.location.href = "/payment/result/" + orderNo + "/failed";
            }
        });
    } else {
        alert("请在微信中打开此页面");
    }
}
```

## ⚠️ 重要注意事项

### 1. 环境要求
- PHP >= 8.0
- Laravel >= 9.0
- OpenSSL扩展
- 支持HTTPS的服务器

### 2. 安全配置
- 私钥文件权限设置为600
- 回调URL必须使用HTTPS
- 定期更新密钥对

### 3. 测试环境
- 使用工商银行提供的沙箱环境
- 沙箱环境网关地址：https://gw.open.icbc.com.cn/api
- 生产环境网关地址：https://gw.open.icbc.com.cn/api

### 4. 常见错误
- **签名错误**: 检查私钥格式和签名算法
- **商户号错误**: 确认商户编号和协议编号
- **参数错误**: 检查必填参数和参数格式
- **网络错误**: 确认网关地址和网络连接

### 5. 性能优化
- 使用连接池减少HTTP连接开销
- 实现请求重试机制
- 添加详细的日志记录

## 📞 技术支持

- **工商银行开放平台**: https://open.icbc.com.cn
- **技术文档**: https://open.icbc.com.cn/icbc/apip/docs_index.html
- **客服电话**: 95588
- **开发者QQ群**: [具体群号请咨询工商银行]

## 🔄 更新日志

- **2025-05-23**: 创建初始版本，支持微信和支付宝支付
- **待更新**: 添加退款接口支持
- **待更新**: 添加批量查询接口

---

本文档会根据工商银行API的更新持续维护，如有问题请及时反馈。 