# 工商银行聚合支付模式说明

## 📋 支付模式概述

工商银行聚合支付系统支持两种支付模式：

### 1. UI界面模式（推荐）
- **接口路径**: `/ui/cardbusiness/aggregatepay/b2c/online/ui/consumepurchaseshowpay/V1`
- **特点**: 用户跳转到工商银行官方支付页面完成支付
- **优势**: 界面美观、安全性高、用户信任度高
- **适用场景**: Web端支付、移动端H5支付

### 2. 无界面模式
- **接口路径**: `/cardbusiness/aggregatepay/b2c/online/consumepurchase/V1`
- **特点**: 返回支付参数，在商户页面调用支付SDK
- **优势**: 用户体验流畅、界面可定制
- **适用场景**: APP内支付、小程序支付

## 🔧 配置方法

### 环境变量配置

在 `.env` 文件中添加：

```bash
# 工商银行聚合支付配置
ICBC_APP_ID=10000000000000052474
ICBC_MER_ID=****************
ICBC_MER_PRTCL_NO=*****************

# 网关配置
ICBC_GATEWAY_URL=https://gw.open.icbc.com.cn/api
ICBC_SIGN_TYPE=RSA2
ICBC_CHARSET=UTF-8
ICBC_FORMAT=json
ICBC_VERSION=V1

# 支付模式选择（true=使用工银UI界面, false=无界面模式）
ICBC_USE_UI_MODE=true

# 密钥配置
ICBC_PRIVATE_KEY_PATH=storage/keys/icbc_private_key.pem
ICBC_APIGW_PUBLIC_KEY_PATH=storage/keys/icbc_apigw_public_key.pem

# 回调配置
ICBC_NOTIFY_URL=https://your-domain.com/icbc-pay/notify
ICBC_RETURN_URL=https://your-domain.com/icbc-pay/return
ICBC_DEVICE_INFO=PARKING_SYSTEM_001

# 微信配置
ICBC_WECHAT_SHOP_APPID=wx8888888888888888
ICBC_WECHAT_SHOP_SECRET=your_wechat_shop_secret

# 支付宝配置
ICBC_ALIPAY_SHOP_APPID=2021000000000000
```

## 🎯 使用方法

### UI界面模式

#### 1. 配置启用UI模式
```bash
ICBC_USE_UI_MODE=true
```

#### 2. 创建支付订单
```php
$paymentRecord = PaymentRecord::create([
    'out_trade_no' => PaymentRecord::generateUniqueOrderNo(),
    'total_amount' => 10.00,
    'subject' => '停车费支付',
    'payment_method' => 'wechat',
    'car_number' => '新M16800',
    'status' => 'pending'
]);

$icbcPayService = new IcbcPayService();
$result = $icbcPayService->createPayment($paymentRecord);
```

#### 3. 处理返回结果
```php
if ($result['success']) {
    // UI模式返回跳转URL
    $paymentUrl = $result['payment_url'];
    $paymentForm = $result['payment_form']; // 包含自动跳转的表单
    
    // 直接跳转或显示跳转页面
    return redirect($paymentUrl);
    // 或者返回包含表单的视图
    return view('payment.redirect', compact('paymentForm'));
}
```

#### 4. 支付流程
```mermaid
graph TD
    A[用户提交支付] --> B[系统创建订单]
    B --> C[跳转到工商银行页面]
    C --> D[用户在银行页面支付]
    D --> E[支付完成]
    E --> F[银行异步通知商户]
    E --> G[用户跳转回商户页面]
    F --> H[更新订单状态]
    G --> I[显示支付结果]
```

### 无界面模式

#### 1. 配置启用无界面模式
```bash
ICBC_USE_UI_MODE=false
```

#### 2. 创建支付订单
```php
$paymentRecord = PaymentRecord::create([
    'out_trade_no' => PaymentRecord::generateUniqueOrderNo(),
    'total_amount' => 10.00,
    'subject' => '停车费支付',
    'payment_method' => 'wechat',
    'car_number' => '新M16800',
    'status' => 'pending'
]);

$icbcPayService = new IcbcPayService();
$result = $icbcPayService->createPayment($paymentRecord);
```

#### 3. 处理返回结果
```php
if ($result['success']) {
    // 无界面模式返回支付参数
    $paymentParams = $result['payment_params'];
    
    if ($paymentRecord->payment_method === 'wechat') {
        // 微信支付参数
        $wxParams = [
            'appId' => $paymentParams['appId'],
            'timeStamp' => $paymentParams['timeStamp'],
            'nonceStr' => $paymentParams['nonceStr'],
            'package' => $paymentParams['package'],
            'signType' => $paymentParams['signType'],
            'paySign' => $paymentParams['paySign']
        ];
        
        // 调用微信支付JS SDK
        return view('payment.wechat', compact('wxParams'));
    }
}
```

#### 4. 前端调用支付SDK
```javascript
// 微信支付
if (typeof WeixinJSBridge !== "undefined") {
    WeixinJSBridge.invoke("getBrandWCPayRequest", {
        "appId": wxParams.appId,
        "timeStamp": wxParams.timeStamp,
        "nonceStr": wxParams.nonceStr,
        "package": wxParams.package,
        "signType": wxParams.signType,
        "paySign": wxParams.paySign
    }, function(res) {
        if (res.err_msg == "get_brand_wcpay_request:ok") {
            // 支付成功
            window.location.href = "/payment/result/" + orderNo + "/success";
        } else {
            // 支付失败
            window.location.href = "/payment/result/" + orderNo + "/failed";
        }
    });
}
```

## 📊 两种模式对比

| 特性 | UI界面模式 | 无界面模式 |
|------|------------|------------|
| 用户体验 | 跳转到银行页面 | 在商户页面完成 |
| 安全性 | 银行级安全保障 | 依赖商户实现 |
| 定制化 | 银行统一界面 | 可完全自定义 |
| 开发复杂度 | 简单，只需跳转 | 复杂，需要SDK集成 |
| 移动端兼容 | 完美兼容 | 需要特殊处理 |
| 支付成功率 | 高（用户信任度高） | 中等 |
| 实现难度 | ⭐⭐ | ⭐⭐⭐⭐ |

## 🎯 建议使用场景

### UI界面模式适用于：
- ✅ Web端网页支付
- ✅ 移动端H5支付
- ✅ 微信公众号支付
- ✅ 支付宝生活号支付
- ✅ 对安全性要求高的场景
- ✅ 快速集成需求

### 无界面模式适用于：
- ✅ 原生APP内支付
- ✅ 微信小程序支付
- ✅ 需要完全自定义UI的场景
- ✅ 需要无感知支付的场景

## 🔄 模式切换

系统支持动态切换支付模式，只需修改环境变量：

```bash
# 切换到UI模式
ICBC_USE_UI_MODE=true

# 切换到无界面模式
ICBC_USE_UI_MODE=false
```

修改后重启应用即可生效。

## 📝 注意事项

1. **回调URL配置**: 两种模式都需要正确配置回调URL
2. **签名算法**: 两种模式使用相同的RSA2签名算法
3. **参数格式**: UI模式参数通过URL传递，无界面模式通过POST传递
4. **错误处理**: UI模式错误在银行页面显示，无界面模式需要商户处理
5. **测试环境**: 两种模式都支持模拟支付功能

## 🔍 常见问题

### Q: 如何选择合适的支付模式？
A: 对于大多数Web应用，推荐使用UI界面模式，因为它安全、简单、用户信任度高。只有在需要完全自定义支付界面或APP内集成时才使用无界面模式。

### Q: 可以同时使用两种模式吗？
A: 系统当前设计为全局配置，建议在一个项目中统一使用一种模式。如有特殊需求，可以通过代码调用不同的方法实现。

### Q: UI模式的支付页面可以定制吗？
A: 工商银行的UI界面不支持定制，但会根据支付方式（微信/支付宝）显示相应的界面风格。

### Q: 两种模式的回调处理有区别吗？
A: 回调处理逻辑完全相同，都是通过异步通知更新订单状态。 