# 工商银行UI界面支付模式演示

## 🎯 快速开始

### 1. 配置环境变量

```bash
# 启用UI界面模式
ICBC_USE_UI_MODE=true

# 基本配置
ICBC_APP_ID=你的应用ID
ICBC_MER_ID=你的商户ID
ICBC_MER_PRTCL_NO=你的协议号
ICBC_GATEWAY_URL=https://gw.open.icbc.com.cn/api

# 密钥配置
ICBC_PRIVATE_KEY_PATH=storage/keys/icbc_private_key.pem
ICBC_APIGW_PUBLIC_KEY_PATH=storage/keys/icbc_apigw_public_key.pem

# 回调地址
ICBC_NOTIFY_URL=https://yourdomain.com/icbc-pay/notify
ICBC_RETURN_URL=https://yourdomain.com/icbc-pay/return
```

### 2. 控制器中创建支付

```php
<?php

namespace App\Http\Controllers;

use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

class PaymentController extends Controller
{
    public function createPayment(Request $request)
    {
        // 创建支付记录
        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => PaymentRecord::generateUniqueOrderNo(),
            'total_amount' => $request->amount,
            'subject' => '停车费支付',
            'payment_method' => $request->payment_method, // 'wechat' 或 'alipay'
            'car_number' => $request->car_number,
            'parking_duration' => $request->duration,
            'status' => 'pending'
        ]);

        // 调用支付服务
        $icbcPayService = new IcbcPayService();
        $result = $icbcPayService->createPayment($paymentRecord);

        if ($result['success']) {
            // UI模式：直接跳转到工商银行支付页面
            if (isset($result['payment_url'])) {
                return redirect($result['payment_url']);
            }
            
            // 或者显示包含自动跳转的页面
            return view('payment.redirect', [
                'paymentForm' => $result['payment_form'],
                'orderInfo' => $paymentRecord
            ]);
        }

        return back()->withErrors('支付创建失败');
    }
}
```

### 3. 支付跳转页面视图

创建 `resources/views/payment/redirect.blade.php`:

```php
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在跳转到支付页面...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .order-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>正在跳转到工商银行支付页面</h2>
        <p>请稍候，系统正在为您准备安全的支付环境...</p>
        
        <div class="order-info">
            <p><strong>订单号:</strong> {{ $orderInfo->out_trade_no }}</p>
            <p><strong>车牌号:</strong> {{ $orderInfo->car_number }}</p>
            <p><strong>支付金额:</strong> ¥{{ number_format($orderInfo->total_amount, 2) }}</p>
            <p><strong>支付方式:</strong> {{ $orderInfo->payment_method === 'wechat' ? '微信支付' : '支付宝支付' }}</p>
        </div>
        
        <p><small>如果页面没有自动跳转，请点击下方按钮</small></p>
        <button onclick="submitPayment()" style="
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        ">立即支付</button>
    </div>

    <!-- 支付表单（自动提交） -->
    {!! $paymentForm !!}

    <script>
        // 备用手动提交函数
        function submitPayment() {
            document.getElementById('icbcUIPayForm').submit();
        }
        
        // 5秒后如果还没跳转，显示提示
        setTimeout(function() {
            alert('页面跳转时间较长，请点击"立即支付"按钮手动跳转');
        }, 5000);
    </script>
</body>
</html>
```

### 4. 支付成功处理

当用户在工商银行页面完成支付后，系统会：

1. **异步通知**: 工商银行向 `/icbc-pay/notify` 发送支付结果
2. **同步跳转**: 用户浏览器跳转到 `/icbc-pay/return`

```php
// app/Http/Controllers/IcbcNotifyController.php 已经处理了异步通知

// 支付完成后的页面跳转处理
public function handleReturn(Request $request)
{
    $outTradeNo = $request->get('out_trade_no');
    
    if ($outTradeNo) {
        $paymentRecord = PaymentRecord::where('out_trade_no', $outTradeNo)->first();
        if ($paymentRecord) {
            return redirect()->route('parking.result', [
                'outTradeNo' => $outTradeNo,
                'status' => $paymentRecord->status
            ]);
        }
    }
    
    return redirect()->route('parking.index')->with('error', '订单查询失败');
}
```

### 5. 支付结果页面

```php
// 在 ParkingController 中
public function showPaymentResult($outTradeNo, $status = null)
{
    $paymentRecord = PaymentRecord::where('out_trade_no', $outTradeNo)->first();
    
    if (!$paymentRecord) {
        abort(404, '订单不存在');
    }
    
    return view('parking.result', [
        'payment' => $paymentRecord,
        'status' => $paymentRecord->status
    ]);
}
```

## 🎨 UI模式的优势

### 1. 安全性更高
- 用户在工商银行官方页面输入支付信息
- 商户无需处理敏感的支付数据
- 银行级别的安全保障

### 2. 用户信任度高
- 显示工商银行的官方界面
- 用户更容易信任和完成支付
- 减少支付流程中的顾虑

### 3. 开发简单
- 无需集成复杂的前端SDK
- 只需处理页面跳转
- 降低技术实现难度

### 4. 维护成本低
- 界面更新由银行维护
- 减少兼容性问题
- 更稳定的支付体验

## 📱 移动端优化

UI模式在移动端表现优秀：

```css
/* 移动端样式优化 */
@media (max-width: 768px) {
    .container {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .order-info {
        font-size: 14px;
    }
    
    button {
        width: 100%;
        padding: 15px;
        font-size: 18px;
    }
}
```

## 🔍 调试技巧

### 查看生成的支付URL

```php
// 在开发环境中，可以记录支付URL用于调试
Log::info('ICBC Payment URL Generated', [
    'url' => $result['payment_url'],
    'order_no' => $paymentRecord->out_trade_no
]);
```

### 测试环境配置

```bash
# 测试环境使用模拟支付
ICBC_USE_UI_MODE=true
APP_ENV=testing  # 这将启用模拟支付功能
```

## 📈 性能监控

```php
// 记录支付跳转时间
$startTime = microtime(true);

$result = $icbcPayService->createPayment($paymentRecord);

$endTime = microtime(true);
$duration = ($endTime - $startTime) * 1000; // 毫秒

Log::info('Payment Creation Performance', [
    'duration_ms' => $duration,
    'order_no' => $paymentRecord->out_trade_no
]);
```

## 🎯 最佳实践

1. **超时处理**: 设置合理的页面跳转超时时间
2. **错误处理**: 提供用户友好的错误提示
3. **日志记录**: 记录关键操作用于问题排查
4. **状态检查**: 跳转前验证订单状态
5. **安全防护**: 验证请求来源和参数完整性

---

通过以上配置，您就可以在项目中使用工商银行的UI界面支付模式，为用户提供安全、可靠的支付体验！ 