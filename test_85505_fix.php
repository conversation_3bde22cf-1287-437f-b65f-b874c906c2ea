<?php

/**
 * 测试85505错误修复脚本
 * 
 * 验证网关公钥更新后是否解决了85505错误
 */

require_once 'vendor/autoload.php';

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🧪 工商银行85505错误修复验证\n";
    echo "===============================\n\n";
    
    // 1. 检查网关公钥配置
    echo "1️⃣ 检查网关公钥配置...\n";
    
    $config = config('icbc-pay');
    $apigwKeyPath = $config['apigw_public_key_path'] ?? null;
    
    if (!$apigwKeyPath) {
        throw new Exception("网关公钥路径未配置");
    }
    
    if (!file_exists($apigwKeyPath)) {
        throw new Exception("网关公钥文件不存在: {$apigwKeyPath}");
    }
    
    $keyContent = file_get_contents($apigwKeyPath);
    $keyResource = openssl_pkey_get_public($keyContent);
    
    if (!$keyResource) {
        throw new Exception("网关公钥格式无效");
    }
    
    $keyDetails = openssl_pkey_get_details($keyResource);
    openssl_pkey_free($keyResource);
    
    echo "✅ 网关公钥配置正确\n";
    echo "   路径: {$apigwKeyPath}\n";
    echo "   大小: " . filesize($apigwKeyPath) . " 字节\n";
    echo "   类型: " . ($keyDetails['type'] == OPENSSL_KEYTYPE_RSA ? 'RSA' : '其他') . "\n";
    echo "   长度: {$keyDetails['bits']} 位\n\n";
    
    // 2. 检查其他必要配置
    echo "2️⃣ 检查其他必要配置...\n";
    
    $requiredConfigs = [
        'app_id' => 'APP ID',
        'mer_id' => '商户号',
        'mer_prtcl_no' => '商户协议号',
        'private_key_path' => '私钥路径'
    ];
    
    foreach ($requiredConfigs as $key => $name) {
        $value = $config[$key] ?? null;
        if (empty($value)) {
            echo "⚠️ {$name} 未配置\n";
        } else {
            if ($key === 'private_key_path') {
                echo "✅ {$name}: " . (file_exists($value) ? '文件存在' : '文件不存在') . "\n";
            } else {
                echo "✅ {$name}: " . substr($value, 0, 10) . "...\n";
            }
        }
    }
    echo "\n";
    
    // 3. 测试签名验证功能
    echo "3️⃣ 测试签名验证功能...\n";
    
    // 模拟工商银行返回的签名数据
    $testData = [
        'return_code' => '0',
        'return_msg' => 'success',
        'msg_id' => 'TEST_' . date('YmdHis') . mt_rand(1000, 9999)
    ];
    
    // 创建测试签名字符串
    $signString = '';
    foreach ($testData as $key => $value) {
        if (!empty($value)) {
            $signString .= $key . '=' . $value . '&';
        }
    }
    $signString = rtrim($signString, '&');
    
    echo "✅ 签名字符串构建成功\n";
    echo "   内容: {$signString}\n\n";
    
    // 4. 创建支付客户端并测试
    echo "4️⃣ 创建支付客户端...\n";
    
    $icbcClient = app(\IcbcPay\IcbcPayClient::class);
    echo "✅ 支付客户端创建成功\n\n";
    
    // 5. 测试支付订单创建（不实际提交）
    echo "5️⃣ 测试支付订单创建...\n";
    
    $testOrderData = [
        'out_trade_no' => 'TEST_85505_' . date('YmdHis') . mt_rand(1000, 9999),
        'total_amount' => '0.01',
        'subject' => '85505错误修复测试',
        'body' => '测试网关公钥是否正确',
        'payment_method' => 'wechat'
    ];
    
    try {
        // 这里只测试签名生成，不实际发送请求
        $reflection = new ReflectionClass($icbcClient);
        $method = $reflection->getMethod('generateSignature');
        $method->setAccessible(true);
        
        $signature = $method->invoke($icbcClient, $testOrderData);
        
        echo "✅ 签名生成成功\n";
        echo "   签名长度: " . strlen($signature) . " 字符\n";
        echo "   签名预览: " . substr($signature, 0, 50) . "...\n\n";
        
    } catch (Exception $e) {
        echo "❌ 签名生成失败: " . $e->getMessage() . "\n\n";
    }
    
    // 6. 检查错误码映射
    echo "6️⃣ 检查错误码映射...\n";
    
    $errorCodes = $config['error_codes'] ?? [];
    
    // 添加85505错误码到映射中（如果不存在）
    if (!isset($errorCodes['85505'])) {
        echo "⚠️ 85505错误码未在映射中定义\n";
        echo "   建议添加: '85505' => '网关公钥验证失败'\n";
    } else {
        echo "✅ 85505错误码已定义: " . $errorCodes['85505'] . "\n";
    }
    echo "\n";
    
    // 7. 总结和建议
    echo "📋 修复验证总结\n";
    echo "================\n\n";
    
    echo "✅ 已完成的修复：\n";
    echo "   - 更新了工商银行网关公钥\n";
    echo "   - 配置了正确的公钥路径\n";
    echo "   - 验证了公钥格式和有效性\n";
    echo "   - 测试了签名生成功能\n\n";
    
    echo "🧪 下一步测试建议：\n";
    echo "   1. 在实际支付页面测试支付功能\n";
    echo "   2. 观察是否还出现85505错误\n";
    echo "   3. 检查工商银行返回的错误信息\n";
    echo "   4. 如果仍有问题，联系工商银行技术支持\n\n";
    
    echo "📞 如果85505错误仍然存在：\n";
    echo "   可能的原因：\n";
    echo "   - 工商银行更新了网关公钥（需要获取最新版本）\n";
    echo "   - 您的商户配置与网关公钥不匹配\n";
    echo "   - 环境配置问题（沙箱vs生产环境）\n";
    echo "   - 其他网络或配置问题\n\n";
    
    echo "   解决方案：\n";
    echo "   1. 联系工商银行技术支持: 95588\n";
    echo "   2. 提供您的商户号: " . ($config['mer_id'] ?? '未配置') . "\n";
    echo "   3. 提供您的APP_ID: " . ($config['app_id'] ?? '未配置') . "\n";
    echo "   4. 说明85505错误和网关公钥问题\n";
    echo "   5. 请求最新的官方网关公钥\n\n";
    
    echo "🎉 85505错误修复验证完成！\n";
    echo "现在可以测试实际的支付功能了。\n";
    
} catch (Exception $e) {
    echo "❌ 验证过程中出现错误: " . $e->getMessage() . "\n";
    echo "请检查配置并重试\n";
    exit(1);
}
