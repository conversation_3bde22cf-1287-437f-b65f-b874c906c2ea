<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('icbc_payment_records')) {
            Schema::create('icbc_payment_records', function (Blueprint $table) {
                $table->id();
                $table->string('out_trade_no')->unique()->comment('商户订单号');
                $table->string('trade_no')->nullable()->comment('工行交易号');
                $table->decimal('total_amount', 10, 2)->comment('支付金额');
                $table->string('subject')->comment('订单标题');
                $table->string('payment_method')->comment('支付方式：alipay,wechat');
                $table->string('car_number')->comment('车牌号');
                $table->integer('parking_duration')->default(0)->comment('停车时长(分钟)');
                $table->enum('status', ['pending', 'success', 'failed', 'closed'])->default('pending')->comment('支付状态');
                $table->timestamp('paid_at')->nullable()->comment('支付时间');
                $table->json('notify_params')->nullable()->comment('回调参数');
                $table->timestamps();
                
                $table->index(['status', 'created_at']);
                $table->index('car_number');
                $table->index('out_trade_no');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('icbc_payment_records');
    }
};
