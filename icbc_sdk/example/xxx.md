```mermaid
sequenceDiagram
    participant C as 车辆
    participant P as 出口摄像头
    participant R as Redis缓存
    participant S as 停车场系统
    participant Pay as 支付系统
    
    C->>P: 车辆到达出口
    P->>S: 识别车牌号码
    S->>S: 计算停车费用
    S->>R: 存储(车牌号+费用)<br/>key=车道号<br/>expire=5分钟
    
    Note over S,Pay: 后端自动完成支付处理
    
    S->>Pay: 调用支付接口
    Pay-->>S: 返回支付结果
    
    alt 支付成功
        S->>S: 更新支付记录
        S->>C: 抬杆放行
        S->>R: 删除缓存数据
    else 支付失败
        S->>S: 记录失败日志
        S->>S: 触发异常处理
    end
```