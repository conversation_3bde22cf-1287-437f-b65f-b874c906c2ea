<?php
    include_once '../IcbcConstants.php';
    include_once '../UiIcbcClient.php';


    $request = array(
        "serviceUrl" => 'http://ip:port/ui/cardbussiness/zfbh5/ui/h5consumption/V2',
        "method" => 'POST',
        "isNeedEncrypt" => false,
        "biz_content" => array(
            "mer_id"=>"020001040311",
            "mer_prtcl_no"=>"0200010403110201",
            "order_id"=>"AAPPT20051202205754714",
            "decive_info"=>"9774d56d682e549c",
            "body"=>"勇士总冠军",
            "cur_type"=>"1",
            "icbc_appid"=>"10000000000004095503",
            "mer_url"=>"http://5.1fendb.com/recall/recall.php",
            "order_date_time"=>"2021-09-12T02:20:51",
            "amount"=>"1",
            "notify_type"=>"HS"
            "notify_url"=>"http://*************",
            "result_type"=>"0",
    	    "pay_limit"=>"no_balance",
    	    "order_apd_inf"=>"test",
    	    "detail"=>"",
    	    "attach"=>"attach",
      	    "ser_id"=>"ser_id",
      	    "is_suborder"=>"1",
      	    "order_num"=>"1",
            "order_submit_act_input"=>
                array(
                "seq_no"=>"020001030604000521912200000001",
                "mer_sp_infor"=>"商户自定义信息",
                "sub_mer_prtcl_no"=>"0200010305560201",
                "ori_trx_date"=>"2021-10-11",
                "classify_amt"=>"1",
                "term_id"=>"1",
                "busi_type"=>"2",
                "sub_order_no"=>"55555",
                "rec_num"=>"1",
                "mer_acct"=>"0200000209024213154",
                "ori_mer_id"=>"20001030604",
                "oper_flag"=>"0",
                "sub_mer_id"=>"020001030556"
                )
    
        )

    );

    $client = new UiIcbcClient('10000000000004095503',
        'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDYLo0htdHwedbvoJA0o5AA/UUE1zb/J4OO9SYmeMBZnLeiNC7TwG53PXgwCSHsp2gM02QatfohUUDHQJg/ezYCSDSMoWfYKv/dfz0DlXp1C8xOnc/im2y0PnZV1YVaTsFHnax91N7nxtfsGVRlT8e2UdkWlW0HX2QOxCBpuevDssfosA151f4JV07VHl+pyHxTS48wGeqDUU0z+hvD8P+KIA01/UOQqXdx47I9Zm5Rm7nfBq1tAxkG6S32D5fsFF2/N+Pa1fTZ//mHFVnn6hbu1c45EFRSBo0O1G+/qq7qTy5+tq5R0Vskgm6FzHJaX2ffQ2tmBwfm/OnZmxKaHmhFAgMBAAECggEAUTE6pFyLWswH47vkLUD5BsYYs/a4myAWEw0TpQNZCs8HUQ5UQAdX9cTKbRAhA6bkN1z1jeqm5PiFYdBq3fzCjhzcT60XOWL88g2ltsDfWzJxK12uBCfnrdJ/00D8cqx9fw5DCId4qIhP9EaXIe3SzjDzXb4FUu2KwNj8a3j+iWLc4rxMo84CRieXLrSIaL6pgE6wxm/G7lcfL2IicrcJGBQEj65Ivyj9UBAmu3TiOFWhQDgZYnAHjIi62MWmoY2hArSDB2BEc4Ul0q4AFj5bK5B7+MAg2bKcNZyKXTFvI5euMTIlqGoFR58oFwI0fh3BLk9ABVgeM+AAPw7hWjk6AQKBgQD+B9RXX7TjjcYx45fc/YB+FsbdFAbdU5q04aLtFpWrkyu9ZQwIlgpDVFuxJ5apgXjjHTsrw6n3oxbuls+Lp3gVMVnWNgtHW/AyqdTnchHjaJ3FTMpPL32suBLSGSHfc8MQmkD1CDZAEoo/HSWxEMhkrZdud1EDEiJPkFeXo1/9hQKBgQDZ25qhh1QmETY9WT42/CrZHfWdMVkodJsvGqRgkgIya+22DpvmSEvT2349fvHYL+Kr7dEl0ZrCHV1knIEkF/ftcQq8qnpNxIT05igPZxIzdnSHjDEeePgZ5Ldsaruvxa/wSYInbIN6knQpxRwJhs4fQQ9qB0aXetMHoH3yubpbwQKBgQDab50VrMR8z5JXn98cNhfhVHCX9fqZIqTrWQKiYEM/EAQSjes1Dt+wWb+mq+YPx7dNg/s3fL6QpHq5mpDJA65ses1HN52nNNVsm0Dp69qZ84GHAKsOQEYO2RHF+7p2zLI1eo7UpMURf1/FOakJgubuO571bEALJfK++912FRQbiQKBgEw15FwJSUif0MeZRohZomudbWR19OiQEhFiUoptyVL2Kov7hFIEjHIqYHkGxXeJGNRoNzfxoR+ywg8GKK8Fq3dmigkB4hL+YjyBnxX0SouyLoWUZ0Jvsurr9bZ5h/qvPyLhtCQyc7QXM4fBKlOy0rxgOBIxRWKMVvJeFHEqw6SBAoGAPuR/+v2VCePORhobY0EIIRLK1sO+Q2IVJIU4ecm0irD99/uwEc+RgJF0IakYlrw1PtmhDYniXO07GMIecj4zsnnPZtkttoNxmikMrK51YsFcsz8I55XzRPPGj1V8CxpfRxmEj9ofyHYHDDvSoHAR28xJdubbn3PuB7nBp94yNWk=',
        IcbcConstants::$SIGN_TYPE_RSA,
        '',
        '',
        '',
        '',
        '',
        '',
        '');

    try{
        $resp = $client->buildPostForm($request,'12314adafadsa',''); //执行调用
        echo $resp;
    }catch(Exception $e){//捕获异常
        echo 'Exception:'.$e->getMessage()."\n";
    }

?>