<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\ParkingController;


Route::post('/pay', [ParkingController::class, 'createPayment'])
    ->name('pay.create');

Route::post('/pay/redirect', [ParkingController::class, 'paymentRedirect'])
    ->name('pay.redirect');

Route::get('/pay/{outTradeNo}', [ParkingController::class, 'payByOrderNo'])
    ->name('pay.by_order');

Route::get('/pay/query/{outTradeNo}', [ParkingController::class, 'queryPaymentStatus'])
    ->name('pay.query');

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');