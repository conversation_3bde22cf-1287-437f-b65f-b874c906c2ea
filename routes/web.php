<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\ParkingController;
use App\Http\Controllers\IcbcNotifyController;

// 停车费支付相关路由
Route::get('/parking', [ParkingController::class, 'index'])
    ->name('parking.index');

Route::get('/pay/{orderNo}', [ParkingController::class, 'showPaymentPage'])
    ->name('parking.payment');

Route::get('/payment/result/{orderNo}', [ParkingController::class, 'showPaymentResult'])
    ->name('parking.result');

// 工商银行支付回调路由
Route::post('/icbc-pay/notify', [ParkingController::class, 'handlePaymentCallback'])
    ->name('icbc.notify');

Route::get('/icbc-pay/return', [ParkingController::class, 'handlePaymentReturn'])
    ->name('icbc.return');

// 降级支付页面路由
Route::get('/payment/fallback/{orderNo}', [ParkingController::class, 'showFallbackPayment'])
    ->name('parking.fallback');

// 调试路由（仅在开发环境）
if (app()->environment(['local', 'development', 'testing'])) {
    Route::get('/debug/icbc-payment', [App\Http\Controllers\DebugController::class, 'debugIcbcPayment'])
        ->name('debug.icbc.payment');
    
    Route::get('/debug/network-test', [App\Http\Controllers\DebugController::class, 'testNetworkConnection'])
        ->name('debug.network.test');
        
    Route::get('/debug/network-page', function () {
        return view('debug.network-test');
    })->name('debug.network.page');
}

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
