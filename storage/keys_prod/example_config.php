<?php

/**
 * 工商银行支付测试配置示例
 * 
 * 复制此文件到 config/icbc-pay.php 并修改相应参数
 */

return [
    // 测试环境配置
    'environment' => 'sandbox',
    'app_id' => 'myapp01234567890123456789012',
    'mer_id' => '1001000000000000',
    'mer_prtcl_no' => '1001000000000000',
    
    // 密钥文件路径
    'private_key_path' => storage_path('keys/icbc_private_key.pem'),
    'icbc_public_key_path' => storage_path('keys/icbc_public_key.pem'),
    'gateway_key_path' => storage_path('keys/icbc_gateway_key.pem'),
    
    // 签名配置
    'sign_type' => 'RSA2',
    'charset' => 'UTF-8',
    'format' => 'json',
    
    // 回调URL
    'notify_url' => env('APP_URL', 'http://localhost') . '/icbc-pay/notify',
    'return_url' => env('APP_URL', 'http://localhost') . '/icbc-pay/return',
    
    // 网关配置
    'gateways' => [
        'sandbox' => [
            'base_url' => 'https://gw.open.icbc.com.cn/sandbox',
            'payment_url' => '/api/cardbusiness/aggregatepay/consumepurchase',
            'query_url' => '/api/cardbusiness/aggregatepay/orderquery',
        ],
    ],
    
    // 支付方式配置
    'payment_methods' => [
        'wechat' => [
            'pay_mode' => '9',
            'access_type' => '1',
            'name' => '微信支付',
        ],
        'alipay' => [
            'pay_mode' => '10',
            'access_type' => '1',
            'name' => '支付宝支付',
        ],
    ],
    
    // 开发配置
    'dev' => [
        'mock_enabled' => true,
        'debug_enabled' => true,
        'test_mode' => true,
    ],
];
