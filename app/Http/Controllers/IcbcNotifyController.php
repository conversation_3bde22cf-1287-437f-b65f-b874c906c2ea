<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use IcbcPay\Services\IcbcPayService;
use IcbcPay\Models\PaymentRecord;

class IcbcNotifyController extends Controller
{
    private $icbcPayService;

    public function __construct(IcbcPayService $icbcPayService)
    {
        $this->icbcPayService = $icbcPayService;
    }

    /**
     * 处理工商银行支付结果通知
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleNotify(Request $request)
    {
        try {
            Log::info('ICBC Payment Notify Received', $request->all());

            // 获取回调参数
            $params = $request->all();
            
            // 必要参数验证
            if (!isset($params['biz_content']) || !isset($params['sign'])) {
                Log::error('ICBC Notify: Missing required parameters');
                return $this->buildErrorResponse('参数错误');
            }

            // 验证签名
            if (!$this->verifySign($params)) {
                Log::error('ICBC Notify: Sign verification failed');
                return $this->buildErrorResponse('签名验证失败');
            }

            // 解析业务内容
            $bizContent = json_decode($params['biz_content'], true);
            if (!$bizContent) {
                Log::error('ICBC Notify: Invalid biz_content format');
                return $this->buildErrorResponse('业务内容格式错误');
            }

            // 提取关键信息
            $outTradeNo = $bizContent['out_trade_no'] ?? '';
            $returnCode = $bizContent['return_code'] ?? '';
            $thirdTradeNo = $bizContent['third_trade_no'] ?? '';
            $totalAmt = $bizContent['total_amt'] ?? '';
            $msgId = $bizContent['msg_id'] ?? '';
            $returnMsg = $bizContent['return_msg'] ?? '';

            Log::info('ICBC Notify: Processing payment', [
                'out_trade_no' => $outTradeNo,
                'return_code' => $returnCode,
                'third_trade_no' => $thirdTradeNo,
                'total_amt' => $totalAmt
            ]);

            // 查找支付记录
            $paymentRecord = PaymentRecord::where('out_trade_no', $outTradeNo)->first();
            if (!$paymentRecord) {
                Log::error('ICBC Notify: Payment record not found', ['out_trade_no' => $outTradeNo]);
                return $this->buildErrorResponse('订单不存在');
            }

            // 检查订单状态，避免重复处理
            if ($paymentRecord->status !== 'pending') {
                Log::info('ICBC Notify: Payment already processed', [
                    'out_trade_no' => $outTradeNo,
                    'current_status' => $paymentRecord->status
                ]);
                return $this->buildSuccessResponse($msgId);
            }

            // 验证金额
            $expectedAmount = bcmul($paymentRecord->total_amount, 100, 0); // 转换为分
            if ($totalAmt !== $expectedAmount) {
                Log::error('ICBC Notify: Amount mismatch', [
                    'expected' => $expectedAmount,
                    'received' => $totalAmt
                ]);
                return $this->buildErrorResponse('金额不匹配');
            }

            // 处理支付结果
            if ($returnCode === '0') {
                // 支付成功
                $this->handlePaymentSuccess($paymentRecord, $thirdTradeNo, $bizContent);
                Log::info('ICBC Notify: Payment success processed', ['out_trade_no' => $outTradeNo]);
            } else {
                // 支付失败
                $this->handlePaymentFailed($paymentRecord, $returnMsg, $bizContent);
                Log::info('ICBC Notify: Payment failed processed', [
                    'out_trade_no' => $outTradeNo,
                    'return_msg' => $returnMsg
                ]);
            }

            return $this->buildSuccessResponse($msgId);

        } catch (\Exception $e) {
            Log::error('ICBC Notify: Exception occurred', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->buildErrorResponse('系统错误');
        }
    }

    /**
     * 验证回调签名
     * 
     * @param array $params
     * @return bool
     */
    private function verifySign(array $params)
    {
        try {
            $sign = $params['sign'] ?? '';
            $signType = $params['sign_type'] ?? '';
            
            if (empty($sign) || $signType !== 'RSA') {
                return false;
            }

            // 构建验签参数
            $verifyParams = $params;
            unset($verifyParams['sign']); // 移除签名参数
            
            // 按照工商银行要求的格式构建签名字符串
            $signString = $this->buildSignString($verifyParams);
            
            // 获取工商银行网关公钥
            $publicKey = $this->getIcbcPublicKey();
            if (!$publicKey) {
                Log::error('ICBC Notify: Public key not found');
                return false;
            }

            // 验证签名
            $result = openssl_verify(
                $signString, 
                base64_decode($sign), 
                $publicKey, 
                OPENSSL_ALGO_SHA1  // 注意：回调使用RSA/SHA1，不是RSA2
            );

            return $result === 1;

        } catch (\Exception $e) {
            Log::error('ICBC Notify: Sign verification error', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 构建签名字符串
     * 
     * @param array $params
     * @return string
     */
    private function buildSignString(array $params)
    {
        // 按照工商银行回调验签要求构建签名字符串
        $notifyUrl = parse_url($params['from'] ?? '', PHP_URL_PATH) ?: 'notify';
        
        // 移除from参数
        unset($params['from']);
        
        // 参数排序
        ksort($params);
        
        // 构建查询字符串
        $queryString = http_build_query($params, '', '&', PHP_QUERY_RFC3986);
        
        // 根据工商银行文档格式
        return '"' . $notifyUrl . '?' . $queryString . '"';
    }

    /**
     * 获取工商银行网关公钥
     * 
     * @return resource|false
     */
    private function getIcbcPublicKey()
    {
        $config = config('icbc-pay');
        
        if (!empty($config['apigw_public_key'])) {
            return openssl_pkey_get_public($config['apigw_public_key']);
        }
        
        if (!empty($config['apigw_public_key_path']) && file_exists($config['apigw_public_key_path'])) {
            $publicKeyContent = file_get_contents($config['apigw_public_key_path']);
            return openssl_pkey_get_public($publicKeyContent);
        }
        
        return false;
    }

    /**
     * 处理支付成功
     * 
     * @param PaymentRecord $paymentRecord
     * @param string $thirdTradeNo
     * @param array $bizContent
     */
    private function handlePaymentSuccess(PaymentRecord $paymentRecord, string $thirdTradeNo, array $bizContent)
    {
        $paymentRecord->update([
            'status' => 'success',
            'trade_no' => $thirdTradeNo,
            'paid_at' => now(),
            'notify_params' => json_encode($bizContent),
            'updated_at' => now()
        ]);

        // 可以在这里添加其他业务逻辑，如：
        // 1. 发送支付成功短信
        // 2. 更新停车场系统
        // 3. 记录停车费用日志
        // 4. 触发其他相关业务流程

        Log::info('Payment success processed', [
            'out_trade_no' => $paymentRecord->out_trade_no,
            'car_number' => $paymentRecord->car_number,
            'amount' => $paymentRecord->total_amount,
            'trade_no' => $thirdTradeNo
        ]);
    }

    /**
     * 处理支付失败
     * 
     * @param PaymentRecord $paymentRecord
     * @param string $returnMsg
     * @param array $bizContent
     */
    private function handlePaymentFailed(PaymentRecord $paymentRecord, string $returnMsg, array $bizContent)
    {
        $paymentRecord->update([
            'status' => 'failed',
            'notify_params' => json_encode($bizContent),
            'updated_at' => now()
        ]);

        Log::warning('Payment failed processed', [
            'out_trade_no' => $paymentRecord->out_trade_no,
            'car_number' => $paymentRecord->car_number,
            'amount' => $paymentRecord->total_amount,
            'reason' => $returnMsg
        ]);
    }

    /**
     * 构建成功响应
     * 
     * @param string $msgId
     * @return \Illuminate\Http\JsonResponse
     */
    private function buildSuccessResponse(string $msgId)
    {
        $responseBizContent = json_encode([
            'return_code' => 0,
            'return_msg' => 'success',
            'msg_id' => $msgId
        ], JSON_UNESCAPED_UNICODE);

        $responseData = [
            'response_biz_content' => $responseBizContent,
            'sign_type' => 'RSA2'
        ];

        // 生成响应签名
        $signString = '"response_biz_content":' . $responseBizContent . ',"sign_type":"RSA2"';
        $responseData['sign'] = $this->generateResponseSign($signString);

        return response()->json($responseData);
    }

    /**
     * 构建错误响应
     * 
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    private function buildErrorResponse(string $message)
    {
        $responseBizContent = json_encode([
            'return_code' => -1,
            'return_msg' => $message
        ], JSON_UNESCAPED_UNICODE);

        $responseData = [
            'response_biz_content' => $responseBizContent,
            'sign_type' => 'RSA2'
        ];

        // 生成响应签名
        $signString = '"response_biz_content":' . $responseBizContent . ',"sign_type":"RSA2"';
        $responseData['sign'] = $this->generateResponseSign($signString);

        return response()->json($responseData);
    }

    /**
     * 生成响应签名
     * 
     * @param string $signString
     * @return string
     */
    private function generateResponseSign(string $signString)
    {
        try {
            $config = config('icbc-pay');
            $privateKey = null;

            if (!empty($config['private_key'])) {
                $privateKey = openssl_pkey_get_private($config['private_key']);
            } elseif (!empty($config['private_key_path']) && file_exists($config['private_key_path'])) {
                $privateKeyContent = file_get_contents($config['private_key_path']);
                $privateKey = openssl_pkey_get_private($privateKeyContent);
            }

            if (!$privateKey) {
                Log::error('ICBC Notify: Private key not found for response signing');
                return '';
            }

            if (!openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
                Log::error('ICBC Notify: Failed to generate response signature');
                return '';
            }

            return base64_encode($signature);

        } catch (\Exception $e) {
            Log::error('ICBC Notify: Response sign generation error', ['error' => $e->getMessage()]);
            return '';
        }
    }

    /**
     * 处理支付返回页面（用户支付完成后的跳转）
     * 
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleReturn(Request $request)
    {
        try {
            $outTradeNo = $request->get('out_trade_no');
            $status = $request->get('status', 'pending');

            if ($outTradeNo) {
                // 查询最新的支付状态
                $paymentRecord = PaymentRecord::where('out_trade_no', $outTradeNo)->first();
                if ($paymentRecord) {
                    $status = $paymentRecord->status;
                }

                return redirect()->route('parking.result', [
                    'outTradeNo' => $outTradeNo,
                    'status' => $status
                ]);
            }

            return redirect()->route('parking.index')->with('error', '支付状态查询失败');

        } catch (\Exception $e) {
            Log::error('ICBC Return: Exception occurred', ['error' => $e->getMessage()]);
            return redirect()->route('parking.index')->with('error', '系统错误');
        }
    }
} 