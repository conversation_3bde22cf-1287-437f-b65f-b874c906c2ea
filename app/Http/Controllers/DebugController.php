<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use IcbcPay\Services\IcbcPayService;
use IcbcPay\Models\PaymentRecord;

class DebugController extends Controller
{
    /**
     * 调试工商银行支付配置和网络连接
     */
    public function debugIcbcPayment()
    {
        $debug = [];
        
        // 1. 检查配置
        $debug['config'] = [
            'gateway_url' => config('icbc-pay.gateway_url'),
            'ui_mode' => config('icbc-pay.payment_config.use_ui_mode'),
            'app_id' => config('icbc-pay.app_id'),
            'mer_id' => config('icbc-pay.mer_id'),
            'timeout' => config('icbc-pay.timeout'),
            'consume_purchase_api' => config('icbc-pay.api_urls.consume_purchase'),
            'consume_purchase_ui_api' => config('icbc-pay.api_urls.consume_purchase_ui'),
        ];

        // 2. 检查网络连接
        try {
            $baseUrl = config('icbc-pay.gateway_url');
            $debug['network']['base_url'] = $baseUrl;
            
            // 测试基础连接
            $response = Http::timeout(10)->get($baseUrl);
            $debug['network']['base_connection'] = [
                'status' => $response->status(),
                'success' => $response->successful(),
                'error' => $response->failed() ? 'Failed' : 'OK'
            ];
        } catch (\Exception $e) {
            $debug['network']['base_connection'] = [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }

        // 3. 测试UI接口连接
        try {
            $uiUrl = config('icbc-pay.gateway_url') . config('icbc-pay.api_urls.consume_purchase_ui');
            $debug['network']['ui_url'] = $uiUrl;
            
            $response = Http::timeout(10)->get($uiUrl . '?test=1');
            $debug['network']['ui_connection'] = [
                'status' => $response->status(),
                'success' => $response->successful(),
                'headers' => $response->headers(),
                'error' => $response->failed() ? 'Failed' : 'OK'
            ];
        } catch (\Exception $e) {
            $debug['network']['ui_connection'] = [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }

        // 4. 测试API接口连接
        try {
            $apiUrl = config('icbc-pay.gateway_url') . config('icbc-pay.api_urls.consume_purchase');
            $debug['network']['api_url'] = $apiUrl;
            
            $response = Http::timeout(10)->post($apiUrl, ['test' => '1']);
            $debug['network']['api_connection'] = [
                'status' => $response->status(),
                'success' => $response->successful(),
                'error' => $response->failed() ? 'Failed' : 'OK'
            ];
        } catch (\Exception $e) {
            $debug['network']['api_connection'] = [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }

        // 5. 测试模拟支付
        try {
            $paymentRecord = PaymentRecord::create([
                'out_trade_no' => 'DEBUG_' . time(),
                'total_amount' => 1.00,
                'subject' => '调试测试',
                'payment_method' => 'wechat',
                'car_number' => '调试000',
                'status' => 'pending'
            ]);

            $icbcPayService = new IcbcPayService();
            $result = $icbcPayService->createPayment($paymentRecord);
            
            $debug['payment_test'] = [
                'success' => $result['success'] ?? false,
                'payment_id' => $result['payment_id'] ?? null,
                'has_payment_url' => isset($result['payment_url']),
                'has_payment_form' => isset($result['payment_form']),
                'ui_mode' => $result['ui_mode'] ?? false,
                'result_keys' => array_keys($result)
            ];

            // 清理测试数据
            $paymentRecord->delete();
            
        } catch (\Exception $e) {
            $debug['payment_test'] = [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ];
        }

        // 6. 环境信息
        $debug['environment'] = [
            'app_env' => app()->environment(),
            'debug_mode' => config('app.debug'),
            'php_version' => PHP_VERSION,
            'curl_version' => function_exists('curl_version') ? curl_version()['version'] : 'Not available',
            'openssl_version' => OPENSSL_VERSION_TEXT,
        ];

        return response()->json($debug, 200, [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * 测试网络连接
     */
    public function testNetworkConnection(Request $request)
    {
        $url = $request->get('url', config('icbc-pay.gateway_url'));
        
        try {
            $startTime = microtime(true);
            $response = Http::timeout(30)->get($url);
            $endTime = microtime(true);
            
            return response()->json([
                'url' => $url,
                'status' => $response->status(),
                'success' => $response->successful(),
                'response_time_ms' => round(($endTime - $startTime) * 1000, 2),
                'headers' => $response->headers(),
                'body_preview' => substr($response->body(), 0, 500),
                'error' => null
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'url' => $url,
                'success' => false,
                'error' => $e->getMessage(),
                'error_type' => get_class($e)
            ]);
        }
    }

    /**
     * 显示调试页面
     */
    public function showDebugPage()
    {
        return view('debug.icbc-payment');
    }
} 