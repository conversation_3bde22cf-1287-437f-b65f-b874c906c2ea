<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use IcbcPay\Models\PaymentRecord;
use IcbcPay\IcbcPayClient;
use Exception;
use InvalidArgumentException;
use Illuminate\Support\Facades\Log;
use App\Services\IcbcRateLimiter;

/**
 * 停车费支付控制器 - 增强版日志记录
 *
 * @package App\Http\Controllers
 * <AUTHOR> Name
 * @version 2.0.0
 */
class ParkingController extends Controller
{
    private IcbcPayClient $icbcPayClient;

    /**
     * 构造函数
     *
     * @param IcbcPayClient $icbcPayClient ICBC支付客户端
     */
    public function __construct(IcbcPayClient $icbcPayClient)
    {
        $this->icbcPayClient = $icbcPayClient;
    }

    /**
     * 显示停车费支付主页
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // 记录用户访问支付主页
        Log::info('🏠 PARKING INDEX: User accessed parking payment homepage', [
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'user_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
        ]);

        return view('parking.index');
    }

    /**
     * 创建支付订单
     *
     * @param Request $request 请求对象
     * @return \Illuminate\Http\JsonResponse
     */
    public function createPayment(Request $request)
    {
        $requestId = uniqid('REQ_');
        
        // 记录前端提交的原始数据
        Log::info('💰 PAYMENT CREATE: Frontend data received', [
            'request_id' => $requestId,
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'user_ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'session_id' => session()->getId(),
            'raw_input_data' => $request->all(),
            'content_type' => $request->header('Content-Type'),
            'request_method' => $request->method(),
            'url' => $request->fullUrl(),
        ]);

        try {
            // 数据验证
            Log::info('📋 PAYMENT CREATE: Starting data validation', [
                'request_id' => $requestId,
                'validation_rules' => [
                    'car_number' => 'required|string|max:20|regex:/^[\x{4e00}-\x{9fa5}A-Z0-9]{2,8}$/u',
                    'amount' => 'required|numeric|min:0.01|max:9999.99',
                    'payment_method' => 'required|in:alipay,wechat,unionpay',
                    'parking_duration' => 'nullable|integer|min:0|max:1440',
                ]
            ]);

            $validated = $request->validate([
                'car_number' => 'required|string|max:20|regex:/^[\x{4e00}-\x{9fa5}A-Z0-9]{2,8}$/u',
                'amount' => 'required|numeric|min:0.01|max:9999.99',
                'payment_method' => 'required|in:alipay,wechat,unionpay',
                'parking_duration' => 'nullable|integer|min:0|max:1440', // 最长24小时，可选
            ]);

            Log::info('✅ PAYMENT CREATE: Data validation passed', [
                'request_id' => $requestId,
                'validated_data' => $validated,
            ]);

            // 并发限制检查已关闭 - 不进行频率限制检测
            // if (!IcbcRateLimiter::canMakePayment()) {
            //     $nextTime = IcbcRateLimiter::getNextAvailableTime();
            //     
            //     Log::warning('🚦 PAYMENT CREATE: Rate limit exceeded', [
            //         'request_id' => $requestId,
            //         'rate_limit_info' => $nextTime,
            //         'user_ip' => $request->ip(),
            //     ]);
            //     
            //     $waitMinutes = $nextTime ? round($nextTime['wait_seconds'] / 60, 1) : 5;
            //     
            //     return response()->json([
            //         'success' => false,
            //         'error' => '请求过于频繁，请稍后再试',
            //         'message' => "为避免触发工行并发限制，请等待 {$waitMinutes} 分钟后重试",
            //         'next_available_time' => $nextTime['next_time_formatted'] ?? null,
            //         'wait_seconds' => $nextTime['wait_seconds'] ?? 300,
            //     ], 429);
            // }
            
            Log::info('✅ PAYMENT CREATE: Rate limit check disabled', [
                'request_id' => $requestId,
                'note' => 'Concurrency control is turned off',
            ]);

            // 检查重复订单
            Log::info('🔍 PAYMENT CREATE: Checking for duplicate orders', [
                'request_id' => $requestId,
                'car_number' => $validated['car_number'],
                'check_time_range' => '5 minutes ago',
            ]);

            $existingPendingOrder = PaymentRecord::where('car_number', $validated['car_number'])
                ->where('status', 'pending')
                ->where('created_at', '>=', now()->subMinutes(5))
                ->first();
                
            if ($existingPendingOrder) {
                Log::warning('⚠️ PAYMENT CREATE: Duplicate order detected', [
                    'request_id' => $requestId,
                    'car_number' => $validated['car_number'],
                    'existing_order_no' => $existingPendingOrder->out_trade_no,
                    'existing_order_created' => $existingPendingOrder->created_at->format('Y-m-d H:i:s'),
                ]);

                return response()->json([
                    'success' => false,
                    'error' => '该车牌已有未完成的支付订单',
                    'existing_order' => $existingPendingOrder->out_trade_no,
                    'message' => '请完成现有订单或等待5分钟后重试'
                ], 409);
            }

            // 创建支付记录
            Log::info('💾 PAYMENT CREATE: Creating payment record', [
                'request_id' => $requestId,
                'attempt_start' => now()->format('Y-m-d H:i:s.u'),
            ]);

            $paymentRecord = $this->createPaymentRecord($validated);
            
            if (!$paymentRecord) {
                Log::error('❌ PAYMENT CREATE: Payment record creation failed', [
                    'request_id' => $requestId,
                    'validated_data' => $validated,
                ]);

                return response()->json([
                    'success' => false,
                    'error' => '订单创建失败',
                    'message' => '请稍后重试'
                ], 500);
            }

            Log::info('✅ PAYMENT CREATE: Payment record created successfully', [
                'request_id' => $requestId,
                'order_no' => $paymentRecord->out_trade_no,
                'record_id' => $paymentRecord->id,
                'created_at' => $paymentRecord->created_at->format('Y-m-d H:i:s.u'),
            ]);

            // 处理支付
            Log::info('🏦 PAYMENT CREATE: Starting ICBC payment processing', [
                'request_id' => $requestId,
                'order_no' => $paymentRecord->out_trade_no,
                'payment_method' => $paymentRecord->payment_method,
                'amount' => $paymentRecord->total_amount,
            ]);

            $paymentResult = $this->processPayment($paymentRecord, $request);

            Log::info('🎉 PAYMENT CREATE: Payment processing completed successfully', [
                'request_id' => $requestId,
                'order_no' => $paymentRecord->out_trade_no,
                'payment_result' => $paymentResult,
                'processing_time' => now()->format('Y-m-d H:i:s.u'),
            ]);

            // 并发限制记录已关闭 - 不记录到频率限制器
            // IcbcRateLimiter::recordPayment();
            
            Log::info('📊 PAYMENT CREATE: Payment recording disabled', [
                'request_id' => $requestId,
                'order_no' => $paymentRecord->out_trade_no,
                'note' => 'Rate limiter recording is turned off',
            ]);

            // 记录成功创建的支付订单
            Log::info('📊 PAYMENT CREATE: Final success log', [
                'request_id' => $requestId,
                'out_trade_no' => $paymentRecord->out_trade_no,
                'car_number' => $paymentRecord->car_number,
                'amount' => $paymentRecord->total_amount,
                'payment_method' => $paymentRecord->payment_method,
                'user_ip' => $request->ip(),
                'timestamp' => now()->format('Y-m-d H:i:s'),
                'complete_flow_time' => now()->format('Y-m-d H:i:s.u'),
            ]);

            return response()->json([
                'success' => true,
                'message' => '支付订单创建成功',
                'data' => $paymentResult
            ]);

        } catch (InvalidArgumentException $e) {
            Log::error('❌ PAYMENT CREATE: Validation failed', [
                'request_id' => $requestId,
                'error_type' => 'validation_error',
                'error_message' => $e->getMessage(),
                'input_data' => $request->all(),
                'timestamp' => now()->format('Y-m-d H:i:s'),
            ]);

            return response()->json([
                'success' => false,
                'error' => '参数验证失败',
                'message' => $e->getMessage()
            ], 400);
        } catch (Exception $e) {
            Log::error('❌ PAYMENT CREATE: Exception occurred', [
                'request_id' => $requestId,
                'error_type' => get_class($e),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'error_trace' => $e->getTraceAsString(),
                'out_trade_no' => $paymentRecord->out_trade_no ?? 'N/A',
                'car_number' => $validated['car_number'] ?? 'N/A',
                'amount' => $validated['amount'] ?? 'N/A',
                'user_ip' => $request->ip(),
                'timestamp' => now()->format('Y-m-d H:i:s'),
            ]);

            // 更新订单状态为失败（如果订单已创建）
            if (isset($paymentRecord)) {
                $paymentRecord->update(['status' => 'failed']);
                Log::info('📝 PAYMENT CREATE: Order status updated to failed', [
                    'request_id' => $requestId,
                    'order_no' => $paymentRecord->out_trade_no,
                ]);
            }

            return response()->json([
                'success' => false,
                'error' => $this->getErrorMessage($e),
                'debug_info' => app()->environment(['local', 'development']) ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * 显示支付页面
     *
     * @param string $orderNo 订单号
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function showPaymentPage(string $orderNo)
    {
        $pageId = uniqid('PAGE_');
        
        Log::info('💳 PAYMENT PAGE: User accessing payment page', [
            'page_id' => $pageId,
            'order_no' => $orderNo,
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'user_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'referrer' => request()->header('referer'),
        ]);

        try {
            $paymentRecord = PaymentRecord::where('out_trade_no', $orderNo)->firstOrFail();
            
            Log::info('📋 PAYMENT PAGE: Payment record found', [
                'page_id' => $pageId,
                'order_no' => $orderNo,
                'record_id' => $paymentRecord->id,
                'status' => $paymentRecord->status,
                'amount' => $paymentRecord->total_amount,
                'car_number' => $paymentRecord->car_number,
                'created_at' => $paymentRecord->created_at->format('Y-m-d H:i:s'),
            ]);
            
            // 检查支付状态
            if ($paymentRecord->status !== 'pending') {
                Log::info('🔄 PAYMENT PAGE: Order not pending, redirecting to result', [
                    'page_id' => $pageId,
                    'order_no' => $orderNo,
                    'current_status' => $paymentRecord->status,
                ]);

                return redirect()->route('parking.result', ['orderNo' => $orderNo]);
            }

            // 准备订单数据用于支付表单
            $orderData = [
                'order_id' => $paymentRecord->out_trade_no,
                'amount' => $paymentRecord->total_amount,
                'subject' => $paymentRecord->subject,
                'merchant_order_no' => $paymentRecord->out_trade_no,
                'payment_method' => $paymentRecord->payment_method,
                'body' => "停车费支付 - 车牌：{$paymentRecord->car_number}",
                'attach' => json_encode([
                    'car_number' => $paymentRecord->car_number,
                    'parking_duration' => $paymentRecord->parking_duration
                ])
            ];

            Log::info('🏗️ PAYMENT PAGE: Preparing order data for ICBC form', [
                'page_id' => $pageId,
                'order_data' => $orderData,
                'prepare_time' => now()->format('Y-m-d H:i:s.u'),
            ]);

            // 生成工行支付表单
            Log::info('🔨 PAYMENT PAGE: Building ICBC payment form', [
                'page_id' => $pageId,
                'order_no' => $orderNo,
                'build_start' => now()->format('Y-m-d H:i:s.u'),
            ]);

            $paymentForm = $this->icbcPayClient->buildForm($orderData);
            
            Log::info('✅ PAYMENT PAGE: ICBC payment form built successfully', [
                'page_id' => $pageId,
                'order_no' => $orderNo,
                'form_length' => strlen($paymentForm),
                'build_end' => now()->format('Y-m-d H:i:s.u'),
            ]);

            // 提取表单中的关键信息用于日志
            $this->logPaymentFormDetails($pageId, $paymentForm, $orderNo);
            
            return view('parking.payment', [
                'paymentRecord' => $paymentRecord,
                'paymentForm' => $paymentForm,
                'orderNo' => $orderNo
            ]);

        } catch (Exception $e) {
            Log::error('❌ PAYMENT PAGE: Failed to show payment page', [
                'page_id' => $pageId,
                'order_no' => $orderNo,
                'error_type' => get_class($e),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => now()->format('Y-m-d H:i:s'),
            ]);

            return redirect()->route('parking.fallback', ['orderNo' => $orderNo]);
        }
    }

    /**
     * 显示支付结果页面
     *
     * @param string $orderNo 订单号
     * @return \Illuminate\View\View
     */
    public function showPaymentResult(string $orderNo)
    {
        $paymentRecord = PaymentRecord::where('out_trade_no', $orderNo)->firstOrFail();
        
        return view('parking.result', [
            'paymentRecord' => $paymentRecord,
            'orderNo' => $orderNo
        ]);
    }

    /**
     * 查询支付状态（API接口）
     *
     * @param string $orderNo 订单号
     * @return \Illuminate\Http\JsonResponse
     */
    public function queryPaymentStatus(string $orderNo)
    {
        try {
            $paymentRecord = PaymentRecord::where('out_trade_no', $orderNo)->first();
            
            if (!$paymentRecord) {
                return response()->json([
                    'success' => false,
                    'message' => '订单不存在'
                ], 404);
            }

            // 如果订单状态为pending，尝试查询最新状态
            if ($paymentRecord->status === 'pending') {
                try {
                    $queryResult = $this->icbcPayClient->query($orderNo);
                    
                    // 更新本地订单状态
                    if ($queryResult['success'] && isset($queryResult['status'])) {
                        $newStatus = $this->mapPaymentStatus($queryResult['status']);
                        if ($newStatus !== 'pending') {
                            $paymentRecord->update([
                                'status' => $newStatus,
                                'trade_no' => $queryResult['trade_no'] ?? null,
                                'paid_at' => $newStatus === 'success' ? now() : null
                            ]);
                        }
                    }
                } catch (Exception $e) {
                    \Log::warning('Failed to query payment status from ICBC', [
                        'order_no' => $orderNo,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => '查询成功',
                'data' => [
                    'out_trade_no' => $paymentRecord->out_trade_no,
                    'trade_no' => $paymentRecord->trade_no,
                    'car_number' => $paymentRecord->car_number,
                    'total_amount' => number_format($paymentRecord->total_amount, 2),
                    'status' => $paymentRecord->status,
                    'payment_method' => $paymentRecord->payment_method,
                    'parking_duration' => $paymentRecord->parking_duration,
                    'created_at' => $paymentRecord->created_at->format('Y-m-d H:i:s'),
                    'paid_at' => $paymentRecord->paid_at?->format('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '查询失败：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 处理支付回调
     *
     * @param Request $request 请求对象
     * @return \Illuminate\Http\Response
     */
    public function handlePaymentCallback(Request $request)
    {
        $callbackId = uniqid('CALLBACK_');
        
        Log::info('📞 ICBC CALLBACK: Payment callback received', [
            'callback_id' => $callbackId,
            'timestamp' => now()->format('Y-m-d H:i:s.u'),
            'user_ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'content_type' => $request->header('Content-Type'),
            'request_method' => $request->method(),
            'url' => $request->fullUrl(),
            'raw_callback_data' => $request->all(),
            'raw_input_stream' => $request->getContent(),
        ]);

        try {
            $notifyData = $request->all();
            
            Log::info('🔍 ICBC CALLBACK: Processing callback data', [
                'callback_id' => $callbackId,
                'notify_data' => $notifyData,
                'processing_start' => now()->format('Y-m-d H:i:s.u'),
            ]);

            // 调用ICBC客户端处理回调
            Log::info('🏦 ICBC CALLBACK: Calling ICBC client notify method', [
                'callback_id' => $callbackId,
                'client_call_start' => now()->format('Y-m-d H:i:s.u'),
            ]);

            $result = $this->icbcPayClient->notify($notifyData);
            
            Log::info('📊 ICBC CALLBACK: ICBC client response received', [
                'callback_id' => $callbackId,
                'icbc_result' => $result,
                'client_call_end' => now()->format('Y-m-d H:i:s.u'),
            ]);
            
            if ($result['success']) {
                $orderNo = $result['order_id'];
                
                Log::info('✅ ICBC CALLBACK: Callback validation successful', [
                    'callback_id' => $callbackId,
                    'order_no' => $orderNo,
                    'trade_no' => $result['trade_no'] ?? null,
                    'amount' => $result['amount'] ?? null,
                ]);

                $paymentRecord = PaymentRecord::where('out_trade_no', $orderNo)->first();
                
                if ($paymentRecord) {
                    Log::info('📝 ICBC CALLBACK: Updating payment record', [
                        'callback_id' => $callbackId,
                        'order_no' => $orderNo,
                        'old_status' => $paymentRecord->status,
                        'new_status' => 'success',
                        'update_start' => now()->format('Y-m-d H:i:s.u'),
                    ]);

                    $paymentRecord->update([
                        'status' => 'success',
                        'trade_no' => $result['trade_no'] ?? null,
                        'paid_at' => now(),
                        'notify_params' => $notifyData
                    ]);

                    Log::info('🎉 ICBC CALLBACK: Payment record updated successfully', [
                        'callback_id' => $callbackId,
                        'order_no' => $orderNo,
                        'record_id' => $paymentRecord->id,
                        'trade_no' => $result['trade_no'] ?? null,
                        'amount' => $result['amount'],
                        'car_number' => $paymentRecord->car_number,
                        'update_end' => now()->format('Y-m-d H:i:s.u'),
                    ]);
                } else {
                    Log::warning('⚠️ ICBC CALLBACK: Payment record not found', [
                        'callback_id' => $callbackId,
                        'order_no' => $orderNo,
                        'search_attempted' => true,
                    ]);
                }
                
                Log::info('✅ ICBC CALLBACK: Returning SUCCESS response', [
                    'callback_id' => $callbackId,
                    'response' => 'SUCCESS',
                    'response_time' => now()->format('Y-m-d H:i:s.u'),
                ]);

                return response('SUCCESS', 200);
            } else {
                Log::error('❌ ICBC CALLBACK: Callback validation failed', [
                    'callback_id' => $callbackId,
                    'icbc_result' => $result,
                    'notify_data' => $notifyData,
                    'validation_failure_time' => now()->format('Y-m-d H:i:s.u'),
                ]);
                
                return response('FAIL', 400);
            }
        } catch (Exception $e) {
            Log::error('❌ ICBC CALLBACK: Exception during callback processing', [
                'callback_id' => $callbackId,
                'error_type' => get_class($e),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'error_trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'exception_time' => now()->format('Y-m-d H:i:s.u'),
            ]);
            
            return response('FAIL', 500);
        }
    }

    /**
     * 处理支付返回
     *
     * @param Request $request 请求对象
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handlePaymentReturn(Request $request)
    {
        $returnId = uniqid('RETURN_');
        
        Log::info('🔙 ICBC RETURN: User returned from ICBC payment', [
            'return_id' => $returnId,
            'timestamp' => now()->format('Y-m-d H:i:s.u'),
            'user_ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referrer' => $request->header('referer'),
            'return_data' => $request->all(),
            'query_string' => $request->getQueryString(),
        ]);

        $orderNo = $request->get('out_trade_no');
        
        if ($orderNo) {
            Log::info('✅ ICBC RETURN: Order number found, redirecting to result', [
                'return_id' => $returnId,
                'order_no' => $orderNo,
                'redirect_target' => route('parking.result', ['orderNo' => $orderNo]),
            ]);

            return redirect()->route('parking.result', ['orderNo' => $orderNo]);
        }
        
        Log::warning('⚠️ ICBC RETURN: No order number in return data', [
            'return_id' => $returnId,
            'return_data' => $request->all(),
            'redirect_target' => 'parking.index',
        ]);

        return redirect()->route('parking.index')->with('error', '支付返回参数异常');
    }

    /**
     * 显示降级支付页面（当工商银行支付不可用时）
     *
     * @param string $orderNo 订单号
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function showFallbackPayment(string $orderNo)
    {
        $paymentRecord = PaymentRecord::where('out_trade_no', $orderNo)->firstOrFail();

        // 检查订单状态
        if ($paymentRecord->status === 'success') {
            return redirect()->route('parking.result', ['orderNo' => $orderNo]);
        }

        return view('parking.fallback', [
            'paymentRecord' => $paymentRecord,
            'orderNo' => $orderNo
        ]);
    }

    /**
     * 创建支付记录
     *
     * @param array $validated 验证后的数据
     * @return PaymentRecord|null
     */
    private function createPaymentRecord(array $validated): ?PaymentRecord
    {
        $maxRetries = 3;
        $attempts = 0;
        
        while ($attempts < $maxRetries) {
            try {
                return PaymentRecord::create([
                    'out_trade_no' => PaymentRecord::generateUniqueOrderNo(),
                    'total_amount' => $validated['amount'],
                    'subject' => '停车费支付',
                    'payment_method' => $validated['payment_method'],
                    'car_number' => $validated['car_number'],
                    'parking_duration' => $validated['parking_duration'] ?? 0,
                    'status' => 'pending',
                ]);
                
            } catch (\Illuminate\Database\QueryException $e) {
                if (str_contains($e->getMessage(), 'UNIQUE constraint failed') && $attempts < $maxRetries - 1) {
                    $attempts++;
                    usleep(rand(1000, 5000)); // 1-5毫秒随机延迟
                    continue;
                } else {
                    throw $e;
                }
            }
        }
        
        return null;
    }

    /**
     * 处理支付
     *
     * @param PaymentRecord $paymentRecord 支付记录
     * @param Request $request 请求对象
     * @return array
     * @throws Exception
     */
    private function processPayment(PaymentRecord $paymentRecord, Request $request): array
    {
        $orderData = [
            'order_id' => $paymentRecord->out_trade_no,
            'amount' => $paymentRecord->total_amount,
            'subject' => $paymentRecord->subject,
            'merchant_order_no' => $paymentRecord->out_trade_no,
            'payment_method' => $paymentRecord->payment_method,
            'body' => "停车费支付 - 车牌：{$paymentRecord->car_number}",
            'attach' => json_encode([
                'car_number' => $paymentRecord->car_number,
                'parking_duration' => $paymentRecord->parking_duration
            ])
        ];

        // 根据支付方式调用相应的支付方法
        switch ($paymentRecord->payment_method) {
            case 'wechat':
                $result = $this->icbcPayClient->wechatPay($orderData);
                break;
            case 'alipay':
                $result = $this->icbcPayClient->alipay($orderData);
                break;
            default:
                throw new InvalidArgumentException('不支持的支付方式：' . $paymentRecord->payment_method . '。当前仅支持微信支付和支付宝支付。');
        }

        // 添加支付页面URL
        $result['payment_url'] = route('parking.payment', ['orderNo' => $paymentRecord->out_trade_no]);
        
        return $result;
    }

    /**
     * 映射支付状态
     *
     * @param string $status 原始状态
     * @return string 标准状态
     */
    private function mapPaymentStatus(string $status): string
    {
        $statusMap = [
            'pending' => 'pending',
            'success' => 'success',
            'failed' => 'failed',
            'cancelled' => 'failed',
            'refunded' => 'refunded',
            'unknown' => 'pending'
        ];

        return $statusMap[$status] ?? 'pending';
    }

    /**
     * 获取友好的错误信息
     *
     * @param Exception $e 异常对象
     * @return string
     */
    private function getErrorMessage(Exception $e): string
    {
        $message = $e->getMessage();
        
        if (str_contains($message, '网络')) {
            return '网络连接异常，请检查网络后重试';
        } elseif (str_contains($message, '签名')) {
            return '支付参数错误，请联系客服';
        } elseif (str_contains($message, '配置')) {
            return '支付系统配置异常，请联系客服';
        }

        return '支付服务暂时不可用，请稍后重试';
    }

    /**
     * 提取并记录支付表单的详细信息
     */
    private function logPaymentFormDetails(string $pageId, string $formHtml, string $orderNo): void
    {
        try {
            // 提取时间戳
            if (preg_match('/name="timestamp" value="([^"]+)"/', $formHtml, $matches)) {
                $timestamp = $matches[1];
                Log::info('🕐 PAYMENT FORM: Timestamp extracted', [
                    'page_id' => $pageId,
                    'order_no' => $orderNo,
                    'form_timestamp' => $timestamp,
                    'current_time' => now()->format('Y-m-d H:i:s'),
                    'time_diff_seconds' => abs(strtotime($timestamp) - time()),
                ]);
            }

            // 提取MSG ID
            if (preg_match('/name="msg_id" value="([^"]+)"/', $formHtml, $matches)) {
                $msgId = $matches[1];
                Log::info('🆔 PAYMENT FORM: MSG ID extracted', [
                    'page_id' => $pageId,
                    'order_no' => $orderNo,
                    'msg_id' => $msgId,
                    'msg_id_time_part' => substr($msgId, 0, 14),
                ]);
            }

            // 提取APP ID
            if (preg_match('/name="app_id" value="([^"]+)"/', $formHtml, $matches)) {
                $appId = $matches[1];
                Log::info('🏪 PAYMENT FORM: APP ID extracted', [
                    'page_id' => $pageId,
                    'order_no' => $orderNo,
                    'app_id' => $appId,
                ]);
            }

            // 提取签名
            if (preg_match('/name="sign" value="([^"]+)"/', $formHtml, $matches)) {
                $sign = $matches[1];
                Log::info('🔐 PAYMENT FORM: Signature extracted', [
                    'page_id' => $pageId,
                    'order_no' => $orderNo,
                    'signature' => substr($sign, 0, 50) . '...',
                    'signature_length' => strlen($sign),
                ]);
            }

            // 提取biz_content
            if (preg_match('/name="biz_content" value="([^"]+)"/', $formHtml, $matches)) {
                $bizContent = html_entity_decode($matches[1]);
                $bizData = json_decode($bizContent, true);
                Log::info('📋 PAYMENT FORM: Business content extracted', [
                    'page_id' => $pageId,
                    'order_no' => $orderNo,
                    'biz_content' => $bizData,
                ]);
            }

        } catch (Exception $e) {
            Log::warning('⚠️ PAYMENT FORM: Failed to extract form details', [
                'page_id' => $pageId,
                'order_no' => $orderNo,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
