<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * 工商银行支付频率限制器
 * 防止触发工行API并发限制
 *
 * @package App\Services
 */
class IcbcRateLimiter
{
    /** @var string 缓存键前缀 */
    private const RATE_LIMIT_KEY = 'icbc_payment_rate_limit';
    
    /** @var string 每日计数器键 */
    private const DAILY_COUNT_KEY = 'icbc_daily_payment_count';
    
    /** @var int 最小间隔时间（秒） */
    private const MIN_INTERVAL = 600; // 5分钟
    
    /** @var int 每日最大请求数 */
    private const MAX_DAILY_REQUESTS = 50;
    
    /** @var int 短期突发限制（1分钟内最多请求数） */
    private const BURST_LIMIT = 1;
    
    /** @var int 突发检查窗口（秒） */
    private const BURST_WINDOW = 120;

    /**
     * 检查是否可以发起支付请求
     *
     * @return bool
     */
    public static function canMakePayment(): bool
    {
        // 检查基本间隔限制
        if (!self::checkBasicInterval()) {
            return false;
        }
        
        // 检查突发限制
        if (!self::checkBurstLimit()) {
            return false;
        }
        
        // 检查每日限制
        if (!self::checkDailyLimit()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查基本间隔限制（5分钟）
     *
     * @return bool
     */
    private static function checkBasicInterval(): bool
    {
        $lastPaymentTime = Cache::get(self::RATE_LIMIT_KEY, 0);
        $currentTime = time();
        
        if ($currentTime - $lastPaymentTime < self::MIN_INTERVAL) {
            $waitTime = self::MIN_INTERVAL - ($currentTime - $lastPaymentTime);
            
            Log::warning('🚦 ICBC Rate Limit: 基本间隔限制', [
                'type' => 'basic_interval',
                'wait_seconds' => $waitTime,
                'wait_until' => date('Y-m-d H:i:s', $currentTime + $waitTime),
                'last_payment' => date('Y-m-d H:i:s', $lastPaymentTime),
                'min_interval' => self::MIN_INTERVAL
            ]);
            
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查突发限制（1分钟内最多1次）
     *
     * @return bool
     */
    private static function checkBurstLimit(): bool
    {
        $burstKey = self::RATE_LIMIT_KEY . '_burst_' . floor(time() / self::BURST_WINDOW);
        $burstCount = Cache::get($burstKey, 0);
        
        if ($burstCount >= self::BURST_LIMIT) {
            $waitTime = self::BURST_WINDOW - (time() % self::BURST_WINDOW);
            
            Log::warning('🚦 ICBC Rate Limit: 突发限制', [
                'type' => 'burst_limit',
                'current_count' => $burstCount,
                'max_count' => self::BURST_LIMIT,
                'wait_seconds' => $waitTime,
                'window_seconds' => self::BURST_WINDOW
            ]);
            
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查每日限制
     *
     * @return bool
     */
    private static function checkDailyLimit(): bool
    {
        $dailyKey = self::DAILY_COUNT_KEY . '_' . date('Y-m-d');
        $dailyCount = Cache::get($dailyKey, 0);
        
        if ($dailyCount >= self::MAX_DAILY_REQUESTS) {
            Log::warning('🚦 ICBC Rate Limit: 每日限制', [
                'type' => 'daily_limit',
                'current_count' => $dailyCount,
                'max_count' => self::MAX_DAILY_REQUESTS,
                'date' => date('Y-m-d')
            ]);
            
            return false;
        }
        
        return true;
    }
    
    /**
     * 记录支付请求
     *
     * @return void
     */
    public static function recordPayment(): void
    {
        $currentTime = time();
        
        // 记录最后支付时间
        Cache::put(self::RATE_LIMIT_KEY, $currentTime, 3600);
        
        // 记录突发计数
        $burstKey = self::RATE_LIMIT_KEY . '_burst_' . floor($currentTime / self::BURST_WINDOW);
        $burstCount = Cache::get($burstKey, 0) + 1;
        Cache::put($burstKey, $burstCount, self::BURST_WINDOW);
        
        // 记录每日计数
        $dailyKey = self::DAILY_COUNT_KEY . '_' . date('Y-m-d');
        $dailyCount = Cache::get($dailyKey, 0) + 1;
        Cache::put($dailyKey, $dailyCount, 86400); // 24小时
        
        Log::info('✅ ICBC Rate Limit: 支付请求已记录', [
            'timestamp' => date('Y-m-d H:i:s', $currentTime),
            'burst_count' => $burstCount,
            'daily_count' => $dailyCount,
            'unix_timestamp' => $currentTime
        ]);
    }
    
    /**
     * 获取下次可支付时间
     *
     * @return array|null 包含时间信息的数组，如果可以立即支付则返回null
     */
    public static function getNextAvailableTime(): ?array
    {
        $currentTime = time();
        $reasons = [];
        $nextTimes = [];
        
        // 检查基本间隔
        $lastPaymentTime = Cache::get(self::RATE_LIMIT_KEY, 0);
        if ($lastPaymentTime && ($currentTime - $lastPaymentTime < self::MIN_INTERVAL)) {
            $nextBasic = $lastPaymentTime + self::MIN_INTERVAL;
            $nextTimes[] = $nextBasic;
            $reasons[] = "基本间隔限制（{$nextBasic}）";
        }
        
        // 检查突发限制
        $burstKey = self::RATE_LIMIT_KEY . '_burst_' . floor($currentTime / self::BURST_WINDOW);
        $burstCount = Cache::get($burstKey, 0);
        if ($burstCount >= self::BURST_LIMIT) {
            $nextBurst = ($currentTime + self::BURST_WINDOW) - ($currentTime % self::BURST_WINDOW);
            $nextTimes[] = $nextBurst;
            $reasons[] = "突发限制（下个窗口）";
        }
        
        // 检查每日限制
        $dailyKey = self::DAILY_COUNT_KEY . '_' . date('Y-m-d');
        $dailyCount = Cache::get($dailyKey, 0);
        if ($dailyCount >= self::MAX_DAILY_REQUESTS) {
            $nextDaily = strtotime('tomorrow 00:00:00');
            $nextTimes[] = $nextDaily;
            $reasons[] = "每日限制（明天重置）";
        }
        
        if (empty($nextTimes)) {
            return null;
        }
        
        // 返回最晚的限制时间
        $nextTime = max($nextTimes);
        
        return [
            'next_time' => $nextTime,
            'next_time_formatted' => date('Y-m-d H:i:s', $nextTime),
            'wait_seconds' => $nextTime - $currentTime,
            'reasons' => $reasons,
            'current_limits' => [
                'last_payment' => $lastPaymentTime ? date('Y-m-d H:i:s', $lastPaymentTime) : null,
                'burst_count' => $burstCount,
                'daily_count' => $dailyCount,
            ]
        ];
    }
    
    /**
     * 获取当前限制状态
     *
     * @return array
     */
    public static function getStatus(): array
    {
        $currentTime = time();
        $lastPaymentTime = Cache::get(self::RATE_LIMIT_KEY, 0);
        
        $burstKey = self::RATE_LIMIT_KEY . '_burst_' . floor($currentTime / self::BURST_WINDOW);
        $burstCount = Cache::get($burstKey, 0);
        
        $dailyKey = self::DAILY_COUNT_KEY . '_' . date('Y-m-d');
        $dailyCount = Cache::get($dailyKey, 0);
        
        return [
            'can_make_payment' => self::canMakePayment(),
            'last_payment_time' => $lastPaymentTime ? date('Y-m-d H:i:s', $lastPaymentTime) : null,
            'time_since_last' => $lastPaymentTime ? ($currentTime - $lastPaymentTime) : null,
            'basic_limit' => [
                'interval_seconds' => self::MIN_INTERVAL,
                'remaining_seconds' => $lastPaymentTime ? max(0, self::MIN_INTERVAL - ($currentTime - $lastPaymentTime)) : 0,
            ],
            'burst_limit' => [
                'max_requests' => self::BURST_LIMIT,
                'current_count' => $burstCount,
                'window_seconds' => self::BURST_WINDOW,
                'window_remaining' => self::BURST_WINDOW - ($currentTime % self::BURST_WINDOW),
            ],
            'daily_limit' => [
                'max_requests' => self::MAX_DAILY_REQUESTS,
                'current_count' => $dailyCount,
                'date' => date('Y-m-d'),
                'remaining_requests' => max(0, self::MAX_DAILY_REQUESTS - $dailyCount),
            ],
            'next_available' => self::getNextAvailableTime(),
        ];
    }
    
    /**
     * 重置所有限制（调试用）
     *
     * @return void
     */
    public static function reset(): void
    {
        $currentTime = time();
        
        // 清除基本间隔缓存
        Cache::forget(self::RATE_LIMIT_KEY);
        
        // 清除当前突发窗口缓存
        $burstKey = self::RATE_LIMIT_KEY . '_burst_' . floor($currentTime / self::BURST_WINDOW);
        Cache::forget($burstKey);
        
        // 清除今日计数缓存
        $dailyKey = self::DAILY_COUNT_KEY . '_' . date('Y-m-d');
        Cache::forget($dailyKey);
        
        Log::info('🔄 ICBC Rate Limit: 所有限制已重置', [
            'reset_time' => date('Y-m-d H:i:s', $currentTime),
            'reset_by' => 'manual_reset'
        ]);
    }
} 