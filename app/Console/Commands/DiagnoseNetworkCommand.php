<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use IcbcPay\Services\IcbcPayService;
use IcbcPay\Models\PaymentRecord;

class DiagnoseNetworkCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'icbc:diagnose {--detail : 显示详细信息}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '诊断工商银行支付系统的网络和配置问题';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 开始诊断工商银行支付系统...');
        $this->newLine();

        $allPassed = true;

        // 1. 检查基础配置
        $this->info('1️⃣ 检查基础配置');
        $configPassed = $this->checkConfiguration();
        $allPassed = $allPassed && $configPassed;
        $this->newLine();

        // 2. 检查数据库连接
        $this->info('2️⃣ 检查数据库连接');
        $dbPassed = $this->checkDatabase();
        $allPassed = $allPassed && $dbPassed;
        $this->newLine();

        // 3. 检查网络连接
        $this->info('3️⃣ 检查网络连接');
        $networkPassed = $this->checkNetwork();
        $allPassed = $allPassed && $networkPassed;
        $this->newLine();

        // 4. 测试支付功能
        $this->info('4️⃣ 测试支付功能');
        $paymentPassed = $this->testPayment();
        $allPassed = $allPassed && $paymentPassed;
        $this->newLine();

        // 5. 检查路由
        $this->info('5️⃣ 检查路由配置');
        $routePassed = $this->checkRoutes();
        $allPassed = $allPassed && $routePassed;
        $this->newLine();

        // 总结
        if ($allPassed) {
            $this->info('✅ 所有检查都通过了！系统运行正常。');
        } else {
            $this->error('❌ 发现问题，请根据上述提示进行修复。');
        }

        return $allPassed ? 0 : 1;
    }

    private function checkConfiguration()
    {
        $passed = true;
        
        $requiredConfigs = [
            'icbc-pay.gateway_url' => '网关地址',
            'icbc-pay.app_id' => '应用ID',
            'icbc-pay.mer_id' => '商户ID',
            'icbc-pay.payment_config.use_ui_mode' => 'UI模式配置'
        ];

        foreach ($requiredConfigs as $key => $name) {
            $value = config($key);
            if (empty($value)) {
                $this->error("   ❌ {$name} 未配置 ({$key})");
                $passed = false;
            } else {
                $this->info("   ✅ {$name}: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value));
            }
        }

        // 检查环境变量
        $envVars = ['ICBC_GATEWAY_URL', 'ICBC_APP_ID', 'ICBC_USE_UI_MODE'];
        foreach ($envVars as $var) {
            $value = env($var);
            if ($this->option('detail')) {
                $this->line("   📋 {$var}: " . ($value ?: '未设置'));
            }
        }

        return $passed;
    }

    private function checkDatabase()
    {
        try {
            DB::connection()->getPdo();
            $this->info('   ✅ 数据库连接正常');

            // 检查支付记录表
            $count = PaymentRecord::count();
            $this->info("   ✅ 支付记录表存在，当前有 {$count} 条记录");

            return true;
        } catch (\Exception $e) {
            $this->error('   ❌ 数据库连接失败: ' . $e->getMessage());
            return false;
        }
    }

    private function checkNetwork()
    {
        $passed = true;
        $gatewayUrl = config('icbc-pay.gateway_url');

        // 测试基础连接
        try {
            $response = Http::timeout(10)->get($gatewayUrl);
            if ($response->successful()) {
                $this->info("   ✅ 基础网络连接正常 ({$gatewayUrl})");
            } else {
                $this->error("   ❌ 基础网络连接失败，状态码: {$response->status()}");
                $passed = false;
            }
        } catch (\Exception $e) {
            $this->error('   ❌ 网络连接异常: ' . $e->getMessage());
            $passed = false;
        }

        // 测试UI接口
        try {
            $uiUrl = $gatewayUrl . config('icbc-pay.api_urls.consume_purchase_ui');
            $response = Http::timeout(10)->get($uiUrl . '?test=1');
            if ($response->successful()) {
                $this->info('   ✅ UI接口连接正常');
            } else {
                $this->warn("   ⚠️ UI接口响应异常，状态码: {$response->status()}");
            }
        } catch (\Exception $e) {
            $this->warn('   ⚠️ UI接口连接异常: ' . $e->getMessage());
        }

        return $passed;
    }

    private function testPayment()
    {
        try {
            // 创建测试支付记录
            $paymentRecord = PaymentRecord::create([
                'out_trade_no' => 'DIAGNOSE_' . time(),
                'total_amount' => 1.00,
                'subject' => '诊断测试',
                'payment_method' => 'wechat',
                'car_number' => '诊断001',
                'status' => 'pending'
            ]);

            $icbcPayService = new IcbcPayService();
            $result = $icbcPayService->createPayment($paymentRecord);

            if ($result['success'] ?? false) {
                $this->info('   ✅ 支付功能测试通过');
                if (isset($result['ui_mode']) && $result['ui_mode']) {
                    $this->info('   ✅ UI模式正常工作');
                }
                if (isset($result['payment_url'])) {
                    $this->info('   ✅ 支付URL生成成功');
                }
            } else {
                $this->error('   ❌ 支付功能测试失败');
                return false;
            }

            // 清理测试数据
            $paymentRecord->delete();
            return true;

        } catch (\Exception $e) {
            $this->error('   ❌ 支付功能测试异常: ' . $e->getMessage());
            if ($this->option('detail')) {
                $this->line('   📋 详细错误: ' . $e->getFile() . ':' . $e->getLine());
            }
            return false;
        }
    }

    private function checkRoutes()
    {
        $routes = [
            'api/pay' => 'POST',
            'debug/icbc-payment' => 'GET',
            'debug/network-page' => 'GET'
        ];

        $passed = true;
        foreach ($routes as $uri => $method) {
            try {
                $route = \Route::getRoutes()->match(
                    \Illuminate\Http\Request::create($uri, $method)
                );
                $this->info("   ✅ 路由存在: {$method} {$uri}");
            } catch (\Exception $e) {
                $this->error("   ❌ 路由不存在: {$method} {$uri}");
                $passed = false;
            }
        }

        return $passed;
    }
}
