<?php

/**
 * 测试工行支付的两种模式：UI模式和API模式
 */

require_once __DIR__ . '/bootstrap/app.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 测试工行支付的两种模式...\n\n";

try {
    // 创建测试订单数据
    $testOrderData = [
        'car_number' => '测试C' . time(),
        'amount' => '1.00',
        'payment_method' => 'wechat',
        'parking_duration' => 60
    ];

    echo "📦 测试订单数据:\n";
    foreach ($testOrderData as $key => $value) {
        echo "   {$key}: {$value}\n";
    }
    echo "\n";

    // 测试1: UI模式（有界面）
    echo "🖥️ 测试1: UI模式（有界面支付）\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    try {
        $controller = app('App\Http\Controllers\ParkingController');
        $request = new Illuminate\Http\Request($testOrderData);
        $response = $controller->createPayment($request);
        
        $responseData = json_decode($response->getContent(), true);
        
        if ($responseData['success']) {
            echo "✅ UI模式支付订单创建成功\n";
            echo "   订单号: " . $responseData['data']['order_id'] . "\n";
            echo "   支付URL: " . $responseData['data']['payment_url'] . "\n";
            
            // 检查是否包含表单HTML
            if (isset($responseData['data']['form_html'])) {
                echo "   表单HTML: 已生成 (" . strlen($responseData['data']['form_html']) . " 字符)\n";
                
                // 检查表单中的关键信息
                $formHtml = $responseData['data']['form_html'];
                if (strpos($formHtml, '/ui/') !== false) {
                    echo "   ✅ 使用UI模式路径 (/ui/)\n";
                } else {
                    echo "   ❌ 未使用UI模式路径\n";
                }
            } else {
                echo "   ❌ 未生成表单HTML\n";
            }
        } else {
            echo "❌ UI模式支付订单创建失败: " . ($responseData['error'] ?? 'Unknown error') . "\n";
        }
    } catch (Exception $e) {
        echo "❌ UI模式测试失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n";

    // 测试2: API模式（无界面）
    echo "🔌 测试2: API模式（无界面支付）\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    try {
        // 修改车牌号避免重复订单
        $testOrderData['car_number'] = '测试D' . time();
        
        // 获取IcbcPayClient实例并测试API模式
        $client = app('IcbcPay\IcbcPayClient');
        
        echo "🔧 测试API模式支付方法...\n";
        $apiResult = $client->payWithApi($testOrderData);
        
        echo "✅ API模式支付调用成功\n";
        echo "   模式: " . ($apiResult['mode'] ?? 'unknown') . "\n";
        echo "   订单号: " . ($apiResult['order_id'] ?? 'unknown') . "\n";
        echo "   支付状态: " . ($apiResult['payment_status'] ?? 'unknown') . "\n";
        echo "   需要跳转: " . ($apiResult['redirect_required'] ? 'Yes' : 'No') . "\n";
        echo "   消息: " . ($apiResult['message'] ?? 'No message') . "\n";
        
    } catch (Exception $e) {
        echo "❌ API模式测试失败: " . $e->getMessage() . "\n";
        echo "   错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    }
    
    echo "\n";

    // 测试3: 自动模式选择
    echo "⚙️ 测试3: 自动模式选择\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    try {
        // 修改车牌号避免重复订单
        $testOrderData['car_number'] = '测试E' . time();
        
        $client = app('IcbcPay\IcbcPayClient');
        
        echo "🔧 测试自动模式选择...\n";
        $autoResult = $client->pay($testOrderData);
        
        echo "✅ 自动模式支付调用成功\n";
        echo "   模式: " . ($autoResult['mode'] ?? 'unknown') . "\n";
        echo "   订单号: " . ($autoResult['order_id'] ?? 'unknown') . "\n";
        echo "   需要跳转: " . ($autoResult['redirect_required'] ? 'Yes' : 'No') . "\n";
        
    } catch (Exception $e) {
        echo "❌ 自动模式测试失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n";

    // 测试4: 配置检查
    echo "📋 测试4: 配置检查\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $config = config('icbc-pay');
    echo "当前配置:\n";
    echo "   环境: " . ($config['environment'] ?? 'unknown') . "\n";
    echo "   支付模式: " . ($config['payment_mode'] ?? 'unknown') . "\n";
    echo "   沙箱网关: " . ($config['gateways']['sandbox']['base_url'] ?? 'unknown') . "\n";
    echo "   UI支付路径: " . ($config['gateways']['sandbox']['ui_payment_url'] ?? 'unknown') . "\n";
    echo "   API支付路径: " . ($config['gateways']['sandbox']['api_payment_url'] ?? 'unknown') . "\n";
    
    echo "\n🎉 所有测试完成！\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
