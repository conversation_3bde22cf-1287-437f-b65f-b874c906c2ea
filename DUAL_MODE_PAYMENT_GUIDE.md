# 🔄 工行支付双模式使用指南

## 📋 概述

本项目现在支持工商银行支付的两种模式：

1. **UI模式（有界面）** - 用户跳转到工行支付页面完成支付
2. **API模式（无界面）** - 直接调用工行API，在商户页面完成支付

## 🎯 两种模式对比

| 特性 | UI模式 | API模式 |
|------|--------|---------|
| **用户体验** | 跳转到工行支付页面 | 在商户页面直接支付 |
| **接口路径** | `/ui/cardbusiness/aggregatepay/...` | `/api/cardbusiness/aggregatepay/...` |
| **客户端类型** | UiIcbcClient | DefaultIcbcClient |
| **返回结果** | HTML表单 | JSON数据 |
| **开发复杂度** | 简单 | 复杂 |
| **安全性** | 高（支付在工行页面） | 需要额外安全措施 |
| **用户体验** | 需要页面跳转 | 无缝支付体验 |

## 🔧 配置说明

### 环境变量配置

在 `.env` 文件中添加：

```env
# 支付模式配置
ICBC_PAYMENT_MODE=ui    # ui: 有界面模式, api: 无界面模式
```

### 配置文件

`config/icbc-pay.php` 中的网关配置：

```php
'gateways' => [
    'sandbox' => [
        'base_url' => 'https://apipcs3.dccnet.com.cn',
        // UI模式（有界面）
        'ui_payment_url' => '/ui/cardbusiness/aggregatepay/b2c/online/ui/consumepurchaseshowpay/V1',
        // API模式（无界面）
        'api_payment_url' => '/api/cardbusiness/aggregatepay/b2c/online/consumepurchase/V1',
        // 兼容性配置
        'payment_url' => '/api/cardbusiness/aggregatepay/b2c/online/consumepurchase/V1',
    ],
],
```

## 💻 使用方法

### 1. 自动模式选择

根据配置自动选择支付模式：

```php
use IcbcPay\IcbcPayClient;

$client = new IcbcPayClient();

$orderData = [
    'order_id' => 'ORDER_' . time(),
    'amount' => '10.00',
    'subject' => '停车费支付',
    'payment_method' => 'wechat',
];

// 根据配置自动选择模式
$result = $client->pay($orderData);
```

### 2. 强制使用UI模式

```php
// 强制使用UI模式（有界面）
$result = $client->payWithUi($orderData);

// 返回结果包含：
// - form_html: 自动提交的HTML表单
// - redirect_required: true
// - mode: 'ui'
```

### 3. 强制使用API模式

```php
// 强制使用API模式（无界面）
$result = $client->payWithApi($orderData);

// 返回结果包含：
// - payment_status: 支付状态
// - trade_no: 交易号
// - redirect_required: false
// - mode: 'api'
```

## 📊 返回结果格式

### UI模式返回结果

```php
[
    'success' => true,
    'mode' => 'ui',
    'order_id' => 'ORDER_123456',
    'payment_url' => 'http://localhost/pay/ORDER_123456',
    'form_html' => '<form>...</form>',
    'redirect_required' => true,
    'message' => '请在跳转的工行支付页面完成支付',
]
```

### API模式返回结果

```php
[
    'success' => true,
    'mode' => 'api',
    'order_id' => 'ORDER_123456',
    'trade_no' => 'ICBC_789012',
    'payment_status' => 'pending',
    'payment_url' => 'https://pay.icbc.com.cn/...',
    'qr_code' => 'data:image/png;base64,...',
    'redirect_required' => false,
    'message' => '支付请求已提交，请查看支付状态',
    'api_response' => [...],
]
```

## 🎮 控制器使用示例

### 在ParkingController中使用

```php
public function createPayment(Request $request)
{
    $orderData = [
        'car_number' => $request->car_number,
        'amount' => $request->amount,
        'payment_method' => $request->payment_method,
        'parking_duration' => $request->parking_duration,
    ];

    $client = app('IcbcPay\IcbcPayClient');
    
    // 根据业务需求选择模式
    if ($request->has('force_ui')) {
        $result = $client->payWithUi($orderData);
    } elseif ($request->has('force_api')) {
        $result = $client->payWithApi($orderData);
    } else {
        $result = $client->pay($orderData); // 自动选择
    }
    
    return response()->json([
        'success' => true,
        'data' => $result,
    ]);
}
```

## 🧪 测试方法

### 1. 运行测试脚本

```bash
php test_payment_modes.php
```

### 2. API测试

```bash
# 测试UI模式
curl -X POST http://localhost:8001/api/pay \
  -H "Content-Type: application/json" \
  -d '{"car_number":"测试UI001","amount":"1.00","payment_method":"wechat","force_ui":true}'

# 测试API模式
curl -X POST http://localhost:8001/api/pay \
  -H "Content-Type: application/json" \
  -d '{"car_number":"测试API001","amount":"1.00","payment_method":"wechat","force_api":true}'
```

### 3. Laravel Tinker测试

```bash
php artisan tinker

# 测试UI模式
$client = app('IcbcPay\IcbcPayClient');
$result = $client->payWithUi(['order_id' => 'TEST_UI_001', 'amount' => '1.00', 'subject' => '测试']);

# 测试API模式
$result = $client->payWithApi(['order_id' => 'TEST_API_001', 'amount' => '1.00', 'subject' => '测试']);
```

## ⚙️ 高级配置

### 1. 动态模式选择

```php
// 根据用户设备类型选择模式
$mode = $request->isMobile() ? 'ui' : 'api';
$result = $client->payWithMode($orderData, $mode);
```

### 2. 支付方式特定配置

```php
// 微信支付使用UI模式，支付宝使用API模式
if ($orderData['payment_method'] === 'wechat') {
    $result = $client->payWithUi($orderData);
} else {
    $result = $client->payWithApi($orderData);
}
```

## 🔍 调试和日志

所有支付操作都会记录详细日志：

```bash
# 查看支付日志
tail -f storage/logs/laravel.log | grep "ICBC"
```

日志标识：
- `🖥️ ICBC UI:` - UI模式相关日志
- `🔌 ICBC API:` - API模式相关日志
- `💳 ICBC PAY:` - 通用支付日志

## 🚀 部署建议

1. **生产环境**：建议使用UI模式，安全性更高
2. **开发环境**：可以使用API模式，便于调试
3. **移动端**：建议使用UI模式，用户体验更好
4. **PC端**：可以根据需求选择任一模式

## 📝 注意事项

1. **网关公钥**：API模式需要配置网关公钥用于验证响应
2. **错误处理**：API模式需要更完善的错误处理机制
3. **安全性**：API模式需要额外的安全验证措施
4. **兼容性**：保持向后兼容，原有代码无需修改

现在您的项目已经支持工行支付的两种模式，可以根据具体需求灵活选择使用！
