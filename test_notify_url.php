<?php

/**
 * 测试notify_url字段是否正确设置
 */

require_once __DIR__ . '/bootstrap/app.php';

use IcbcPay\IcbcPayClient;

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 测试notify_url字段设置...\n\n";

try {
    // 获取配置
    $config = config('icbc-pay');
    
    echo "📋 当前配置:\n";
    echo "   notify_url: " . ($config['notify_url'] ?? 'NULL') . "\n";
    echo "   return_url: " . ($config['return_url'] ?? 'NULL') . "\n";
    echo "   environment: " . ($config['environment'] ?? 'NULL') . "\n\n";
    
    // 创建测试订单数据
    $testOrderData = [
        'order_id' => 'TEST_ORDER_' . time(),
        'amount' => '1.00',
        'subject' => '停车费支付测试',
        'payment_method' => 'wechat',
        'body' => '测试订单 - 车牌：测A12345',
    ];
    
    echo "📦 测试订单数据:\n";
    foreach ($testOrderData as $key => $value) {
        echo "   {$key}: {$value}\n";
    }
    echo "\n";
    
    // 创建支付客户端（使用模拟模式）
    $testConfig = array_merge($config, [
        'dev' => ['mock_enabled' => true]
    ]);
    
    // 创建临时测试私钥文件
    $testPrivateKeyPath = storage_path('keys/test_private_key.pem');
    $testPrivateKey = "-----BEGIN PRIVATE KEY-----\nTEST_PRIVATE_KEY_CONTENT\n-----END PRIVATE KEY-----";
    
    if (!file_exists(dirname($testPrivateKeyPath))) {
        mkdir(dirname($testPrivateKeyPath), 0755, true);
    }
    file_put_contents($testPrivateKeyPath, $testPrivateKey);
    
    $testConfig['private_key_path'] = $testPrivateKeyPath;
    
    // 使用反射来测试buildApiRequest方法
    $client = new IcbcPayClient($testConfig);
    $reflection = new ReflectionClass($client);
    
    // 测试normalizeOrderData方法
    $normalizeMethod = $reflection->getMethod('normalizeOrderData');
    $normalizeMethod->setAccessible(true);
    $normalizedData = $normalizeMethod->invoke($client, $testOrderData);
    
    echo "🔄 标准化后的订单数据:\n";
    foreach ($normalizedData as $key => $value) {
        echo "   {$key}: {$value}\n";
    }
    echo "\n";
    
    // 测试buildApiRequest方法
    $buildApiMethod = $reflection->getMethod('buildApiRequest');
    $buildApiMethod->setAccessible(true);
    $apiRequest = $buildApiMethod->invoke($client, $normalizedData);
    
    echo "🏗️ 构建的API请求:\n";
    echo "   serviceUrl: " . ($apiRequest['serviceUrl'] ?? 'NULL') . "\n";
    echo "   method: " . ($apiRequest['method'] ?? 'NULL') . "\n";
    echo "\n";
    
    echo "📋 业务内容 (biz_content):\n";
    $bizContent = $apiRequest['biz_content'] ?? [];
    foreach ($bizContent as $key => $value) {
        echo "   {$key}: {$value}\n";
    }
    echo "\n";
    
    // 检查关键字段
    $requiredFields = ['notify_url', 'mer_url', 'return_url', 'page_url'];
    echo "✅ 关键字段检查:\n";
    foreach ($requiredFields as $field) {
        $value = $bizContent[$field] ?? '';
        $status = !empty($value) ? '✅' : '❌';
        echo "   {$status} {$field}: " . ($value ?: 'EMPTY') . "\n";
    }
    
    // 清理测试文件
    if (file_exists($testPrivateKeyPath)) {
        unlink($testPrivateKeyPath);
    }
    
    echo "\n🎉 测试完成！\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
