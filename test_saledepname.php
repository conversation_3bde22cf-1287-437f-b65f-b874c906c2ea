<?php

require_once __DIR__ . '/bootstrap/app.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 测试saledepname字段修复...\n\n";

try {
    // 创建测试订单
    $orderData = [
        'car_number' => '测试A12345',
        'amount' => '1.00',
        'payment_method' => 'wechat',
        'parking_duration' => 60
    ];

    echo "📦 创建支付订单...\n";
    
    $controller = app('App\Http\Controllers\ParkingController');
    $request = new Illuminate\Http\Request($orderData);
    $response = $controller->createPayment($request);
    
    $responseData = json_decode($response->getContent(), true);
    
    if (isset($responseData['data']['form_html'])) {
        $formHtml = $responseData['data']['form_html'];
        
        // 提取biz_content
        if (preg_match('/name="biz_content" value="([^"]+)"/', $formHtml, $matches)) {
            $bizContentJson = html_entity_decode($matches[1]);
            $bizContent = json_decode($bizContentJson, true);
            
            echo "📋 检查关键字段:\n";
            $checkFields = ['saledepname', 'body', 'subject', 'notify_url', 'mer_url'];
            foreach ($checkFields as $field) {
                $value = $bizContent[$field] ?? 'MISSING';
                $status = ($value !== 'MISSING' && !empty($value)) ? '✅' : '❌';
                echo "   {$status} {$field}: {$value}\n";
            }
            
            // 特别检查saledepname
            if (isset($bizContent['saledepname']) && !empty($bizContent['saledepname'])) {
                echo "\n🎉 saledepname字段已正确设置！\n";
                echo "   值: " . $bizContent['saledepname'] . "\n";
            } else {
                echo "\n❌ saledepname字段仍然缺失或为空\n";
            }
            
            echo "\n📋 完整的biz_content内容:\n";
            echo json_encode($bizContent, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
            
        } else {
            echo "❌ 无法从表单中提取biz_content\n";
            echo "表单HTML片段:\n" . substr($formHtml, 0, 500) . "...\n";
        }
    } else {
        echo "❌ 响应中没有form_html\n";
        echo "响应内容: " . $response->getContent() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
