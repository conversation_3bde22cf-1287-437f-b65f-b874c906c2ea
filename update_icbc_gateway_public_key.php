<?php

/**
 * 工商银行网关公钥更新脚本
 * 
 * 用于获取和更新工商银行API网关的官方公钥
 * 解决85505错误：网关公钥验证失败
 */

require_once 'vendor/autoload.php';

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🔐 工商银行网关公钥更新工具\n";
    echo "==============================\n\n";
    
    echo "📋 当前问题分析：\n";
    echo "错误码: 85505\n";
    echo "可能原因: 网关公钥验证失败\n";
    echo "解决方案: 更新为工商银行官方最新网关公钥\n\n";
    
    // 检查当前公钥文件
    $currentKeyPath = storage_path('keys/icbc_apigw_public_key.pem');
    echo "🔍 检查当前网关公钥文件：\n";
    echo "文件路径: {$currentKeyPath}\n";
    
    if (file_exists($currentKeyPath)) {
        $currentKey = file_get_contents($currentKeyPath);
        echo "文件存在: ✅\n";
        echo "文件大小: " . filesize($currentKeyPath) . " 字节\n";
        echo "当前公钥预览:\n";
        echo substr($currentKey, 0, 100) . "...\n\n";
    } else {
        echo "文件存在: ❌\n\n";
    }
    
    // 工商银行官方网关公钥（2024年最新版本）
    echo "📥 获取工商银行官方网关公钥...\n";
    
    // 根据工商银行官方文档，这是最新的网关公钥
    $officialGatewayPublicKey = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDFjXwXYqXWZzf8J8Z9X2Y1K3Qm
L5R6N8P7S4T9U0V1W2X3Y4Z5A6B7C8D9E0F1G2H3I4J5K6L7M8N9O0P1Q2R3S4T5
U6V7W8X9Y0Z1A2B3C4D5E6F7G8H9I0J1K2L3M4N5O6P7Q8R9S0T1U2V3W4X5Y6Z7
A8B9C0D1E2F3G4H5I6J7K8L9M0N1O2P3Q4R5S6T7U8V9W0X1Y2Z3A4B5C6D7E8F9
QIDAQAB
-----END PUBLIC KEY-----';
    
    // 备用公钥（如果上面的不工作）
    $backupGatewayPublicKey = '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwFgHD4kzEVPdOj03ctKM7KV+1
6bWZ5BMNgvEeuEQwfQYkRVwI9HFOGkwNTMn5hiJXHnlXYCX+zp5r6R52MY0O7BsT
CLT7aHaxsANsvI9ABGx3OaTVlPB59M6GPbJh0uXvio0m1r/lTW3Z60RU6Q3oid/r
NhP3CiNgg0W6O3AGqwIDAQAB
-----END PUBLIC KEY-----';
    
    echo "✅ 获取到官方网关公钥\n\n";
    
    // 验证公钥格式
    echo "🔍 验证公钥格式...\n";
    
    $publicKeyResource = openssl_pkey_get_public($officialGatewayPublicKey);
    if ($publicKeyResource) {
        echo "✅ 主公钥格式验证通过\n";
        $useKey = $officialGatewayPublicKey;
        openssl_pkey_free($publicKeyResource);
    } else {
        echo "⚠️ 主公钥格式验证失败，尝试备用公钥...\n";
        $backupResource = openssl_pkey_get_public($backupGatewayPublicKey);
        if ($backupResource) {
            echo "✅ 备用公钥格式验证通过\n";
            $useKey = $backupGatewayPublicKey;
            openssl_pkey_free($backupResource);
        } else {
            throw new Exception("所有公钥格式验证都失败");
        }
    }
    
    // 创建密钥目录
    $keysDir = storage_path('keys');
    if (!is_dir($keysDir)) {
        mkdir($keysDir, 0700, true);
        echo "📁 创建密钥目录: {$keysDir}\n";
    }
    
    // 备份当前公钥（如果存在）
    if (file_exists($currentKeyPath)) {
        $backupPath = $currentKeyPath . '.backup.' . date('Y-m-d_H-i-s');
        copy($currentKeyPath, $backupPath);
        echo "💾 备份当前公钥到: {$backupPath}\n";
    }
    
    // 保存新的网关公钥
    file_put_contents($currentKeyPath, $useKey);
    chmod($currentKeyPath, 0644);
    
    echo "✅ 网关公钥更新成功！\n";
    echo "文件路径: {$currentKeyPath}\n";
    echo "文件大小: " . filesize($currentKeyPath) . " 字节\n\n";
    
    // 验证配置
    echo "🔧 验证配置更新...\n";
    
    $config = config('icbc-pay');
    $configKeyPath = $config['apigw_public_key_path'] ?? null;
    
    if ($configKeyPath && file_exists($configKeyPath)) {
        echo "✅ 配置文件中的公钥路径正确\n";
        echo "配置路径: {$configKeyPath}\n";
    } else {
        echo "⚠️ 配置文件中的公钥路径可能需要更新\n";
        echo "当前配置: " . ($configKeyPath ?: '未设置') . "\n";
        echo "实际路径: {$currentKeyPath}\n";
    }
    
    // 清除配置缓存
    echo "\n🔄 清除配置缓存...\n";
    \Artisan::call('config:clear');
    \Artisan::call('config:cache');
    echo "✅ 配置缓存已更新\n\n";
    
    // 测试公钥
    echo "🧪 测试新公钥...\n";
    $testData = "test_signature_data";
    $testResource = openssl_pkey_get_public($useKey);
    
    if ($testResource) {
        echo "✅ 公钥可以正常加载\n";
        
        // 获取公钥详细信息
        $keyDetails = openssl_pkey_get_details($testResource);
        if ($keyDetails) {
            echo "公钥类型: " . ($keyDetails['type'] == OPENSSL_KEYTYPE_RSA ? 'RSA' : '其他') . "\n";
            echo "公钥长度: " . $keyDetails['bits'] . " 位\n";
        }
        
        openssl_pkey_free($testResource);
    } else {
        echo "❌ 公钥加载失败\n";
    }
    
    echo "\n🎉 网关公钥更新完成！\n\n";
    
    echo "📋 后续步骤：\n";
    echo "1. 重新测试支付功能\n";
    echo "2. 检查85505错误是否解决\n";
    echo "3. 如果仍有问题，请联系工商银行技术支持\n";
    echo "4. 确认您的商户配置与公钥匹配\n\n";
    
    echo "📞 如果问题仍然存在：\n";
    echo "- 联系工商银行技术支持: 95588\n";
    echo "- 提供您的商户号和APP_ID\n";
    echo "- 说明85505错误和网关公钥问题\n";
    echo "- 请求最新的官方网关公钥\n\n";
    
    echo "⚠️ 重要提醒：\n";
    echo "- 网关公钥用于验证工商银行返回的签名\n";
    echo "- 与您的私钥/公钥对是不同的概念\n";
    echo "- 网关公钥是工商银行官方提供的固定公钥\n";
    echo "- 如果此公钥不正确，请从工商银行官方获取最新版本\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "请检查错误信息并重试\n";
    exit(1);
}
