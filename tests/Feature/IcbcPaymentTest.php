<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

class IcbcPaymentTest extends TestCase
{
    use RefreshDatabase;

    protected $icbcPayService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->icbcPayService = new IcbcPayService();
    }

    /**
     * 测试创建支付订单
     * 
     * @test
     */
    public function test_create_payment_order()
    {
        // 创建支付记录
        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => 'TEST_' . time() . rand(1000, 9999),
            'total_amount' => 1.00,
            'subject' => '测试停车费支付',
            'payment_method' => 'wechat',
            'car_number' => '测试A12345',
            'parking_duration' => 60,
            'status' => 'pending'
        ]);

        $this->assertInstanceOf(PaymentRecord::class, $paymentRecord);
        $this->assertEquals('pending', $paymentRecord->status);
        $this->assertEquals('测试A12345', $paymentRecord->car_number);
    }

    /**
     * 测试订单号生成唯一性
     * 
     * @test
     */
    public function test_order_no_uniqueness()
    {
        $orderNos = [];
        
        // 生成100个订单号
        for ($i = 0; $i < 100; $i++) {
            $orderNo = PaymentRecord::generateUniqueOrderNo();
            $orderNos[] = $orderNo;
            
            // 短暂延迟确保时间戳不同
            usleep(1000);
        }

        // 检查唯一性
        $uniqueOrderNos = array_unique($orderNos);
        $this->assertEquals(count($orderNos), count($uniqueOrderNos), '订单号应该是唯一的');
    }

    /**
     * 测试支付参数构建
     * 
     * @test
     */
    public function test_payment_parameters()
    {
        // 模拟配置
        config([
            'icbc-pay.app_id' => 'TEST_APP_ID',
            'icbc-pay.mer_id' => 'TEST_MER_ID',
            'icbc-pay.mer_prtcl_no' => 'TEST_PRTCL_NO',
            'icbc-pay.private_key' => $this->getTestPrivateKey(),
            'icbc-pay.wechat.shop_appid' => 'wx_test_appid'
        ]);

        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => 'TEST_ORDER_001',
            'total_amount' => 5.00,
            'subject' => '测试支付',
            'payment_method' => 'wechat',
            'car_number' => '测试B54321',
            'parking_duration' => 120,
            'status' => 'pending'
        ]);

        // 这里可以测试参数构建逻辑
        $this->assertEquals('wechat', $paymentRecord->payment_method);
        $this->assertEquals(5.00, $paymentRecord->total_amount);
    }

    /**
     * 测试支付成功状态更新
     * 
     * @test
     */
    public function test_payment_success_status_update()
    {
        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => 'TEST_SUCCESS_001',
            'total_amount' => 2.50,
            'subject' => '成功测试',
            'payment_method' => 'alipay',
            'car_number' => '测试C67890',
            'status' => 'pending'
        ]);

        // 模拟支付成功
        $paymentRecord->update([
            'status' => 'success',
            'trade_no' => 'ICBC_TEST_TRADE_' . time(),
            'paid_at' => now()
        ]);

        $this->assertEquals('success', $paymentRecord->status);
        $this->assertNotNull($paymentRecord->trade_no);
        $this->assertNotNull($paymentRecord->paid_at);
    }

    /**
     * 测试API路由
     * 
     * @test
     */
    public function test_payment_api_routes()
    {
        // 测试创建支付接口
        $response = $this->postJson('/api/pay', [
            'car_number' => '测试D11111',
            'amount' => 3.00,
            'payment_method' => 'wechat',
            'parking_duration' => 90
        ]);

        // 根据实际实现调整断言
        $this->assertTrue($response->status() == 200 || $response->status() == 409);
    }

    /**
     * 测试回调处理
     * 
     * @test
     */
    public function test_payment_notify_callback()
    {
        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => 'TEST_NOTIFY_001',
            'total_amount' => 1.50,
            'subject' => '回调测试',
            'payment_method' => 'wechat',
            'car_number' => '测试E22222',
            'status' => 'pending'
        ]);

        // 模拟工商银行回调数据
        $callbackData = [
            'biz_content' => json_encode([
                'out_trade_no' => 'TEST_NOTIFY_001',
                'return_code' => '0',
                'return_msg' => 'success',
                'third_trade_no' => 'ICBC_CALLBACK_' . time(),
                'total_amt' => '150', // 1.50元 = 150分
                'msg_id' => 'MSG_' . time()
            ]),
            'sign_type' => 'RSA',
            'sign' => 'test_signature',
            'app_id' => 'TEST_APP_ID',
            'charset' => 'UTF-8',
            'format' => 'json',
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'version' => 'V1'
        ];

        // 发送回调请求
        $response = $this->postJson('/icbc-pay/notify', $callbackData);

        // 由于签名验证可能失败，我们主要测试路由是否正常
        $this->assertTrue(in_array($response->status(), [200, 400]));
    }

    /**
     * 测试金额转换
     * 
     * @test
     */
    public function test_amount_conversion()
    {
        // 测试元转分
        $this->assertEquals('100', bcmul(1.00, 100, 0));
        $this->assertEquals('150', bcmul(1.50, 100, 0));
        $this->assertEquals('1025', bcmul(10.25, 100, 0));
        
        // 测试分转元
        $this->assertEquals('1.00', bcdiv(100, 100, 2));
        $this->assertEquals('1.50', bcdiv(150, 100, 2));
        $this->assertEquals('10.25', bcdiv(1025, 100, 2));
    }

    /**
     * 测试Web页面路由
     * 
     * @test
     */
    public function test_web_page_routes()
    {
        // 测试支付首页
        $response = $this->get('/parking');
        $response->assertStatus(200);

        // 测试支付页面（需要有效订单号）
        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => 'TEST_WEB_001',
            'total_amount' => 2.00,
            'subject' => 'Web测试',
            'payment_method' => 'alipay',
            'car_number' => '测试F33333',
            'status' => 'pending'
        ]);

        $response = $this->get('/pay/' . $paymentRecord->out_trade_no);
        $response->assertStatus(200);

        // 测试结果页面
        $response = $this->get('/payment/result/' . $paymentRecord->out_trade_no . '/success');
        $response->assertStatus(200);
    }

    /**
     * 获取测试用私钥
     * 
     * @return string
     */
    private function getTestPrivateKey()
    {
        return "-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wxfSBo8V9M06lnKjqvDKT6l8iJOB7hVdOYAVzRDdGNaGH5qPQI9J5MRNIzl5L8GD
lRNZ1W8cW1P7fvq8SY6lS6JA8GV2OQfHXxMz0C0L6Wy3v5jA1Z2cE5K7fQ8VwV
V2W5xJ5K4iA9vz6Q2LvDJpH8jOLyV3N3z1Z5Q8vLZ2gJ7Y9K6pW5xL7A5dJ8K
VQeOlZOCYOhMXE7K8Xz5zZ3J5cW6V8C4rL1Y9G2z5dK4zKvCv7vJ5mQ2I4Yz9
n7LcK5p1tQ8o1KdQ5p9J7m4z8v3Q1rKq3Z2xE8bJ5Q9p2m8K5vZ1rQ8oQ5K9M
V2Q8v7z1Z5Q8vLZ2gJ7Y9K6pW5xL7A5dJ8KVQeOlZOCYOhMXE7K8Xz5zZ3J5c
-----END PRIVATE KEY-----";
    }

    /**
     * 测试支付方式映射
     * 
     * @test
     */
    public function test_payment_method_mapping()
    {
        $testCases = [
            'wechat' => ['pay_mode' => '9', 'access_type' => '9'],
            'alipay' => ['pay_mode' => '10', 'access_type' => '8']
        ];

        foreach ($testCases as $method => $expected) {
            $paymentRecord = PaymentRecord::create([
                'out_trade_no' => 'TEST_METHOD_' . strtoupper($method),
                'total_amount' => 1.00,
                'subject' => '方式测试',
                'payment_method' => $method,
                'car_number' => '测试' . strtoupper($method),
                'status' => 'pending'
            ]);

            $this->assertEquals($method, $paymentRecord->payment_method);
        }
    }

    /**
     * 测试并发订单创建
     * 
     * @test
     */
    public function test_concurrent_order_creation()
    {
        $carNumber = '并发测试001';
        
        // 创建第一个订单
        $order1 = PaymentRecord::create([
            'out_trade_no' => PaymentRecord::generateUniqueOrderNo(),
            'total_amount' => 1.00,
            'subject' => '并发测试1',
            'payment_method' => 'wechat',
            'car_number' => $carNumber,
            'status' => 'pending'
        ]);

        // 尝试为同一车牌创建第二个订单（模拟ParkingController中的逻辑）
        $existingOrder = PaymentRecord::where('car_number', $carNumber)
            ->where('status', 'pending')
            ->where('created_at', '>=', now()->subMinutes(5))
            ->first();

        $this->assertNotNull($existingOrder);
        $this->assertEquals($order1->id, $existingOrder->id);
    }

    /**
     * 测试UI模式支付
     * 
     * @test
     */
    public function test_ui_mode_payment()
    {
        // 测试UI模式的逻辑分支，但使用模拟支付避免实际API调用
        config([
            'icbc-pay.payment_config.use_ui_mode' => true
        ]);

        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => 'TEST_UI_' . time(),
            'total_amount' => 15.00,
            'subject' => 'UI模式测试',
            'payment_method' => 'wechat',
            'car_number' => 'UI测试001',
            'status' => 'pending'
        ]);

        // 在测试环境中会进入模拟模式，我们验证返回结果格式
        $result = $this->icbcPayService->createPayment($paymentRecord);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('payment_form', $result);
        
        // 验证支付记录创建成功
        $this->assertEquals('pending', $paymentRecord->status);
        $this->assertEquals('wechat', $paymentRecord->payment_method);
    }

    /**
     * 测试无界面模式支付
     * 
     * @test
     */
    public function test_direct_mode_payment()
    {
        // 测试无界面模式的逻辑分支
        config([
            'icbc-pay.payment_config.use_ui_mode' => false
        ]);

        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => 'TEST_DIRECT_' . time(),
            'total_amount' => 25.00,
            'subject' => '无界面模式测试',
            'payment_method' => 'alipay',
            'car_number' => '直接测试001',
            'status' => 'pending'
        ]);

        // 在测试环境下会进入模拟模式
        $result = $this->icbcPayService->createPayment($paymentRecord);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('payment_params', $result);
        $this->assertArrayHasKey('payment_form', $result);
        
        // 验证支付记录创建成功
        $this->assertEquals('pending', $paymentRecord->status);
        $this->assertEquals('alipay', $paymentRecord->payment_method);
    }

    /**
     * 测试配置检查逻辑
     * 
     * @test
     */
    public function test_configuration_check()
    {
        // 测试配置不完整时的处理
        config([
            'icbc-pay.app_id' => '',  // 空配置
            'icbc-pay.mer_id' => '',
            'icbc-pay.private_key' => ''
        ]);

        $paymentRecord = PaymentRecord::create([
            'out_trade_no' => 'TEST_CONFIG_' . time(),
            'total_amount' => 10.00,
            'subject' => '配置测试',
            'payment_method' => 'wechat',
            'car_number' => '配置测试001',
            'status' => 'pending'
        ]);

        // 配置不完整应该进入模拟模式
        $result = $this->icbcPayService->createPayment($paymentRecord);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('payment_form', $result);
        $this->assertStringContainsString('Mock payment', $result['payment_form']);
    }
} 