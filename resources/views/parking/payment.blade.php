<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳转到工商银行支付</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen flex items-center justify-center">
    <div class="max-w-md mx-auto">
        <!-- 支付信息卡片 -->
        <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
            <!-- 银行Logo和标题 -->
            <div class="mb-6">
                <div class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-university text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">工商银行支付</h1>
                <p class="text-gray-600">正在为您跳转到安全支付页面...</p>
            </div>

            <!-- 订单信息 -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                <h3 class="font-semibold text-gray-800 mb-3 text-center">
                    <i class="fas fa-receipt mr-2"></i>订单信息
                </h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">订单号:</span>
                        <span class="font-mono text-gray-800 text-xs">{{ $paymentRecord->out_trade_no }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">车牌号:</span>
                        <span class="font-semibold text-blue-600">{{ $paymentRecord->car_number }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">支付金额:</span>
                        <span class="font-bold text-red-600 text-lg">¥{{ number_format($paymentRecord->total_amount, 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">支付方式:</span>
                        <span class="text-gray-800">
                            @switch($paymentRecord->payment_method)
                                @case('alipay')
                                    <i class="fab fa-alipay text-blue-500 mr-1"></i>支付宝
                                    @break
                                @case('wechat')
                                    <i class="fab fa-weixin text-green-500 mr-1"></i>微信支付
                                    @break
                                @case('unionpay')
                                    <i class="fas fa-credit-card text-orange-500 mr-1"></i>银联支付
                                    @break
                                @default
                                    <i class="fas fa-credit-card text-gray-500 mr-1"></i>{{ $paymentRecord->payment_method }}
                            @endswitch
                        </span>
                    </div>
                    @if($paymentRecord->parking_duration > 0)
                    <div class="flex justify-between">
                        <span class="text-gray-600">停车时长:</span>
                        <span class="text-gray-800">{{ $paymentRecord->parking_duration }} 分钟</span>
                    </div>
                    @endif
                    <div class="flex justify-between">
                        <span class="text-gray-600">创建时间:</span>
                        <span class="text-gray-800 text-xs">{{ $paymentRecord->created_at->format('Y-m-d H:i:s') }}</span>
                    </div>
                </div>
            </div>

            <!-- 支付状态显示 -->
            <div id="paymentStatus" class="mb-6">
                <div class="relative">
                    <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <i class="fas fa-credit-card text-blue-600 text-xl"></i>
                    </div>
                </div>
                <p class="text-gray-600 mb-2">正在跳转到工商银行安全支付环境...</p>
                <div class="text-sm text-gray-500">
                    页面将在 <span id="countdown" class="font-bold text-blue-600">3</span> 秒后自动跳转
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="space-y-3">
                <button id="payNowBtn" onclick="submitPayment()" 
                        class="w-full bg-gradient-to-r from-red-600 to-red-700 text-white font-semibold py-3 px-6 rounded-lg hover:from-red-700 hover:to-red-800 transition duration-200 disabled:opacity-50">
                    <i class="fas fa-university mr-2"></i>
                    立即跳转到工商银行支付
                </button>
                
                <button id="queryBtn" onclick="queryPaymentStatus()" 
                        class="w-full bg-blue-100 text-blue-700 font-medium py-2 px-6 rounded-lg hover:bg-blue-200 transition duration-200"
                        style="display: none;">
                    <i class="fas fa-search mr-2"></i>
                    查询支付状态
                </button>
                
                <a href="{{ route('parking.index') }}" 
                   class="block w-full bg-gray-100 text-gray-700 font-medium py-3 px-6 rounded-lg hover:bg-gray-200 transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    返回支付首页
                </a>
            </div>

            <!-- 安全提示 -->
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-shield-alt text-yellow-600 text-lg mr-3 mt-1"></i>
                    <div class="text-left">
                        <h4 class="font-semibold text-yellow-800 mb-1">安全提示</h4>
                        <p class="text-sm text-yellow-700">
                            您即将跳转到工商银行官方支付页面，请确认地址栏显示的是工商银行官方域名，保护您的资金安全。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 支付表单容器 -->
    <div id="paymentFormContainer" style="position: absolute; left: -9999px; top: -9999px;">
        {!! $paymentForm !!}
    </div>

    <!-- 错误提示模态框 -->
    <div id="errorModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">支付异常</h3>
                <p id="errorMessage" class="text-gray-600 mb-4"></p>
                <div class="space-y-2">
                    <button onclick="closeErrorModal()" 
                            class="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200">
                        确定
                    </button>
                    <button onclick="retryPayment()" 
                            class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                        重试
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let countdown = 3;
        let paymentSubmitted = false;
        const countdownElement = document.getElementById('countdown');
        const payNowBtn = document.getElementById('payNowBtn');
        const queryBtn = document.getElementById('queryBtn');
        const orderNo = '{{ $orderNo }}';
        
        // 倒计时
        const timer = setInterval(() => {
            countdown--;
            if (countdownElement) {
                countdownElement.textContent = countdown;
            }
            
            if (countdown <= 0) {
                clearInterval(timer);
                submitPayment();
            }
        }, 1000);

        // 提交支付表单
        function submitPayment() {
            if (paymentSubmitted) return;
            
            try {
                // 查找支付表单
                const form = document.querySelector('#paymentFormContainer form');
                if (form) {
                    paymentSubmitted = true;
                    clearInterval(timer);
                    
                    // 更新UI状态
                    updatePaymentStatus('提交中...', 'processing');
                    payNowBtn.disabled = true;
                    
                    // 提交表单
                    form.submit();
                    
                    // 显示查询按钮
                    setTimeout(() => {
                        queryBtn.style.display = 'block';
                    }, 5000);
                    
                } else {
                    throw new Error('支付表单不存在');
                }
            } catch (error) {
                console.error('支付提交失败:', error);
                showError('支付表单加载失败，请刷新页面重试');
            }
        }

        // 查询支付状态
        function queryPaymentStatus() {
            updatePaymentStatus('查询中...', 'querying');
            
            fetch(`/api/pay/query/${orderNo}`, {
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    const status = data.data.status;
                    if (status === 'success') {
                        // 支付成功，跳转到结果页面
                        window.location.href = `/payment/result/${orderNo}`;
                    } else if (status === 'failed') {
                        showError('支付失败，请重新支付');
                    } else {
                        updatePaymentStatus('支付处理中，请稍候...', 'pending');
                    }
                } else {
                    showError(data.message || '查询支付状态失败');
                }
            })
            .catch(error => {
                console.error('查询失败:', error);
                showError('网络异常，请检查网络连接');
            });
        }

        // 更新支付状态显示
        function updatePaymentStatus(message, status) {
            const statusElement = document.getElementById('paymentStatus');
            let iconClass = 'fas fa-credit-card';
            let colorClass = 'text-blue-600';
            
            switch (status) {
                case 'processing':
                    iconClass = 'fas fa-spinner fa-spin';
                    colorClass = 'text-orange-600';
                    break;
                case 'querying':
                    iconClass = 'fas fa-search';
                    colorClass = 'text-purple-600';
                    break;
                case 'success':
                    iconClass = 'fas fa-check';
                    colorClass = 'text-green-600';
                    break;
                case 'failed':
                    iconClass = 'fas fa-times';
                    colorClass = 'text-red-600';
                    break;
            }
            
            statusElement.innerHTML = `
                <div class="mb-4">
                    <i class="${iconClass} ${colorClass} text-3xl"></i>
                </div>
                <p class="text-gray-600">${message}</p>
            `;
        }

        // 显示错误模态框
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorModal').classList.remove('hidden');
            document.getElementById('errorModal').classList.add('flex');
        }

        // 关闭错误模态框
        function closeErrorModal() {
            document.getElementById('errorModal').classList.add('hidden');
            document.getElementById('errorModal').classList.remove('flex');
        }

        // 重试支付
        function retryPayment() {
            closeErrorModal();
            paymentSubmitted = false;
            payNowBtn.disabled = false;
            countdown = 3;
            updatePaymentStatus('准备重试支付...', 'pending');
            
            const timer = setInterval(() => {
                countdown--;
                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    submitPayment();
                }
            }, 1000);
        }

        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('支付页面加载完成', {
                orderNo: orderNo,
                paymentMethod: '{{ $paymentRecord->payment_method }}',
                amount: '{{ $paymentRecord->total_amount }}'
            });
            
            // 检查支付表单是否存在
            const form = document.querySelector('#paymentFormContainer form');
            if (!form) {
                console.error('支付表单未找到');
                showError('支付表单加载失败，请刷新页面重试');
            }
        });

        // 防止用户意外关闭页面
        window.addEventListener('beforeunload', function(e) {
            if (countdown > 0 && !paymentSubmitted) {
                const message = '支付正在处理中，确定要离开页面吗？';
                e.returnValue = message;
                return message;
            }
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && paymentSubmitted) {
                // 页面重新可见时自动查询支付状态
                setTimeout(queryPaymentStatus, 1000);
            }
        });
    </script>
</body>
</html> 