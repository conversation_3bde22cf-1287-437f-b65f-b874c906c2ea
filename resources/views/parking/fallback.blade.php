@extends('layouts.app')

@section('title', '支付服务维护中')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-orange-500 to-red-500 px-6 py-4">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-white text-2xl mr-3"></i>
                <h1 class="text-xl font-bold text-white">支付服务维护中</h1>
            </div>
        </div>

        <div class="p-6">
            <!-- 订单信息 -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-3">订单信息</h2>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-600">订单号:</span>
                        <span class="font-mono text-sm">{{ $paymentRecord->out_trade_no }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">车牌号:</span>
                        <span class="font-semibold">{{ $paymentRecord->car_number }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">停车时长:</span>
                        <span>{{ $paymentRecord->parking_duration }} 分钟</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">支付金额:</span>
                        <span class="text-xl font-bold text-green-600">¥{{ number_format($paymentRecord->total_amount, 2) }}</span>
                    </div>
                </div>
            </div>

            <!-- 状态说明 -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-yellow-600 text-lg mr-3 mt-1"></i>
                    <div>
                        <h3 class="font-semibold text-yellow-800 mb-2">系统维护通知</h3>
                        <p class="text-yellow-700 text-sm mb-2">
                            工商银行支付接口正在维护中，我们正在努力恢复服务。
                        </p>
                        <p class="text-yellow-700 text-sm">
                            预计恢复时间：<strong>30分钟内</strong>
                        </p>
                    </div>
                </div>
            </div>

            <!-- 解决方案 -->
            <div class="space-y-4">
                <h3 class="font-semibold text-gray-800">解决方案：</h3>
                
                <div class="flex flex-col space-y-3">
                    <!-- 重试支付 -->
                    <button onclick="retryPayment()" 
                            class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                        <i class="fas fa-redo-alt mr-2"></i>
                        重试支付
                    </button>

                    <!-- 现金支付 -->
                    <button onclick="cashPayment()" 
                            class="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                        <i class="fas fa-money-bill-wave mr-2"></i>
                        现金支付
                    </button>

                    <!-- 联系客服 -->
                    <button onclick="contactSupport()" 
                            class="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                        <i class="fas fa-phone mr-2"></i>
                        联系客服
                    </button>

                    <!-- 返回首页 -->
                    <a href="{{ route('parking.index') }}" 
                       class="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-4 rounded-lg transition duration-200 flex items-center justify-center text-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        返回首页
                    </a>
                </div>
            </div>

            <!-- 技术人员信息 -->
            @if(config('app.debug'))
            <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <details>
                    <summary class="cursor-pointer text-red-700 font-semibold">🔧 技术人员信息</summary>
                    <div class="mt-2 text-sm text-red-600">
                        <p><strong>问题类型:</strong> 工商银行证书配置问题</p>
                        <p><strong>订单状态:</strong> {{ $paymentRecord->status }}</p>
                        <p><strong>创建时间:</strong> {{ $paymentRecord->created_at }}</p>
                        <p><strong>解决方案:</strong></p>
                        <ul class="list-disc list-inside ml-4 mt-1">
                            <li>检查工商银行私钥和公钥是否匹配</li>
                            <li>确认APP_ID与商户证书对应</li>
                            <li>验证商户号和协议号是否正确</li>
                            <li>联系工商银行技术支持</li>
                        </ul>
                    </div>
                </details>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- 自动重试计时器 -->
<div id="autoRetryTimer" class="fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg hidden">
    <div class="flex items-center">
        <i class="fas fa-clock mr-2"></i>
        <span id="countdown">30</span>秒后自动重试
    </div>
</div>

<script>
let retryCount = 0;
const maxRetries = 3;

// 重试支付
function retryPayment() {
    if (retryCount >= maxRetries) {
        alert('已达到最大重试次数，请联系客服或使用其他支付方式');
        return;
    }

    retryCount++;
    
    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>重试中...';
    btn.disabled = true;

    // 重新发起支付请求
    fetch('/api/pay', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            car_number: '{{ $paymentRecord->car_number }}',
            amount: {{ $paymentRecord->total_amount }},
            payment_method: 'wechat'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && !data.fallback_mode) {
            // 支付恢复正常，跳转到支付页面
            window.location.href = data.payment_url || `/pay/${data.out_trade_no}`;
        } else {
            // 仍然是降级模式
            alert('支付服务仍在维护中，请稍后再试');
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    })
    .catch(error => {
        console.error('重试支付失败:', error);
        alert('重试失败，请检查网络连接');
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 现金支付
function cashPayment() {
    if (confirm('确认使用现金支付？将标记订单为已完成。')) {
        fetch(`/api/pay/cash/${encodeURIComponent('{{ $paymentRecord->out_trade_no }}')}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('现金支付记录已保存');
                window.location.href = `/payment/result/{{ $orderNo }}`;
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('现金支付失败:', error);
            alert('操作失败，请重试');
        });
    }
}

// 联系客服
function contactSupport() {
    const message = `需要帮助处理支付问题：
订单号：{{ $paymentRecord->out_trade_no }}
车牌号：{{ $paymentRecord->car_number }}
金额：¥{{ number_format($paymentRecord->total_amount, 2) }}
问题：工商银行支付接口暂时不可用`;

    // 这里可以集成实际的客服系统
    if (confirm('是否拨打客服电话？')) {
        window.open('tel:************');
    }
}

// 自动重试计时器
let countdownTimer;
let countdownValue = 30;

function startAutoRetry() {
    document.getElementById('autoRetryTimer').classList.remove('hidden');
    
    countdownTimer = setInterval(() => {
        countdownValue--;
        document.getElementById('countdown').textContent = countdownValue;
        
        if (countdownValue <= 0) {
            clearInterval(countdownTimer);
            document.getElementById('autoRetryTimer').classList.add('hidden');
            retryPayment();
        }
    }, 1000);
}

// 页面加载完成后启动自动重试计时器
document.addEventListener('DOMContentLoaded', function() {
    // 如果重试次数未达到上限，启动自动重试
    if (retryCount < maxRetries) {
        setTimeout(startAutoRetry, 5000); // 5秒后开始倒计时
    }
});

// 页面可见性变化时暂停/恢复计时器
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        clearInterval(countdownTimer);
    } else if (countdownValue > 0 && retryCount < maxRetries) {
        startAutoRetry();
    }
});
</script>
@endsection 