<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付结果 - 停车费支付系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen flex items-center justify-center">
    <div class="max-w-lg mx-auto p-4">
        <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
            
            @if($status === 'success')
                <!-- 支付成功 -->
                <div class="mb-6">
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check-circle text-green-600 text-4xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-green-600 mb-2">支付成功！</h1>
                    <p class="text-gray-600">您的停车费已成功支付</p>
                </div>

                <!-- 成功信息 -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div class="text-left space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">支付金额:</span>
                            <span class="font-bold text-green-600 text-xl">¥{{ number_format($paymentRecord->total_amount, 2) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">车牌号:</span>
                            <span class="font-semibold text-gray-800">{{ $paymentRecord->car_number }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">订单号:</span>
                            <span class="font-mono text-sm text-gray-800">{{ $paymentRecord->out_trade_no }}</span>
                        </div>
                        @if($paymentRecord->trade_no)
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">交易号:</span>
                            <span class="font-mono text-sm text-gray-800">{{ $paymentRecord->trade_no }}</span>
                        </div>
                        @endif
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">支付时间:</span>
                            <span class="text-gray-800">{{ $paymentRecord->paid_at ? $paymentRecord->paid_at->format('Y-m-d H:i:s') : '刚刚' }}</span>
                        </div>
                    </div>
                </div>

                <!-- 成功提示 -->
                <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-600 text-lg mr-3 mt-1"></i>
                        <div class="text-left">
                            <h4 class="font-semibold text-blue-800 mb-1">温馨提示</h4>
                            <p class="text-sm text-blue-700">
                                请保存好此页面截图作为支付凭证。如有疑问，请联系停车场管理员。
                            </p>
                        </div>
                    </div>
                </div>

            @elseif($status === 'failed')
                <!-- 支付失败 -->
                <div class="mb-6">
                    <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-times-circle text-red-600 text-4xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-red-600 mb-2">支付失败</h1>
                    <p class="text-gray-600">支付过程中出现问题，请重试</p>
                </div>

                <!-- 失败信息 -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="text-left space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">订单号:</span>
                            <span class="font-mono text-sm text-gray-800">{{ $paymentRecord->out_trade_no }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">车牌号:</span>
                            <span class="font-semibold text-gray-800">{{ $paymentRecord->car_number }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">支付金额:</span>
                            <span class="font-bold text-gray-800">¥{{ number_format($paymentRecord->total_amount, 2) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">失败时间:</span>
                            <span class="text-gray-800">{{ now()->format('Y-m-d H:i:s') }}</span>
                        </div>
                    </div>
                </div>

            @elseif($status === 'pending')
                <!-- 支付处理中 -->
                <div class="mb-6">
                    <div class="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-hourglass-half text-yellow-600 text-4xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-yellow-600 mb-2">支付处理中</h1>
                    <p class="text-gray-600">您的支付正在处理中，请稍候</p>
                </div>

                <!-- 处理中信息 -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="text-left space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">订单号:</span>
                            <span class="font-mono text-sm text-gray-800">{{ $paymentRecord->out_trade_no }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">车牌号:</span>
                            <span class="font-semibold text-gray-800">{{ $paymentRecord->car_number }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">支付金额:</span>
                            <span class="font-bold text-gray-800">¥{{ number_format($paymentRecord->total_amount, 2) }}</span>
                        </div>
                    </div>
                </div>

            @else
                <!-- 未知状态 -->
                <div class="mb-6">
                    <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-question-circle text-gray-600 text-4xl"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-600 mb-2">状态未知</h1>
                    <p class="text-gray-600">无法确定支付状态，请查询订单</p>
                </div>
            @endif

            <!-- 操作按钮 -->
            <div class="space-y-3">
                @if($status === 'success')
                    <a href="/parking" 
                       class="block w-full bg-gradient-to-r from-green-600 to-green-700 text-white font-semibold py-3 px-6 rounded-lg hover:from-green-700 hover:to-green-800 transition duration-200">
                        <i class="fas fa-home mr-2"></i>
                        返回首页
                    </a>
                    
                    <button onclick="printReceipt()" 
                            class="w-full bg-blue-600 text-white font-medium py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-200">
                        <i class="fas fa-print mr-2"></i>
                        打印凭证
                    </button>

                @elseif($status === 'failed')
                    <a href="/pay/{{ $paymentRecord->out_trade_no }}" 
                       class="block w-full bg-gradient-to-r from-red-600 to-red-700 text-white font-semibold py-3 px-6 rounded-lg hover:from-red-700 hover:to-red-800 transition duration-200">
                        <i class="fas fa-redo mr-2"></i>
                        重新支付
                    </a>

                @elseif($status === 'pending')
                    <button onclick="checkStatus()" 
                            class="w-full bg-gradient-to-r from-yellow-600 to-yellow-700 text-white font-semibold py-3 px-6 rounded-lg hover:from-yellow-700 hover:to-yellow-800 transition duration-200">
                        <i class="fas fa-sync mr-2"></i>
                        刷新状态
                    </button>

                @else
                    <button onclick="checkStatus()" 
                            class="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold py-3 px-6 rounded-lg hover:from-blue-700 hover:to-blue-800 transition duration-200">
                        <i class="fas fa-search mr-2"></i>
                        查询状态
                    </button>
                @endif

                <a href="/parking" 
                   class="block w-full bg-gray-100 text-gray-700 font-medium py-3 px-6 rounded-lg hover:bg-gray-200 transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    返回支付首页
                </a>
            </div>

            <!-- 联系信息 -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="font-semibold text-gray-800 mb-2">需要帮助？</h4>
                <p class="text-sm text-gray-600 mb-2">
                    如果遇到问题，请联系停车场管理员
                </p>
                <div class="text-sm text-gray-500">
                    <div>客服热线: 400-1234-5678</div>
                    <div>工作时间: 24小时</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 打印凭证
        function printReceipt() {
            window.print();
        }

        // 检查支付状态
        function checkStatus() {
            const orderNo = '{{ $paymentRecord->out_trade_no }}';
            
            fetch(`/api/pay/query/${orderNo}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // 如果状态发生变化，刷新页面
                        const currentStatus = '{{ $status }}';
                        if (result.data.status !== currentStatus) {
                            window.location.reload();
                        } else {
                            alert('支付状态未发生变化，当前状态：' + getStatusText(result.data.status));
                        }
                    } else {
                        alert('查询失败：' + result.message);
                    }
                })
                .catch(error => {
                    console.error('查询失败:', error);
                    alert('查询失败，请重试');
                });
        }

        // 获取状态文字
        function getStatusText(status) {
            const statusTexts = {
                'pending': '待支付',
                'success': '支付成功',
                'failed': '支付失败',
                'closed': '已关闭'
            };
            return statusTexts[status] || status;
        }

        @if($status === 'pending')
        // 如果是处理中状态，每10秒自动检查一次
        setInterval(function() {
            checkStatus();
        }, 10000);
        @endif

        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('支付结果页面加载完成，状态：{{ $status }}');
        });
    </script>
</body>
</html> 