<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车费支付系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .payment-method {
            transition: all 0.3s ease;
        }
        .payment-method:hover {
            transform: translateY(-2px);
        }
        .payment-method.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- 头部 -->
    <header class="bg-white shadow-lg">
        <div class="max-w-4xl mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-car text-blue-600 text-5xl"></i>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800">中石油停车费支付系统</h1>
                        <p class="text-gray-600">快速便捷的停车费缴纳服务</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2 text-sm text-gray-600">
                    <i class="fas fa-shield-alt text-red-500"></i>
                    <span>工商银行安全支付</span>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-4xl mx-auto px-4 py-8">
        <!-- 支付表单 -->
        <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">停车费支付</h2>
                <p class="text-gray-600">请填写您的车牌号码和停车信息</p>
            </div>

            <form id="paymentForm" class="space-y-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <!-- 车牌号输入 -->
                    <div class="space-y-2">
                        <label for="carNumber" class="block text-sm font-semibold text-gray-700">
                            <i class="fas fa-car mr-2"></i>车牌号码
                        </label>
                        <input type="text" 
                               id="carNumber" 
                               name="car_number" 
                               placeholder="例：京A12345"
                               maxlength="8"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 text-center text-lg font-bold uppercase"
                               required>
                        <p class="text-xs text-gray-500">请输入完整的车牌号码</p>
                    </div>

                    <!-- 停车金额 -->
                    <div class="space-y-2">
                        <label for="amount" class="block text-sm font-semibold text-gray-700">
                            <i class="fas fa-money-bill-wave mr-2"></i>支付金额
                        </label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-lg">¥</span>
                            <input type="number" 
                                   id="amount" 
                                   name="amount" 
                                   placeholder="0.00"
                                   step="0.01"
                                   min="0.01"
                                   max="9999.99"
                                   class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 text-lg font-semibold"
                                   required>
                        </div>
                        <p class="text-xs text-gray-500">停车费金额，最小0.01元</p>
                    </div>
                </div>

                <!-- 停车时长（可选） -->
                <div class="space-y-2">
                    <label for="parkingDuration" class="block text-sm font-semibold text-gray-700">
                        <i class="fas fa-clock mr-2"></i>停车时长（可选）
                    </label>
                    <select id="parkingDuration" 
                            name="parking_duration" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200">
                        <option value="">请选择停车时长</option>
                        <option value="30">30分钟</option>
                        <option value="60">1小时</option>
                        <option value="120">2小时</option>
                        <option value="180">3小时</option>
                        <option value="240">4小时</option>
                        <option value="480">8小时</option>
                        <option value="720">12小时</option>
                        <option value="1440">24小时</option>
                    </select>
                </div>

                <!-- 支付方式选择 -->
                <div class="space-y-4">
                    <label class="block text-sm font-semibold text-gray-700">
                        <i class="fas fa-credit-card mr-2"></i>选择支付方式
                    </label>
                    <div class="grid md:grid-cols-3 gap-4">
                        <!-- 微信支付 -->
                        <div class="payment-method border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md" 
                             data-method="wechat">
                            <div class="flex items-center justify-center space-x-3">
                                <i class="fab fa-weixin text-green-500 text-2xl"></i>
                                <div>
                                    <h3 class="font-semibold text-gray-800">微信支付</h3>
                                    <p class="text-sm text-gray-600">使用微信扫码支付</p>
                                </div>
                            </div>
                            <input type="radio" name="payment_method" value="wechat" class="hidden">
                        </div>

                        <!-- 支付宝 -->
                        <div class="payment-method border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md" 
                             data-method="alipay">
                            <div class="flex items-center justify-center space-x-3">
                                <i class="fab fa-alipay text-blue-500 text-2xl"></i>
                                <div>
                                    <h3 class="font-semibold text-gray-800">支付宝</h3>
                                    <p class="text-sm text-gray-600">使用支付宝扫码支付</p>
                                </div>
                            </div>
                            <input type="radio" name="payment_method" value="alipay" class="hidden">
                        </div>

                        <!-- 银联支付 -->
                        <div class="payment-method border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md" 
                             data-method="unionpay">
                            <div class="flex items-center justify-center space-x-3">
                                <i class="fas fa-credit-card text-orange-500 text-2xl"></i>
                                <div>
                                    <h3 class="font-semibold text-gray-800">银联支付</h3>
                                    <p class="text-sm text-gray-600">使用银联卡支付</p>
                                </div>
                            </div>
                            <input type="radio" name="payment_method" value="unionpay" class="hidden">
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="flex space-x-4">
                    <button type="submit" 
                            id="submitBtn"
                            class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold py-4 px-8 rounded-lg hover:from-blue-700 hover:to-blue-800 transition duration-200 disabled:opacity-50"
                            disabled>
                        <i class="fas fa-credit-card mr-2"></i>
                        立即支付
                    </button>
                    
                    <button type="button" 
                            onclick="resetForm()"
                            class="px-8 py-4 bg-gray-100 text-gray-700 font-semibold rounded-lg hover:bg-gray-200 transition duration-200">
                        <i class="fas fa-redo mr-2"></i>
                        重置
                    </button>
                </div>
            </form>
        </div>

        <!-- 支付记录查询 -->
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <h3 class="text-xl font-bold text-gray-800 mb-4">
                <i class="fas fa-search mr-2"></i>查询支付记录
            </h3>
            <div class="flex space-x-4">
                <input type="text" 
                       id="queryOrderNo" 
                       placeholder="请输入订单号"
                       class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200">
                <button onclick="queryPayment()" 
                        class="px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 transition duration-200">
                    <i class="fas fa-search mr-2"></i>查询
                </button>
            </div>
            <div id="queryResult" class="mt-4 hidden"></div>
        </div>
    </main>

    <!-- 加载提示模态框 -->
    <div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-sm mx-4 text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">处理中...</h3>
            <p class="text-gray-600">正在创建支付订单，请稍候</p>
        </div>
    </div>

    <!-- 错误提示模态框 -->
    <div id="errorModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-4"></i>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">支付失败</h3>
                <p id="errorMessage" class="text-gray-600 mb-4"></p>
                <button onclick="closeErrorModal()" 
                        class="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700">
                    确定
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedPaymentMethod = null;
        const submitBtn = document.getElementById('submitBtn');
        
        // 支付方式选择处理
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                // 移除之前的选中状态
                document.querySelectorAll('.payment-method').forEach(m => {
                    m.classList.remove('selected');
                    m.querySelector('input[type="radio"]').checked = false;
                });
                
                // 设置当前选中状态
                this.classList.add('selected');
                this.querySelector('input[type="radio"]').checked = true;
                selectedPaymentMethod = this.dataset.method;
                
                // 检查是否可以提交
                checkFormValidation();
            });
        });

        // 表单输入监听
        document.getElementById('carNumber').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
            checkFormValidation();
        });

        document.getElementById('amount').addEventListener('input', checkFormValidation);

        // 检查表单验证
        function checkFormValidation() {
            const carNumber = document.getElementById('carNumber').value.trim();
            const amount = parseFloat(document.getElementById('amount').value);
            
            const isValid = carNumber.length >= 6 && 
                           amount >= 0.01 && 
                           amount <= 9999.99 && 
                           selectedPaymentMethod;
            
            submitBtn.disabled = !isValid;
        }

        // 表单提交处理
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (submitBtn.disabled) return;
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            // 显示加载提示
            showLoadingModal();
            
            // 发送支付请求
            fetch('/api/pay', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                hideLoadingModal();
                
                if (data.success) {
                    // 跳转到支付页面
                    const orderNo = data.data.order_id || data.data.payment_url.split('/').pop();
                    window.location.href = `/pay/${orderNo}`;
                } else {
                    showError(data.message || data.error || '支付创建失败');
                }
            })
            .catch(error => {
                hideLoadingModal();
                console.error('支付请求失败:', error);
                showError('网络异常，请检查网络连接后重试');
            });
        });

        // 查询支付状态
        function queryPayment() {
            const orderNo = document.getElementById('queryOrderNo').value.trim();
            if (!orderNo) {
                alert('请输入订单号');
                return;
            }
            
            fetch(`/api/pay/query/${orderNo}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('queryResult');
                resultDiv.classList.remove('hidden');
                
                if (data.success && data.data) {
                    const payment = data.data;
                    resultDiv.innerHTML = `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 mb-2">查询结果</h4>
                            <div class="space-y-1 text-sm">
                                <div><span class="text-gray-600">订单号:</span> <span class="font-mono">${payment.out_trade_no}</span></div>
                                <div><span class="text-gray-600">车牌号:</span> <span class="font-semibold">${payment.car_number}</span></div>
                                <div><span class="text-gray-600">金额:</span> <span class="text-red-600 font-bold">¥${payment.total_amount}</span></div>
                                <div><span class="text-gray-600">状态:</span> <span class="badge ${payment.status === 'success' ? 'text-green-600' : payment.status === 'failed' ? 'text-red-600' : 'text-yellow-600'}">${getStatusText(payment.status)}</span></div>
                                <div><span class="text-gray-600">创建时间:</span> ${payment.created_at}</div>
                                ${payment.paid_at ? `<div><span class="text-gray-600">支付时间:</span> ${payment.paid_at}</div>` : ''}
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <p class="text-red-600">${data.message || '查询失败'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('查询失败:', error);
                document.getElementById('queryResult').innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <p class="text-red-600">网络异常，查询失败</p>
                    </div>
                `;
                document.getElementById('queryResult').classList.remove('hidden');
            });
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待支付',
                'success': '支付成功',
                'failed': '支付失败',
                'refunded': '已退款'
            };
            return statusMap[status] || status;
        }

        // 重置表单
        function resetForm() {
            document.getElementById('paymentForm').reset();
            document.querySelectorAll('.payment-method').forEach(m => {
                m.classList.remove('selected');
                m.querySelector('input[type="radio"]').checked = false;
            });
            selectedPaymentMethod = null;
            submitBtn.disabled = true;
            document.getElementById('queryResult').classList.add('hidden');
        }

        // 显示加载模态框
        function showLoadingModal() {
            document.getElementById('loadingModal').classList.remove('hidden');
            document.getElementById('loadingModal').classList.add('flex');
        }

        // 隐藏加载模态框
        function hideLoadingModal() {
            document.getElementById('loadingModal').classList.add('hidden');
            document.getElementById('loadingModal').classList.remove('flex');
        }

        // 显示错误模态框
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorModal').classList.remove('hidden');
            document.getElementById('errorModal').classList.add('flex');
        }

        // 关闭错误模态框
        function closeErrorModal() {
            document.getElementById('errorModal').classList.add('hidden');
            document.getElementById('errorModal').classList.remove('flex');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('停车费支付系统已加载');
            
            // 预填充测试数据（仅在开发环境）
            @if(app()->environment(['local', 'development']))
            document.getElementById('carNumber').value = '京A12345';
            document.getElementById('amount').value = '0.01';
            document.querySelector('[data-method="wechat"]').click();
            @endif
        });
    </script>
</body>
</html> 