<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户端检测测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-2xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">
                <i class="fas fa-mobile-alt mr-2"></i>客户端检测测试
            </h1>
            
            <!-- 检测结果显示 -->
            <div id="detectionResult" class="mb-6">
                <!-- 动态内容 -->
            </div>
            
            <!-- 用户代理信息 -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 class="font-semibold text-gray-700 mb-2">用户代理信息</h3>
                <p id="userAgent" class="text-sm text-gray-600 break-all"></p>
            </div>
            
            <!-- 检测详情 -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 class="font-semibold text-gray-700 mb-2">检测详情</h3>
                <div id="detectionDetails" class="space-y-2 text-sm">
                    <!-- 动态内容 -->
                </div>
            </div>
            
            <!-- 手动测试按钮 -->
            <div class="space-y-4">
                <h3 class="font-semibold text-gray-700">手动测试</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="simulateWechat()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        <i class="fab fa-weixin mr-2"></i>模拟微信
                    </button>
                    <button onclick="simulateAlipay()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        <i class="fab fa-alipay mr-2"></i>模拟支付宝
                    </button>
                    <button onclick="resetDetection()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                        <i class="fas fa-redo mr-2"></i>重置检测
                    </button>
                </div>
            </div>
            
            <!-- 返回链接 -->
            <div class="mt-6 pt-6 border-t">
                <a href="/parking" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-2"></i>返回支付页面
                </a>
            </div>
        </div>
    </div>

    <script>
        let originalUserAgent = navigator.userAgent;
        
        /**
         * 检测客户端类型
         */
        function detectClientType(customUserAgent = null) {
            const userAgent = (customUserAgent || navigator.userAgent).toLowerCase();
            
            if (userAgent.includes('micromessenger')) {
                return 'wechat';
            }
            
            if (userAgent.includes('alipayclient') || userAgent.includes('alipay') || userAgent.includes('aliapp')) {
                return 'alipay';
            }
            
            return null;
        }
        
        /**
         * 更新检测结果显示
         */
        function updateDetectionResult(customUserAgent = null) {
            const userAgent = customUserAgent || navigator.userAgent;
            const detectedClient = detectClientType(customUserAgent);
            const resultDiv = document.getElementById('detectionResult');
            const detailsDiv = document.getElementById('detectionDetails');
            
            // 更新用户代理显示
            document.getElementById('userAgent').textContent = userAgent;
            
            // 更新检测结果
            if (detectedClient === 'wechat') {
                resultDiv.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-weixin text-green-500 text-3xl"></i>
                            <div>
                                <h3 class="font-bold text-green-800">检测到微信客户端</h3>
                                <p class="text-green-600">将自动使用微信支付</p>
                            </div>
                        </div>
                    </div>
                `;
            } else if (detectedClient === 'alipay') {
                resultDiv.innerHTML = `
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <i class="fab fa-alipay text-blue-500 text-3xl"></i>
                            <div>
                                <h3 class="font-bold text-blue-800">检测到支付宝客户端</h3>
                                <p class="text-blue-600">将自动使用支付宝支付</p>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-globe text-yellow-500 text-3xl"></i>
                            <div>
                                <h3 class="font-bold text-yellow-800">普通浏览器</h3>
                                <p class="text-yellow-600">需要手动选择支付方式</p>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // 更新检测详情
            detailsDiv.innerHTML = `
                <div><strong>检测结果:</strong> ${detectedClient || '无'}</div>
                <div><strong>包含 micromessenger:</strong> ${userAgent.toLowerCase().includes('micromessenger') ? '是' : '否'}</div>
                <div><strong>包含 alipayclient:</strong> ${userAgent.toLowerCase().includes('alipayclient') ? '是' : '否'}</div>
                <div><strong>包含 alipay:</strong> ${userAgent.toLowerCase().includes('alipay') ? '是' : '否'}</div>
                <div><strong>包含 aliapp:</strong> ${userAgent.toLowerCase().includes('aliapp') ? '是' : '否'}</div>
            `;
        }
        
        /**
         * 模拟微信客户端
         */
        function simulateWechat() {
            const mockUserAgent = 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36 MicroMessenger/8.0.1.1841(0x28000151) Process/tools WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64';
            updateDetectionResult(mockUserAgent);
        }
        
        /**
         * 模拟支付宝客户端
         */
        function simulateAlipay() {
            const mockUserAgent = 'Mozilla/5.0 (Linux; U; Android 10; zh-CN; SM-G975F Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/78.0.3904.108 UCBrowser/13.0.8.1290 Mobile Safari/537.36 AlipayClient/10.2.0.7000';
            updateDetectionResult(mockUserAgent);
        }
        
        /**
         * 重置检测
         */
        function resetDetection() {
            updateDetectionResult();
        }
        
        // 页面加载时执行检测
        document.addEventListener('DOMContentLoaded', function() {
            updateDetectionResult();
        });
    </script>
</body>
</html>
