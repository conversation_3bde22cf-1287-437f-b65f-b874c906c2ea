<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .loading { border-color: #ffc107; background: #fff3cd; }
    </style>
</head>
<body>
    <h1>🔍 工商银行支付网络测试</h1>
    
    <div class="test-section">
        <h3>1. 基础配置检查</h3>
        <button class="test-button" onclick="checkConfig()">检查配置</button>
        <div id="config-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 网络连接测试</h3>
        <button class="test-button" onclick="testNetwork()">测试网络连接</button>
        <div id="network-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 支付订单创建测试</h3>
        <button class="test-button" onclick="testPaymentCreation()">创建测试订单</button>
        <div id="payment-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 模拟用户支付流程</h3>
        <form onsubmit="simulatePayment(event)">
            <div style="margin: 10px 0;">
                <label>车牌号: </label>
                <input type="text" id="car_number" value="测试A12345" required>
            </div>
            <div style="margin: 10px 0;">
                <label>金额: </label>
                <input type="number" id="amount" value="1.00" step="0.01" min="0.01" required>
            </div>
            <div style="margin: 10px 0;">
                <label>支付方式: </label>
                <select id="payment_method">
                    <option value="wechat">微信支付</option>
                    <option value="alipay">支付宝支付</option>
                </select>
            </div>
            <button type="submit" class="test-button">模拟支付</button>
        </form>
        <div id="simulate-result" class="result"></div>
    </div>

    <script>
        // 通用的AJAX请求函数
        function makeRequest(url, method = 'GET', data = null, resultElementId = null) {
            if (resultElementId) {
                const resultElement = document.getElementById(resultElementId);
                resultElement.textContent = '正在请求...';
                resultElement.className = 'result loading';
            }

            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.content || '',
                },
            };

            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }

            return fetch(url, options)
                .then(response => {
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        return response.text().then(text => ({
                            status: response.status,
                            statusText: response.statusText,
                            body: text
                        }));
                    }
                })
                .then(data => {
                    if (resultElementId) {
                        const resultElement = document.getElementById(resultElementId);
                        resultElement.textContent = JSON.stringify(data, null, 2);
                        resultElement.className = 'result success';
                    }
                    return data;
                })
                .catch(error => {
                    console.error('Request failed:', error);
                    if (resultElementId) {
                        const resultElement = document.getElementById(resultElementId);
                        resultElement.textContent = '错误: ' + error.message;
                        resultElement.className = 'result error';
                    }
                    throw error;
                });
        }

        // 检查配置
        function checkConfig() {
            makeRequest('/debug/icbc-payment', 'GET', null, 'config-result');
        }

        // 测试网络连接
        function testNetwork() {
            makeRequest('/debug/network-test', 'GET', null, 'network-result');
        }

        // 测试支付订单创建
        function testPaymentCreation() {
            const testData = {
                car_number: '测试' + Date.now(),
                amount: 1.00,
                payment_method: 'wechat',
                parking_duration: 60
            };

            makeRequest('/api/pay', 'POST', testData, 'payment-result');
        }

        // 模拟用户支付流程
        function simulatePayment(event) {
            event.preventDefault();
            
            const formData = {
                car_number: document.getElementById('car_number').value,
                amount: parseFloat(document.getElementById('amount').value),
                payment_method: document.getElementById('payment_method').value,
                parking_duration: 60
            };

            makeRequest('/api/pay', 'POST', formData, 'simulate-result');
        }

        // 页面加载完成后自动检查配置
        document.addEventListener('DOMContentLoaded', function() {
            checkConfig();
        });
    </script>

    <meta name="csrf-token" content="{{ csrf_token() }}">
</body>
</html> 