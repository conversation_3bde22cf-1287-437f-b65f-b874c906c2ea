<!DOCTYPE html>
<html>
<head>
    <title>停车费支付</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .payment-container { max-width: 400px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input, .form-group select { width: 100%; padding: 8px; }
        .payment-methods { display: flex; gap: 10px; margin: 15px 0; }
        .payment-method { flex: 1; padding: 15px; border: 2px solid #ddd; text-align: center; cursor: pointer; }
        .payment-method.active { border-color: #007cff; background: #f0f8ff; }
        .btn-pay { width: 100%; padding: 12px; background: #007cff; color: white; border: none; font-size: 16px; }
    </style>
</head>
<body>
    <div class="payment-container">
        <h2>停车费支付</h2>
        
        @if ($errors->any())
            <div style="color: red; margin-bottom: 15px;">
                @foreach ($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif

        <form action="{{ route('icbc-pay.create') }}" method="POST">
            @csrf
            
            <div class="form-group">
                <label>车牌号码</label>
                <input type="text" name="car_number" value="{{ $car_number }}" required>
            </div>
            
            <div class="form-group">
                <label>停车时长(小时)</label>
                <input type="number" name="parking_duration" value="{{ $parking_duration }}" step="0.5">
            </div>
            
            <div class="form-group">
                <label>支付金额(元)</label>
                <input type="number" name="total_amount" value="{{ $amount }}" step="0.01" required>
            </div>
            
            <div class="form-group">
                <label>支付方式</label>

                <!-- 自动检测到的支付方式 -->
                <div id="detectedPaymentMethod" style="display: none;">
                    <div style="background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin-bottom: 10px; border-radius: 4px;">
                        <strong>已自动检测支付方式</strong>
                        <p id="detectedMethodText" style="margin: 5px 0 0 0; font-size: 14px;"></p>
                    </div>
                    <div id="selectedPaymentDisplay" style="border: 2px solid #2196f3; background: #f3e5f5; padding: 15px; text-align: center; border-radius: 4px;">
                        <!-- 动态显示选中的支付方式 -->
                    </div>
                </div>

                <!-- 手动选择支付方式 -->
                <div id="manualPaymentSelection" class="payment-methods">
                    @foreach($payment_methods as $key => $method)
                        <div class="payment-method" data-method="{{ $key }}">
                            {{ $key == 'alipay' ? '支付宝' : ($key == 'wechat' ? '微信支付' : $method['name'] ?? $key) }}
                        </div>
                    @endforeach
                </div>

                <input type="hidden" name="payment_method" id="selectedPaymentMethod" required>
            </div>
            
            <button type="submit" class="btn-pay">立即支付</button>
        </form>
    </div>

    <script>
        let selectedPaymentMethod = null;

        /**
         * 检测客户端类型
         */
        function detectClientType() {
            const userAgent = navigator.userAgent.toLowerCase();

            if (userAgent.includes('micromessenger')) {
                return 'wechat';
            }

            if (userAgent.includes('alipayclient') || userAgent.includes('alipay') || userAgent.includes('aliapp')) {
                return 'alipay';
            }

            return null;
        }

        /**
         * 自动选择支付方式
         */
        function autoSelectPaymentMethod() {
            const detectedClient = detectClientType();
            const detectedDiv = document.getElementById('detectedPaymentMethod');
            const manualDiv = document.getElementById('manualPaymentSelection');
            const detectedText = document.getElementById('detectedMethodText');
            const selectedDisplay = document.getElementById('selectedPaymentDisplay');

            if (detectedClient) {
                // 显示检测到的支付方式
                detectedDiv.style.display = 'block';
                manualDiv.style.display = 'none';

                selectedPaymentMethod = detectedClient;
                document.getElementById('selectedPaymentMethod').value = detectedClient;

                if (detectedClient === 'wechat') {
                    detectedText.textContent = '检测到您正在使用微信，将使用微信支付';
                    selectedDisplay.innerHTML = '<strong style="color: #4caf50;">✓ 微信支付</strong><br><small>已自动选择</small>';
                } else if (detectedClient === 'alipay') {
                    detectedText.textContent = '检测到您正在使用支付宝，将使用支付宝支付';
                    selectedDisplay.innerHTML = '<strong style="color: #2196f3;">✓ 支付宝</strong><br><small>已自动选择</small>';
                }
            } else {
                // 显示手动选择
                detectedDiv.style.display = 'none';
                manualDiv.style.display = 'flex';

                // 设置手动选择事件
                document.querySelectorAll('.payment-method').forEach(method => {
                    method.addEventListener('click', function() {
                        document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('active'));
                        this.classList.add('active');
                        selectedPaymentMethod = this.dataset.method;
                        document.getElementById('selectedPaymentMethod').value = selectedPaymentMethod;
                    });
                });
            }
        }

        // 页面加载时自动检测
        document.addEventListener('DOMContentLoaded', function() {
            autoSelectPaymentMethod();
        });
    </script>
</body>
</html>
