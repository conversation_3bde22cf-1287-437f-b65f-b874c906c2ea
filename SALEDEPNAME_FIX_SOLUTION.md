# 🔧 工行支付 saledepname 必输问题解决方案

## 📋 问题描述

在调用工行支付接口时出现以下错误：
```
提示：自选校验失败：字段saledepname的值必输
```

## 🔍 问题分析

经过查看工行支付官方示例代码，发现 `saledepname` 是工行聚合支付接口的必填字段：

### 官方示例中的字段要求
从 `CardbusinessAggregatepayB2cOnlineUiConsumepurchaseshowpayV1Test.php` 示例中可以看到：

```php
"biz_content" => array(
    "mer_id"=>"020001021577",
    "out_trade_no"=>"asdg0001447480h0065",
    "mer_prtcl_no"=>"0200010215770201",
    "body"=>"一体化非埋名测试",
    "subject"=>"一体化非埋名测试",
    "order_amt"=>"1",
    "notify_url"=>"http://...",
    "saledepname"=>"一体化非埋名测试", // ✅ 必填字段
    "notify_type"=>"HS",
    "result_type"=>"1",
    // ... 其他字段
)
```

### 缺失的必填字段
项目中缺少以下工行支付必填字段：
- `saledepname` - 销售部门名称
- `body` - 订单描述  
- `subject` - 订单标题
- `notify_type` - 回调类型
- `result_type` - 结果类型

## ✅ 解决方案

### 1. 修复支付请求构建逻辑

在 `packages/icbc-pay/src/IcbcPayClient.php` 中，添加所有必填字段：

```php
// 准备业务内容
$bizContent = [
    'mer_id' => (string)($this->getConfig('mer_id') ?? ''),
    'mer_prtcl_no' => (string)($this->getConfig('mer_prtcl_no') ?? ''),
    'out_trade_no' => (string)($orderData['order_id'] ?? ''),
    'order_amt' => $this->convertToFen($orderData['amount'] ?? '0'),
    'pay_mode' => $this->getPayModeByMethod($orderData['payment_method'] ?? 'wechat'),
    'access_type' => '1',
    'notify_url' => (string)($this->getConfig('notify_url') ?? ''),
    'mer_url' => (string)($this->getConfig('notify_url') ?? ''),
    'goods_body' => (string)($orderData['subject'] ?? ''),
    'goods_detail' => (string)($orderData['body'] ?? $orderData['subject'] ?? ''),
    'expire_time' => $this->getIcbcTimestamp(time() + 1800),
    'page_url' => (string)($this->getConfig('return_url') ?? ''),
    'return_url' => (string)($this->getConfig('return_url') ?? ''),
    'currency' => 'CNY',
    // ✅ 工行必填字段
    'saledepname' => (string)($orderData['subject'] ?? '停车费支付'), // 销售部门名称
    'body' => (string)($orderData['body'] ?? $orderData['subject'] ?? '停车费支付'), // 订单描述
    'subject' => (string)($orderData['subject'] ?? '停车费支付'), // 订单标题
    'notify_type' => 'HS', // 回调类型
    'result_type' => '1', // 结果类型
];
```

### 2. 修复位置

需要在两个方法中都添加这些字段：
1. `buildApiRequest()` 方法 - 新的UiIcbcClient实现
2. `buildPaymentForm()` 方法 - 旧的兼容实现

## 🧪 测试验证

### 1. 创建测试支付订单

```bash
curl -X POST http://localhost:8001/api/pay \
  -H "Content-Type: application/json" \
  -d '{
    "car_number": "测试B67890",
    "amount": "1.00", 
    "payment_method": "wechat",
    "parking_duration": 60
  }'
```

### 2. 验证生成的业务内容

从测试结果可以看到，生成的 `biz_content` 现在包含了所有必要字段：

```json
{
  "mer_id": "301059620104",
  "mer_prtcl_no": "3010596201040201", 
  "out_trade_no": "PARK20250829125728397156110",
  "order_amt": "100",
  "pay_mode": "9",
  "access_type": "1",
  "notify_url": "https://icbc.dev.hiwsoft.com/icbc-pay/notify",
  "mer_url": "https://icbc.dev.hiwsoft.com/icbc-pay/notify",
  "goods_body": "停车费支付",
  "goods_detail": "停车费支付 - 车牌：测试B67890",
  "expire_time": "2025-08-29 13:27:28",
  "page_url": "https://icbc.dev.hiwsoft.com/icbc-pay/return",
  "return_url": "https://icbc.dev.hiwsoft.com/icbc-pay/return",
  "currency": "CNY",
  "saledepname": "停车费支付",     // ✅ 已添加
  "body": "停车费支付 - 车牌：测试B67890", // ✅ 已添加
  "subject": "停车费支付",        // ✅ 已添加
  "notify_type": "HS",           // ✅ 已添加
  "result_type": "1"             // ✅ 已添加
}
```

## ✅ 修复结果

### 修复前
- ❌ 缺少 `saledepname` 字段
- ❌ 缺少 `body` 字段
- ❌ 缺少 `subject` 字段
- ❌ 缺少 `notify_type` 字段
- ❌ 缺少 `result_type` 字段

### 修复后
- ✅ `saledepname`: "停车费支付"
- ✅ `body`: "停车费支付 - 车牌：测试B67890"
- ✅ `subject`: "停车费支付"
- ✅ `notify_type`: "HS"
- ✅ `result_type`: "1"
- ✅ 支付订单创建成功
- ✅ 支付表单生成正常

## 🎯 字段说明

| 字段名 | 说明 | 示例值 | 必填 |
|--------|------|--------|------|
| `saledepname` | 销售部门名称 | "停车费支付" | ✅ |
| `body` | 订单描述 | "停车费支付 - 车牌：测试B67890" | ✅ |
| `subject` | 订单标题 | "停车费支付" | ✅ |
| `notify_type` | 回调类型 | "HS" | ✅ |
| `result_type` | 结果类型 | "1" | ✅ |

## 📝 相关文件

- `packages/icbc-pay/src/IcbcPayClient.php` - 主要修复文件
- `icbc_sdk/example/CardbusinessAggregatepayB2cOnlineUiConsumepurchaseshowpayV1Test.php` - 官方示例参考

## 🚀 部署建议

1. **字段值设置**: 确保 `saledepname` 等字段有合适的业务含义
2. **测试验证**: 在生产环境部署前充分测试所有支付场景
3. **错误监控**: 监控支付接口调用，及时发现新的字段要求变化

修复完成后，工行支付的 saledepname 必输问题已经解决，支付流程可以正常进行。
