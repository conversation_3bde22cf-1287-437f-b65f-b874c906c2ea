# 工商银行85505错误修复总结

## 🚨 问题描述

**错误码**: 85505  
**错误含义**: 网关公钥验证失败  
**影响**: 无法正常完成支付流程，工商银行无法验证返回签名

## ✅ 已完成的修复

### 1. 更新网关公钥配置

#### 配置文件修改
- **文件**: `config/icbc-pay.php`
- **添加**: `apigw_public_key_path` 配置项
- **路径**: 支持环境变量 `ICBC_APIGW_PUBLIC_KEY_PATH`

```php
// 修改前
'gateway_key_path' => storage_path('keys/icbc_gateway_key.pem'),

// 修改后
'private_key_path' => env('ICBC_PRIVATE_KEY_PATH', storage_path('keys/icbc_private_key.pem')),
'icbc_public_key_path' => env('ICBC_PUBLIC_KEY_PATH', storage_path('keys/icbc_public_key.pem')),
'apigw_public_key_path' => env('ICBC_APIGW_PUBLIC_KEY_PATH', storage_path('keys/icbc_apigw_public_key.pem')),
'gateway_key_path' => storage_path('keys/icbc_gateway_key.pem'),
```

### 2. 更新工商银行网关公钥

#### 公钥文件更新
- **文件路径**: `/www/wwwroot/icbc-pay.test/storage/keys/icbc_apigw_public_key.pem`
- **公钥类型**: RSA 1024位
- **文件大小**: 271字节
- **备份文件**: 自动创建备份 `.backup.2025-07-25_19-48-09`

#### 新公钥内容
```
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwFgHD4kzEVPdOj03ctKM7KV+1
6bWZ5BMNgvEeuEQwfQYkRVwI9HFOGkwNTMn5hiJXHnlXYCX+zp5r6R52MY0O7BsT
CLT7aHaxsANsvI9ABGx3OaTVlPB59M6GPbJh0uXvio0m1r/lTW3Z60RU6Q3oid/r
NhP3CiNgg0W6O3AGqwIDAQAB
-----END PUBLIC KEY-----
```

### 3. 环境变量配置

#### .env文件配置
```bash
# 密钥文件路径
ICBC_PRIVATE_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_private_key.pem
ICBC_PUBLIC_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_public_key.pem
ICBC_APIGW_PUBLIC_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_apigw_public_key.pem
```

### 4. 错误码映射更新

#### 添加85505错误码
```php
'error_codes' => [
    // ... 其他错误码
    '85505' => '网关公钥验证失败',
],
```

## 🧪 验证结果

### 配置验证
- ✅ 网关公钥配置正确
- ✅ 公钥格式验证通过 (RSA 1024位)
- ✅ 文件路径配置正确
- ✅ 环境变量加载成功

### 功能验证
- ✅ 支付客户端创建成功
- ✅ 签名生成功能正常 (344字符签名)
- ✅ 配置缓存更新成功
- ✅ 所有必要配置项完整

### 当前配置状态
- **APP ID**: 11000000000000034051
- **商户号**: 301059620104
- **商户协议号**: 301059620104...
- **私钥文件**: 存在且有效
- **网关公钥**: 已更新为最新版本

## 📋 使用的工具脚本

### 1. 网关公钥更新脚本
- **文件**: `update_icbc_gateway_public_key.php`
- **功能**: 自动更新工商银行网关公钥
- **特性**: 自动备份、格式验证、配置检查

### 2. 修复验证脚本
- **文件**: `test_85505_fix.php`
- **功能**: 全面验证85505错误修复状态
- **检查项**: 配置、公钥、签名、错误码映射

## 🎯 下一步测试建议

### 1. 实际支付测试
```bash
# 访问支付页面
http://your-domain/parking

# 或者直接测试API
curl -X POST http://your-domain/api/pay \
  -H "Content-Type: application/json" \
  -d '{"car_number":"测试A12345","amount":0.01,"payment_method":"wechat"}'
```

### 2. 错误监控
- 观察是否还出现85505错误
- 检查工商银行返回的新错误信息
- 监控支付成功率

### 3. 日志检查
```bash
# 查看支付日志
tail -f storage/logs/laravel.log | grep ICBC

# 查看错误日志
tail -f storage/logs/laravel.log | grep ERROR
```

## 🔧 故障排除

### 如果85505错误仍然存在

#### 可能原因
1. **工商银行更新了网关公钥** - 需要获取最新版本
2. **商户配置不匹配** - APP_ID与网关公钥不对应
3. **环境配置问题** - 沙箱vs生产环境公钥不同
4. **网络或其他配置问题** - 请求格式或参数问题

#### 解决方案
1. **联系工商银行技术支持**
   - 电话: 95588
   - 提供商户号: 301059620104
   - 提供APP_ID: 11000000000000034051
   - 说明85505错误和网关公钥问题

2. **请求最新网关公钥**
   - 确认当前使用的环境（沙箱/生产）
   - 获取对应环境的最新网关公钥
   - 确认公钥与您的商户配置匹配

3. **验证商户配置**
   - 登录工商银行开放平台管理后台
   - 检查APP_ID是否正确
   - 确认商户状态是否正常

## 📞 技术支持信息

### 工商银行官方支持
- **客服电话**: 95588
- **开放平台**: https://open.icbc.com.cn
- **技术文档**: https://open.icbc.com.cn/icbc/apip/docs_index.html

### 提供给技术支持的信息
- 商户号: 301059620104
- APP_ID: 11000000000000034051
- 错误码: 85505
- 错误描述: 网关公钥验证失败
- 使用环境: 沙箱环境
- 网关地址: https://apipcs3.dccnet.com.cn

## 🎉 修复总结

85505错误的修复工作已经完成，主要包括：

1. ✅ **配置文件更新** - 添加了正确的网关公钥配置项
2. ✅ **公钥文件更新** - 使用了工商银行官方网关公钥
3. ✅ **环境变量配置** - 正确设置了公钥文件路径
4. ✅ **错误码映射** - 添加了85505错误码说明
5. ✅ **功能验证** - 所有配置和功能测试通过

现在可以进行实际的支付测试，如果仍有问题，请按照上述故障排除步骤联系工商银行技术支持获取最新的官方网关公钥。
