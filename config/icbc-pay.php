<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | 工商银行支付配置
    |--------------------------------------------------------------------------
    |
    | 这里是工商银行支付SDK的主要配置项
    |
    */

    // 应用基本信息
    'app_id' => env('ICBC_APP_ID', ''),
    'merchant_id' => env('ICBC_MER_ID', ''),
    'mer_id' => env('ICBC_MER_ID', ''),
    'merchant_protocol_no' => env('ICBC_MER_PRTCL_NO', ''),
    'mer_prtcl_no' => env('ICBC_MER_PRTCL_NO', ''),

    // 签名和加密配置
    'sign_type' => env('ICBC_SIGN_TYPE', 'RSA2'),
    'charset' => env('ICBC_CHARSET', 'UTF-8'),
    'format' => env('ICBC_FORMAT', 'json'),

    // 密钥文件路径
    'private_key_path' => storage_path('keys/icbc_private_key.pem'),
    'icbc_public_key_path' => storage_path('keys/icbc_public_key.pem'),
    'gateway_key_path' => storage_path('keys/icbc_gateway_key.pem'),

    // 加密配置（可选）
    'encrypt_key' => env('ICBC_ENCRYPT_KEY', ''),
    'encrypt_type' => env('ICBC_ENCRYPT_TYPE', 'AES'),

    // 证书配置（如果使用CA签名）
    'ca_cert_path' => storage_path('keys/icbc_ca.crt'),
    'ca_password' => env('ICBC_CA_PASSWORD', ''),

    // 网关配置
    'gateways' => [
        'sandbox' => [
            'base_url' => 'https://apipcs3.dccnet.com.cn',
            'payment_url' => '/ui/cardbusiness/aggregatepay/b2c/online/ui/consumepurchaseshowpay/V1',
            'query_url' => '/api/cardbusiness/aggregatepay/orderquery',
            'refund_url' => '/api/cardbusiness/aggregatepay/refund',
        ],
        'production' => [
            'base_url' => 'https://gw.open.icbc.com.cn',
            'payment_url' => '/ui/cardbusiness/aggregatepay/b2c/online/ui/consumepurchaseshowpay/V1',
            'query_url' => '/api/cardbusiness/aggregatepay/orderquery',
            'refund_url' => '/api/cardbusiness/aggregatepay/refund',
        ],
    ],

    // 当前环境
    'environment' => env('ICBC_ENVIRONMENT', 'sandbox'),

    // 回调配置
    'notify_url' => env('ICBC_NOTIFY_URL', env('APP_URL', 'http://localhost') . '/icbc-pay/notify'),
    'return_url' => env('ICBC_RETURN_URL', env('APP_URL', 'http://localhost') . '/icbc-pay/return'),

    // 支付配置
    'payment' => [
        'timeout' => env('ICBC_PAYMENT_TIMEOUT', 30), // 支付超时时间（分钟）
        'currency' => env('ICBC_CURRENCY', 'CNY'),
        'fee_type' => env('ICBC_FEE_TYPE', 'CNY'),
        'device_info' => env('ICBC_DEVICE_INFO', 'WEB'),
    ],

    // 时间同步配置
    'time_sync' => [
        'offset' => env('ICBC_TIME_OFFSET', 0), // 时间偏移秒数（-60 到 60）
        'auto_sync' => env('ICBC_AUTO_SYNC', true), // 是否自动同步时间
        'tolerance' => env('ICBC_TIME_TOLERANCE', 300), // 时间容忍范围（秒）
        'use_utc' => env('ICBC_USE_UTC', false), // 工商银行使用北京时间，不是UTC
    ],

    // 网络配置
    'http' => [
        'timeout' => env('ICBC_HTTP_TIMEOUT', 30),
        'connect_timeout' => env('ICBC_HTTP_CONNECT_TIMEOUT', 8),
        'retry_times' => env('ICBC_HTTP_RETRY_TIMES', 3),
        'verify_ssl' => env('ICBC_VERIFY_SSL', true),
    ],

    // 日志配置
    'log' => [
        'enabled' => env('ICBC_LOG_ENABLED', true),
        'level' => env('ICBC_LOG_LEVEL', 'info'),
        'channel' => env('ICBC_LOG_CHANNEL', 'single'),
        'max_files' => env('ICBC_LOG_MAX_FILES', 30),
    ],

    // 缓存配置
    'cache' => [
        'enabled' => env('ICBC_CACHE_ENABLED', true),
        'ttl' => env('ICBC_CACHE_TTL', 3600), // 缓存时间（秒）
        'prefix' => env('ICBC_CACHE_PREFIX', 'icbc_pay:'),
    ],

    // 支付方式映射
    'payment_methods' => [
        'wechat' => [
            'pay_mode' => '9',
            'access_type' => '1', // 扫码支付
            'name' => '微信支付',
        ],
        'alipay' => [
            'pay_mode' => '10',
            'access_type' => '1', // 扫码支付
            'name' => '支付宝支付',
        ],
        'unionpay' => [
            'pay_mode' => '8',
            'access_type' => '1',
            'name' => '银联支付',
        ],
    ],

    // 错误码映射
    'error_codes' => [
        '0' => '成功',
        '-1' => '系统错误',
        '1001' => '参数错误',
        '1002' => '签名错误',
        '1003' => '商户不存在',
        '1004' => '订单重复',
        '1005' => '余额不足',
        '1006' => '订单不存在',
        '1007' => '订单已支付',
        '1008' => '订单已取消',
        '1009' => '订单已过期',
        '2001' => '网络超时',
        '2002' => '服务不可用',
        '3001' => '证书错误',
        '3002' => '密钥错误',
    ],

    // 开发配置
    'dev' => [
        'mock_enabled' => env('ICBC_MOCK_ENABLED', false),
        'debug_enabled' => env('ICBC_DEBUG_ENABLED', false),
        'test_mode' => env('ICBC_TEST_MODE', false),
    ],
];
