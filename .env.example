APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"


# 工行支付配置
ICBC_ENVIRONMENT=sandbox
ICBC_USE_UI_MODE=true
ICBC_APP_ID=11000000000000021205
ICBC_MER_ID=301008020008
ICBC_MER_PRTCL_NO=3010080200080201
ICBC_GATEWAY_URL=https://apipcs3.dccnet.com.cn
ICBC_NOTIFY_URL=https://icbc.dev.hiwsoft.com/icbc-pay/notify
ICBC_RETURN_URL=https://icbc.dev.hiwsoft.com/icbc-pay/return

# 密钥文件路径
ICBC_PRIVATE_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_private_key.pem
ICBC_PUBLIC_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_public_key.pem
ICBC_APIGW_PUBLIC_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_apigw_public_key.pem

# ICBC支付配置
ICBC_TIME_OFFSET=0
# ICBC支付配置
ICBC_USE_UTC=true
# ICBC支付配置
ICBC_AUTO_SYNC=true
# ICBC支付配置
ICBC_TIME_TOLERANCE=300
# ICBC支付配置
ICBC_MOCK_ENABLED=true
# ICBC支付配置
ICBC_DEBUG_ENABLED=true
# ICBC支付配置
ICBC_TEST_MODE=true




# # 工行支付配置
# ICBC_ENVIRONMENT=production
# ICBC_USE_UI_MODE=true
# ICBC_APP_ID=11000000000000052474
# ICBC_MER_ID=301055420003
# ICBC_MER_PRTCL_NO=3010554200030201
# ICBC_GATEWAY_URL=https://gw.open.icbc.com.cn
# ICBC_NOTIFY_URL=https://icbc.dev.hiwsoft.com/icbc-pay/notify
# ICBC_RETURN_URL=https://icbc.dev.hiwsoft.com/icbc-pay/return

# # 密钥文件路径
# ICBC_PRIVATE_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_private_key.pem
# ICBC_PUBLIC_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_public_key.pem
# ICBC_APIGW_PUBLIC_KEY_PATH=/www/wwwroot/icbc-pay.test/storage/keys/icbc_apigw_public_key.pem

# # ICBC支付配置
# ICBC_TIME_OFFSET=0
# # ICBC支付配置
# ICBC_USE_UTC=true
# # ICBC支付配置
# ICBC_AUTO_SYNC=true
# # ICBC支付配置
# ICBC_TIME_TOLERANCE=300
# # ICBC支付配置
# ICBC_MOCK_ENABLED=true
# # ICBC支付配置
# ICBC_DEBUG_ENABLED=true
# # ICBC支付配置
# ICBC_TEST_MODE=true

