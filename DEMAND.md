## 参照以下两个文档说明，再次检查项目所有文件和配置文件，确保它们与官方文档中的配置一致。


---
## SDK开发

SDK开发
1 SDK介绍
SDK是为调用方（合作方）简化API开放平台调用专门提供的开发工具包，包括密钥生成、服务调用、返回结果解析等功能。本文档所述基于SDK的Java版本实现。

SDK开发运行环境：JDK1.8及以上。

每个已注册的应用会自动分配一个APPID，调用方利用SDK提供的密钥生成功能，生成公私钥对，并将公钥提供给API开放平台。API开放平台维护APPID与公钥的对应关系。

SDK采用私钥对报文签名，并将明文和签名一并发送到API开放平台验签。API开放平台收到请求后进行验证，验证通过后提供服务，并以Json格式返回响应结果，SDK收到返回信息后使用网关公钥对响应报文进行验签。调用方在使用SDK的过程中，无需关心请求信息的签名、组装及响应信息的验签。

2 SDK使用说明
2.1 密钥生成
Windows环境，以生成RSA2算法为例：

进入到bin目录，双击keygen_rsa.bat文件，生成一对RSA2公钥和密钥。

Linux环境，以生成RSA2算法为例：

切换到bin目录，运行 ./keygen_rsa.sh命令，生成一对RSA2公钥和密钥。

2.2 AES密钥生成
Windows环境

进入到bin目录，双击AESKeygen.bat文件，在当前目录下生成一个长度为128的AES密钥,密钥文件名为AESKey.txt，若需改为其他长度的密钥，需修改AESKeygen.bat文件内容中的数字为指定的密钥长度。

AESKeygen.bat文件默认内容为

    java -cp ../lib/icbc-api-sdk-cop.jar com.icbc.api.AESKeyGen 128
例:若需改为长度为192的密钥，则将128替换为192即可。如

    java -cp ../lib/icbc-api-sdk-cop.jar com.icbc.api.AESKeyGen 192
Linux环境

切换到bin目录，运行 ./AESKeygen.sh命令，在当前目录下生成一个长度为128的AES密钥，密钥文件名为AESKey.txt。

3 资源引用
下载SDK后，在工程中直接引用，并引用lib文件夹下的所有jar包。

4 网关公钥
4.1 测试环境网关公钥
请点击下载

4.2 生产环境网关公钥
API开放平台网关公钥可由组织已认证用户申请APP后在APP详情页下载使用，如下图：
生产网关公钥下载

5 接口调用示例
构造函数参数说明如下：

参数定义	参数说明
String appId	APP的编号,应用在API开放平台注册时生成
String privateKey	应用私钥
String signType	签名类型，CA-工行颁发的证书认证，SM2，RSA2-RSAWithSha256，缺省为RSA2
String charset	字符集，缺省为UTF-8
String format	请求参数格式，仅支持json
String icbcPulicKey	网关公钥
String encryptKey	AES加密密钥，缺省为空
String encryptType	加密类型，当前仅支持AES加密，需要按照接口类型是否需要加密来设置，缺省为空
String ca	当签名类型为CA时，通过该字段上送证书公钥，缺省为空
String password	当签名类型为CA时，通过该字段上送证书密码，缺省为空
5.1 数据类型API调用对象说明
沙箱测试网关与正常网关调用方式相同，仅需将请求地址改为沙箱环境地址即可。

数据类型API使用SDK时，通过默认调用实现类DefaultIcbcClient构造调用对象client，数据类型API构造对象说明：

    DefaultIcbcClient client = new DefaultIcbcClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
    // 签名类型为CA时，需传入APP编号，证书私钥，网关公钥，证书公钥和证书密码，其他参数使用缺省值
    DefaultIcbcClient client = new DefaultIcbcClient(APP_ID, CA_PRIVATE_STR, APIGW_PUBLIC_KEY, CA_PUBLIC_STR, CA_PASSWORD);
    // 传入全部参数构造client对象
    DefaultIcbcClient client = new DefaultIcbcClient(APP_ID, IcbcConstants.SIGN_TYPE_CA,     CA_PRIVATE_STR, IcbcConstants.CHARSET_UTF8, IcbcConstants.FORMAT_JSON,APIGW_PUBLIC_KEY, IcbcConstants.ENCRYPT_TYPE_AES,ENCRYPT_KEY, CA_PUBLIC_STR, CA_PASSWORD);
数据类型API调用示例

以使用缺省的RSA2签名为例，数据类型API调用示例如下：

// 构造client对象
DefaultIcbcClient client = new DefaultIcbcClient(APP_ID, MY_PRIVATE_KEY, APIGW_PUBLIC_KEY);
// 设置请求对象request
EbankcVerifiedInfoQueryRequest request = new EbankcVerifiedInfoQueryRequest();
// 设置请求路径
// 生产请求路径：https://gw.open.icbc.com.cn/api/+接口服务地址
// 沙箱测试请求路径：https://gw-sandbox.open.icbc.com.cn/api/+接口服务地址
request.setServiceUrl("https://gw.open.icbc.com.cn/api/ebankc/V1/VerifiedInfoQuery");
// 设置业务参数，每个Request请求实现类都有一个RequestBiz内部类用来设置业务参数
EbankcVerifiedInfoQueryRequestBiz bizContent= new EbankcVerifiedInfoQueryRequestBiz();
bizContent.setNextTag("");
bizContent.setTranDateBegin("********");
bizContent.setTranDateEnd("********");
bizContent.setVerifiedCorpId("0200EG0000602");
bizContent.setVerifiedId("");
request.setBizContent(bizContent);
// request组装完成，开始发起调用
EbankcVerifiedInfoQueryResponse response = client.execute(request, "msgId");
// 判断调用是否成功，进行后续业务处理
if (response.isSuccess()) {
    // TODO 业务成功处理
    System.out.println(response.getReturnMsg());            
} else {
    // TODO 失败
    System.out.println(response.getReturnMsg());
}
5.2 页面类型API调用对象说明
页面类型API使用SDK时，通过默认调用实现类UiIcbcClient构造调用对象client。区别于数据类型API，页面类型API因为没有数据返回，故构造client对象时无需传入网关公钥，页面类型API调用对象构造示例：

// 签名类型为RSA2时，传入APP编号，应用私钥和编码格式，其他参数使用缺省值
UiIcbcClient client = new UiIcbcClient(APP_ID,
 MY_PRIVATE_KEY, IcbcConstants.CHARSET_UTF8);
// 签名类型为CA时，需传入证书公私钥和密码，其他参数使用缺省值
UiIcbcClient client = new UiIcbcClient(APP_ID, CA_PRIVATE_STR,  IcbcConstants.CHARSET_UTF8, CA_PUBLIC_STR, CA_PASSWORD);
页面类型API调用示例

以使用CA签名为例，数据类型API调用示例如下：

// 构造client对象
String castr = "此处为证书公钥";
// 去除签名数据及证书数据中的空格 added for Safari
Pattern p = Pattern.compile("\s*|    ");
Matcher m2 = p.matcher(castr);
castr = m2.replaceAll("");
UiIcbcClient client = new UiIcbcClient("10000000000000016542",
CA_PRIVATE_STR, IcbcConstants.CHARSET_UTF8, castr, "12345678");
// 设置请求对象request
PersonalSignRequest request = new PersonalSignRequest();
// 设置请求路径
request.setServiceUrl("https://gw.open.icbc.com.cn/ui/paytest/V1/uuuu");
// 该示例请求实现类的业务类PersonalSignRequestBiz下还包含PersonalSignRequestExtend和PersonalSignRequestOrderInfo两个内部类
PersonalSignRequestBiz bizContent = new PersonalSignRequestBiz();
PersonalSignRequestExtend extend = new PersonalSignRequestExtend();
extend.setCertNo("CertNo1");
PersonalSignRequestOrderInfo orderInfo = new PersonalSignRequestOrderInfo();
orderInfo.setInterfaceName("interfaceName");
bizContent.setExtend(extend);
bizContent.setOrderInfo(orderInfo);
request.setBizContent(bizContent);
// 生成自提交的表单返回客户浏览器，该表单会自动提交完成调用
System.out.println(client.buildPostForm(request));
6 返回信息说明
6.1 返回说明
响应报文中分别用return_code、return_msg字段表示返回码和返回信息。return_code为0时表示成功，负数表示疑帐（超时），正数表示失败。

{
  "return_code": 500018,
  "return_msg": "访问的API不存在"
}
6.2 返回判断示例
// response对象中获取返回码和返回信息
int returnCode = response.getReturnCode(resp);
String returnMsg = response.getReturnMsg(resp);
if(returnCode > 0){
    //调用失败处理逻辑
    System.out.println("调用失败：" + returnMsg);
}else if(returnCode < 0){
    //调用疑帐（或者叫超时、未知）处理逻辑
    System.out.println("调用疑帐：" + returnMsg);
}else{
    //调用成功处理逻辑
    System.out.println("调用成功" + returnMsg);
}
6.3 返回信息列表
返回码	返回说明
0	成功
400011	参数非法，原因可能为app id为空、app id非法、签名为空、应用系统
时间与API开放平台系统时间不在限定差值以内、时间戳非法
500018	访问的API不存在
500020	非法调用
400016	app公钥未维护
400017	签名验证失败
400019	授权验证失败
500031	速率超限
500032	并发超限
-500041	代理异常
-500042	代理超时
-500044	网关签名失败
500043	网关配置文件错误，无法从配置文件中读取配置
-500099	网关内部异常

---
## 

API请求构造指引
工行API开放平台为合作方提供了SDK&Demo完成快速接入开发，合作方也可选择不使用平台提供的SDK&Demo，自主开发，具体可参考本文详情来构造请求报文和签名原文。

1 公钥
开发者上传至工行API开放平台的需为PKCS8格式的公钥。

API开放平台网关测试环境公钥：请点击下载

API开放平台交易中会用到两套公私钥，一个是应用自身的公私钥（公私钥签名认证体系，如RSA2、SM2)，一个是API开放平台网关的公私钥。

应用的公私钥可通过SDK的bin目录下的工具生成。应用的私钥自己保留，应用的公钥在API开放平台中登记。

调用方发起调用时，需用应用的私钥对报文进行签名，API开放平台会对请求使用应用的公钥进行验签。

对于数据类型API(访问路径：/api)，API开放平台会使用网关的私钥对返回报文使用API网关私钥进行签名，调用方需用API网关公钥对报文进行验签，以验证报文为API开放平台返回的。

对于页面类型API(访问路径：/ui)，API开放平台会对重定向的url添加API开放平台的签名，服务方需使用API网关的公钥对重定向的请求进行验签，以确认重定向url是API开放平台生成的。页面类型调用方无需关心API网关的公钥。

2 签名
如果合作方自行组织签名报文，需注意签名原文遵照API开放平台规范进行。

2.1 签名算法
API开放平台签名算法分为RSA2、SM2、CA(API开放平台固有参数sign_type字段的值)，对应的实际签名算法为RSAWITHSHA256、SM2以及CA,各合作方如需自行构造请求，请在相应的开发语言中使用相应的签名算法。

签名原文为API访问路径(如/ui/personal/sign/V1/verify)与请求参数按照ASCII码增序（字母升序排序）后拼接的字符串。

注:生成签名时，签名原文不要做URLEncode。

2.2 页面类型API签名原文示例
/ui/personal/sign/V1/verify?app_id=10000000000000016542&biz_content={"authen_acct_name":"x9G3wg==","authen_acct_no":"6222020200106190966","authen_name":"x9G3wg==","auto_turn_flag":"0","cert_no":"428767198408147542","cert_type":"0","language":"ZH_CN","logon_id":"020000206164898.p.0200","notify_type":"HS","request_ip":"***********","tran_time":"**************","verified_corp_id":"2000EG0000136","verified_corp_name":"uaTJzL7W","verified_flag":"1","verified_id":"800136","verified_info":"1eLKx9K7uPbHqcP7xNrI3cW2","verified_kind":"0","verified_type":"0"}&charset=UTF-8&format=json&msg_id=5eb0ed071af5434da4cc1942ac42c174&notify_url=https://www.scgsj.com/notify.do&sign_type=RSA2&timestamp=2017-07-13 19:48:47
2.3 数据类型API签名原文示例
/api/csi/credit/card/V1/reject?app_id=10000000000000021587&biz_content={"acc_amount":1000,"acc_fee":0,"acc_fee_rate":0,"acc_price":0,"acc_quantity":0,"amount":0,"buyer_cert_no":"3247677)","buyer_cert_type":0,"buyer_name":"cc","phy_amount":0,"phy_amount_gap":0,"phy_num":0,"phy_pptxnno":0,"prod_sell_price":0,"prod_single_price":0,"recipient_cert_type":0}&charset=UTF-8&format=json&msg_id=Oikeclo001&sign_type=RSA2&timestamp=2017-07-13 20:11:14
2.4 签名原文构造
①筛选

获取所有请求参数，不包括字节型参数，如文件、字节流，剔除sign字段。

②排序

将筛选的参数按照第一个字符的键值ASCII码递增排序（字母升序排序），如果遇到相同字符则按照第二个字符的键值ASCII码递增排序，以此类推。

③拼接

将排序后的参数与其对.值，组合成“参数=参数值”的格式，并且把这些参数用&字符连接来，此时生成的字符串为待签名字符串。

④调用签名函数

现将拼接后的参数，按照编码类型处理为byte数组，使用各自语言对应的RSA2签名函数利用商户私钥对待签名字符串进行签名，并进行Base64编码。

3 请求构造说明
传输过程中注意API开放平台通用参数需要URLEncode（详见5），biz_content在form的body中提交，form中要指明Content-Type属性，Content-Type: application/x-www-form-urlencoded，http header中Host值不允许手动调整为其他值，必须为网关域名。

3.1 页面类型API
即以/ui开头，调用成功后返回页面的API。

页面类型API参数biz_content在http的请求body中传送，除biz_content之外的API开放平台固定参数需在url的参数中传送,固定参数包括：app_id、sign、sign_type、charset、format、encrypt_type、timestamp、msg_id。

若app_id等平台固有参数在body中传送，会导致这个情况：API开放平台会在重定向url中拼上app_id等平台固有参数，原请求body中的app_id参数也会存在，导致服务方接收到重定向请求时，url参数中也解析到app_id，body参数中也会解析到app_id参数。页面类型请求示例如下：

<html>
<body>
<form name="auto_submit_form" method="post" action="http://*************:8081/ui/personal/sign/V1/verify?charset=UTF-8&format=json&sign=A%2B%2BSOQ%2FBus3LzLl8PXT1Ibv0s4L78Uj7bs2NNq4VoAIVAHiXa9lkBXiO2cnVrsJL3dOad5D9wQ5sc4veDrrP%2BMDuIdnDielxLtwgiZOqwoG0Ri7SdajAmBGtk1jo0P0d1rv2CVFa%2FsCRz%2FHK%2FAJO%2Bps5CM3%2FbbkZfoSCo%2FvPU5w%3D&msg_id=msg_id_rsa123&app_id=10000000000000002337&sign_type=RSA2&timestamp=2017-06-12+09%3A12%3A11">
<input type="hidden" name="biz_content" value="{&quot;authen_acct_name&quot;:&quot;x9G3wg==&quot;,&quot;authen_acct_no&quot;:&quot;6222020200106190966&quot;,&quot;authen_name&quot;:&quot;x9G3wg==&quot;,&quot;auto_turn_flag&quot;:&quot;0&quot;,&quot;cert_no&quot;:&quot;428767198408147542&quot;,&quot;cert_type&quot;:&quot;0&quot;,&quot;language&quot;:&quot;ZH_CN&quot;,&quot;logon_id&quot;:&quot;020000206164898.p.0200&quot;,&quot;notify_type&quot;:&quot;HS&quot;,&quot;request_ip&quot;:&quot;***********&quot;,&quot;tran_time&quot;:&quot;**************&quot;,&quot;verified_corp_id&quot;:&quot;2000EG0000136&quot;,&quot;verified_corp_name&quot;:&quot;uaTJzL7W&quot;,&quot;verified_flag&quot;:&quot;1&quot;,&quot;verified_id&quot;:&quot;800136&quot;,&quot;verified_info&quot;:&quot;1eLKx9K7uPbHqcP7xNrI3cW2&quot;,&quot;verified_kind&quot;:&quot;0&quot;,&quot;verified_type&quot;:&quot;0&quot;}">
<input type="hidden" name="interfaceName" value="ICBC_PEEBANK_CERTVERIFY_NEW">
<input type="hidden" name="notify_url" value="https://www.scgsj.com/notify.do">
<input type="submit" value="立刻提交" style="display:none" >
</form>
<script>document.forms[0].submit();</script>
<body>
</html>
3.2 数据类型API
即以/api开头，调用成功后返回Json格式报文的API，数据类型API所有参数建议均在POST的body中传送，通过form提交，form中要指明Content-Type属性，Content-Type: application/x-www-form-urlencoded，http header中禁止使用Expect参数，并且 header中Host值不允许手动调整为其他值，必须为网关域名。

数据类型请求示例如下：

POST HTTP/1.1
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
https://gw.open.icbc.com.cn/api/preciousmetal/gold/V1/purchase?app_id=Oikeclo001&msg_id=urcnl24ciutr9&format=json&charset=utf-8&sign_type=RSA2&sign=TRFEWHYUFCEW&timestamp=2016-10-29 20:44:38&biz_content=
{
  "cust_no":"ABC123456",
  "cust_name":"小肥羊",
  "cert_type":0,
  "cert_no":"44528111111111",
  "mobile_no":"15915722111",
  "trx_seq_no":"8888811",
  "purchase_quantity":10,
  "purchase_price":222,
  "purchase_amt":2220,
  "lock_price_ts":"2016-04-***********.762126",
  "fee_rate":1000000,
  "fee":22,
  "trx_req_ts":"2016-04-***********.762126"
 }
4 响应验签
开发者只对工行API开放平台返回的json中response_biz_content的值做验签。response_biz_content的Json值内容，如为json则需要包含首尾的“{”和“}”两个大括号，如为字符串则需包括前后引号，如为数组，则需包含首位的“[”和“]”，作为验签整体。响应验签需使用API开放平台网关公钥进行验签，签名算法为RSAWITHSHA256。

4.1 返回参数签名验证示例
 {
 "response_biz_content":{
 "return_code":0,
 "return_msg":"success",
 "class_id":"your class id",
 "class_name:"your class name"
 },
 "sign":"ckVL6FMpSPokuqEzpA02xrQAQZkgpn3JwJLf0Ig3smC3P2y5odhZ9IlcmNHD8wMCUOSK
WvVBFpv2gpsx+s7OXqpjsiOZ3i4ibWHUSX0OPBPYtuLxJ/wlTolq9B36mRfj54UT6Uwrl0Vls20c+7
RpZbz2HkEy4Ea7F2SUkqaLgXY="
 }
4.2 待签名数据：
{
 "return_code":0,
 "return_msg":"success",
 "class_id":"your class id",
 "class_name:"your class name"
}
5 关于URLEncode的说明
http请求的参数名与参数值都要做urlEncode，除非参数名与参数值中无特殊字符(如+号等)。参数之间用&隔开，该符号不能做urlEncode，参数名与参数值之间的=(等于号)也不能做urlencode。

若未做URLEncode，+(加号传输）之后会变成空格。若参数之间的&符号做了UrlEncode，例如p1=aaaaaa%26p2=bbbbbb会解析成一个参数(%26转码前是&符号)。