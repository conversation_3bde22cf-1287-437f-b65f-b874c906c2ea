<?php

// URL解码调试脚本
$url = "https://gw.open.icbc.com.cn/ui/cardbusiness/aggregatepay/b2c/online/ui/consumepurchaseshowpay/V1?app_id=11000000000000052474&charset=UTF-8&format=json&sign_type=RSA2&timestamp=2025-05-23+15%3A53%3A26&version=V1&biz_content=%7B%22mer_id%22%3A%22301055420003%22%2C%22mer_prtcl_no%22%3A%223010554200030201%22%2C%22out_trade_no%22%3A%22DEBUG_17480156069121%22%2C%22orig_date_time%22%3A%222025-05-23+15%3A53%3A26%22%2C%22total_fee%22%3A%221%22%2C%22body%22%3A%22%E8%B0%83%E8%AF%95%E6%94%AF%E4%BB%98%E6%B5%8B%E8%AF%95%22%2C%22mer_url%22%3A%22http%3A%5C%2F%5C%2Ficbc.dev.hiwsoft.com%5C%2Ficbc-pay%5C%2Fnotify%22%2C%22spbill_create_ip%22%3A%22203.205.158.53%22%2C%22fee_type%22%3A%22001%22%2C%22pay_mode%22%3A%229%22%2C%22access_type%22%3A%221%22%2C%22notify_type%22%3A%22HS%22%2C%22result_type%22%3A%220%22%2C%22device_info%22%3A%22PARKING_SYSTEM_001%22%2C%22icbc_appid%22%3A%2211000000000000052474%22%2C%22expire_time%22%3A30%2C%22attach%22%3A%22%7B%5C%22car_number%5C%22%3A%5C%22%E8%B0%83%E8%AF%95A12345%5C%22%2C%5C%22parking_duration%5C%22%3A60%7D%22%7D&sign=a3aeN%2BRGcYruvveDmMYIEsuUzIZQJQxstX8VTfi%2Fyx9jEdO%2Fcd8cmPP1nStmO3wZfbghamV8zj67JA2NKNIMACeioCL1%2FPqXLlE4fR55bi5qJpCfP9WNywLC1HhHDCGb6gBZKVh5GIHnZpLPy2PEmi9oT1JVBRXmUomgM1A2ktRINGTeMEcIsTfscNutlCCaY6YlbgoEEsbfo3EpH1CJojgQb4TNynWkPXnPMHG1hYKI%2BR7B7HE0GodMO1t7ObRR4OA46dsRPGi5sVgMjM4DXDMaArsw1lVs2kP0a6cZk5HcMrP9RQn7Vgfd1z6nzbeVCcKBwyVCVGy0DptQrqIlSQ%3D%3D";

echo "分析工商银行支付URL参数:\n\n";

$parsedUrl = parse_url($url);
parse_str($parsedUrl['query'], $params);

echo "基础URL: " . $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $parsedUrl['path'] . "\n\n";

echo "请求参数:\n";
foreach ($params as $key => $value) {
    if ($key === 'biz_content') {
        echo "  {$key}: " . urldecode($value) . "\n";
        echo "  {$key} (解析JSON):\n";
        $bizContent = json_decode(urldecode($value), true);
        if ($bizContent) {
            foreach ($bizContent as $bizKey => $bizValue) {
                echo "    {$bizKey}: {$bizValue}\n";
            }
        }
    } else {
        echo "  {$key}: " . urldecode($value) . "\n";
    }
}

echo "\n测试使用curl访问工商银行API...\n";

// 使用curl测试
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";
if ($error) {
    echo "CURL错误: {$error}\n";
}

echo "响应内容前500字符:\n";
echo substr($response, 0, 500) . "\n";

// 检查是否包含参数校验失败的错误
if (strpos($response, '参数校验失败') !== false) {
    echo "\n❌ 检测到参数校验失败错误\n";
} else if (strpos($response, '支付') !== false || strpos($response, 'pay') !== false) {
    echo "\n✅ 看起来响应正常，没有参数校验错误\n";
} else {
    echo "\n⚠️  响应内容未知\n";
} 