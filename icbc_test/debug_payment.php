<?php

require_once __DIR__ . '/vendor/autoload.php';

// 启动Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

try {
    echo "开始调试工商银行支付参数...\n\n";

    // 创建支付记录
    $paymentRecord = PaymentRecord::create([
        'out_trade_no' => 'DEBUG_' . time() . rand(1000, 9999),
        'total_amount' => 0.01,
        'subject' => '调试支付测试',
        'payment_method' => 'wechat',
        'car_number' => '调试A12345',
        'parking_duration' => 60,
        'status' => 'pending'
    ]);

    echo "支付记录创建成功: {$paymentRecord->out_trade_no}\n";

    // 创建支付服务
    $icbcPayService = new IcbcPayService();

    // 调用支付创建方法
    $result = $icbcPayService->createPayment($paymentRecord);

    echo "支付创建结果:\n";
    print_r($result);

    echo "\n完成调试\n";

} catch (Exception $e) {
    echo "调试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
} 