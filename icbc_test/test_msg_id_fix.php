<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🔧 测试工商银行支付msg_id修复\n";
    echo "===============================\n\n";
    
    echo "创建支付客户端...\n";
    $client = app(IcbcPayClient::class);
    echo "✅ 客户端创建成功\n";
    
    echo "\n检查配置信息...\n";
    $config = $client->getConfig();
    echo "App ID: " . ($config['app_id'] ?? 'N/A') . "\n";
    echo "Mer ID: " . ($config['mer_id'] ?? 'N/A') . "\n"; 
    echo "Mer Protocol No: " . ($config['mer_prtcl_no'] ?? 'N/A') . "\n";
    echo "Mock Enabled: " . ($config['dev']['mock_enabled'] ? 'true' : 'false') . "\n";
    
    echo "\n生成支付表单...\n";
    $testOrderData = [
        'order_id' => 'MSGID_TEST_' . time(),
        'amount' => 0.01,
        'subject' => 'MSG ID测试订单',
        'payment_method' => 'wechat'
    ];
    
    $formHtml = $client->buildForm($testOrderData);
    echo "✅ 支付表单生成成功\n";
    
    echo "\n分析表单内容...\n";
    // 提取msg_id
    if (preg_match('/name="msg_id" value="([^"]+)"/', $formHtml, $matches)) {
        $msgId = $matches[1];
        echo "✅ MSG ID: " . $msgId . "\n";
        echo "✅ MSG ID长度: " . strlen($msgId) . " 字符\n";
        echo "✅ MSG ID格式: " . (preg_match('/^\d{24}$/', $msgId) ? '正确' : '错误') . "\n";
    } else {
        echo "❌ 未找到MSG ID字段\n";
    }
    
    // 提取biz_content
    if (preg_match('/name="biz_content" value="([^"]+)"/', $formHtml, $matches)) {
        $bizContent = html_entity_decode($matches[1]);
        echo "✅ BIZ Content: " . $bizContent . "\n";
        $bizData = json_decode($bizContent, true);
        if ($bizData) {
            echo "✅ BIZ Content解析成功\n";
            echo "   - 商户号: " . ($bizData['mer_id'] ?? 'N/A') . "\n";
            echo "   - 订单号: " . ($bizData['out_trade_no'] ?? 'N/A') . "\n";
            echo "   - 金额: " . ($bizData['order_amt'] ?? 'N/A') . "\n";
            echo "   - 支付方式: " . ($bizData['pay_mode'] ?? 'N/A') . "\n";
        } else {
            echo "❌ BIZ Content解析失败\n";
        }
    } else {
        echo "❌ 未找到BIZ Content字段\n";
    }
    
    // 提取签名
    if (preg_match('/name="sign" value="([^"]+)"/', $formHtml, $matches)) {
        $sign = $matches[1];
        echo "✅ 签名: " . substr($sign, 0, 20) . "...\n";
        echo "✅ 签名长度: " . strlen($sign) . " 字符\n";
        echo "✅ 签名类型: " . (strlen($sign) > 50 ? 'RSA签名' : 'MD5模拟签名') . "\n";
    } else {
        echo "❌ 未找到签名字段\n";
    }
    
    echo "\n📋 完整表单内容:\n";
    echo str_repeat("=", 50) . "\n";
    echo $formHtml . "\n";
    echo str_repeat("=", 50) . "\n";
    
    echo "\n✅ MSG ID修复测试完成！\n";
    echo "现在应该不会再出现 'msg id is invalid' 错误了。\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "堆栈: " . $e->getTraceAsString() . "\n";
} 