<?php

/**
 * 测试环境工商银行支付密钥设置脚本
 * 
 * 自动生成测试用的RSA密钥对，用于开发和测试环境
 * 注意：生产环境请使用工商银行提供的真实证书
 */

echo "🔧 工商银行支付测试密钥设置\n";
echo "===========================\n\n";

// 密钥目录
$keysDir = __DIR__ . '/storage/keys';

// 确保密钥目录存在
if (!is_dir($keysDir)) {
    mkdir($keysDir, 0755, true);
    echo "✅ 创建密钥目录: {$keysDir}\n";
}

echo "🔑 生成RSA密钥对...\n";

// 生成RSA密钥对配置
$config = [
    "digest_alg" => "sha256",
    "private_key_bits" => 2048,
    "private_key_type" => OPENSSL_KEYTYPE_RSA,
];

// 生成密钥对
$resource = openssl_pkey_new($config);

if (!$resource) {
    die("❌ 密钥生成失败: " . openssl_error_string() . "\n");
}

echo "✅ RSA密钥对生成成功\n";

// 导出私钥
openssl_pkey_export($resource, $privateKey);

// 导出公钥
$publicKeyDetails = openssl_pkey_get_details($resource);
$publicKey = $publicKeyDetails["key"];

// 保存私钥
$privateKeyPath = $keysDir . '/icbc_private_key.pem';
if (file_put_contents($privateKeyPath, $privateKey)) {
    chmod($privateKeyPath, 0600);
    echo "✅ 私钥已保存: {$privateKeyPath}\n";
} else {
    echo "❌ 私钥保存失败\n";
    exit(1);
}

// 保存公钥
$publicKeyPath = $keysDir . '/icbc_public_key.pem';
if (file_put_contents($publicKeyPath, $publicKey)) {
    chmod($publicKeyPath, 0644);
    echo "✅ 公钥已保存: {$publicKeyPath}\n";
} else {
    echo "❌ 公钥保存失败\n";
    exit(1);
}

// 保存网关公钥（测试环境使用相同的公钥）
$gatewayKeyPath = $keysDir . '/icbc_gateway_key.pem';
if (file_put_contents($gatewayKeyPath, $publicKey)) {
    chmod($gatewayKeyPath, 0644);
    echo "✅ 网关公钥已保存: {$gatewayKeyPath}\n";
} else {
    echo "❌ 网关公钥保存失败\n";
    exit(1);
}

echo "\n🧪 验证密钥功能...\n";

// 测试签名和验证
$testData = 'test_signature_' . time();
$signature = '';

// 使用私钥签名
if (openssl_sign($testData, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
    echo "✅ 私钥签名测试成功\n";
    
    // 使用公钥验证
    $result = openssl_verify($testData, $signature, $publicKey, OPENSSL_ALGO_SHA256);
    if ($result === 1) {
        echo "✅ 公钥验证测试成功\n";
    } else {
        echo "❌ 公钥验证测试失败\n";
        exit(1);
    }
} else {
    echo "❌ 私钥签名测试失败\n";
    exit(1);
}

// 创建配置示例文件
$configExample = $keysDir . '/example_config.php';
$configContent = '<?php

/**
 * 工商银行支付测试配置示例
 * 
 * 复制此文件到 config/icbc-pay.php 并修改相应参数
 */

return [
    // 测试环境配置
    \'environment\' => \'sandbox\',
    \'app_id\' => \'myapp01234567890123456789012\',
    \'mer_id\' => \'1001000000000000\',
    \'mer_prtcl_no\' => \'1001000000000000\',
    
    // 密钥文件路径
    \'private_key_path\' => storage_path(\'keys/icbc_private_key.pem\'),
    \'icbc_public_key_path\' => storage_path(\'keys/icbc_public_key.pem\'),
    \'gateway_key_path\' => storage_path(\'keys/icbc_gateway_key.pem\'),
    
    // 签名配置
    \'sign_type\' => \'RSA2\',
    \'charset\' => \'UTF-8\',
    \'format\' => \'json\',
    
    // 回调URL
    \'notify_url\' => env(\'APP_URL\', \'http://localhost\') . \'/icbc-pay/notify\',
    \'return_url\' => env(\'APP_URL\', \'http://localhost\') . \'/icbc-pay/return\',
    
    // 网关配置
    \'gateways\' => [
        \'sandbox\' => [
            \'base_url\' => \'https://gw.open.icbc.com.cn/sandbox\',
            \'payment_url\' => \'/api/cardbusiness/aggregatepay/consumepurchase\',
            \'query_url\' => \'/api/cardbusiness/aggregatepay/orderquery\',
        ],
    ],
    
    // 支付方式配置
    \'payment_methods\' => [
        \'wechat\' => [
            \'pay_mode\' => \'9\',
            \'access_type\' => \'1\',
            \'name\' => \'微信支付\',
        ],
        \'alipay\' => [
            \'pay_mode\' => \'10\',
            \'access_type\' => \'1\',
            \'name\' => \'支付宝支付\',
        ],
    ],
    
    // 开发配置
    \'dev\' => [
        \'mock_enabled\' => true,
        \'debug_enabled\' => true,
        \'test_mode\' => true,
    ],
];
';

if (file_put_contents($configExample, $configContent)) {
    echo "✅ 配置示例文件已创建: {$configExample}\n";
} else {
    echo "❌ 配置示例文件创建失败\n";
}

echo "\n📋 生成的文件清单:\n";
echo "=================\n";
echo "🔑 私钥文件: {$privateKeyPath}\n";
echo "🔑 公钥文件: {$publicKeyPath}\n";
echo "🔑 网关公钥: {$gatewayKeyPath}\n";
echo "📄 配置示例: {$configExample}\n";

echo "\n⚠️  重要提醒:\n";
echo "============\n";
echo "1. 这些是测试用密钥，仅用于开发环境\n";
echo "2. 生产环境必须使用工商银行提供的真实证书\n";
echo "3. 私钥文件权限已设置为600，请勿随意修改\n";
echo "4. 请将配置示例复制到正确位置并修改参数\n";

echo "\n🔧 下一步操作:\n";
echo "=============\n";
echo "1. 复制配置文件: cp {$configExample} config/icbc-pay.php\n";
echo "2. 修改环境变量文件 .env\n";
echo "3. 运行数据库迁移: php artisan migrate\n";
echo "4. 运行部署检查: php deploy_payment_system.php\n";
echo "5. 访问支付页面进行测试\n";

echo "\n✅ 测试密钥设置完成！\n";
echo "=====================\n";
echo "现在可以开始测试支付功能了。\n";

// 释放资源
openssl_pkey_free($resource); 