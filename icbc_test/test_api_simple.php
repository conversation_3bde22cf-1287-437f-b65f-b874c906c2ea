<?php

echo "🧪 简单API测试\n";
echo "============\n\n";

// 测试车牌验证
echo "1. 测试车牌验证规则\n";
echo "------------------\n";

$testCarNumbers = ['京A12345', '新M88888', '沪B66666', '粤A88888', 'ABC123'];
$pattern = '/^[\x{4e00}-\x{9fa5}A-Z0-9]{2,8}$/u';

foreach ($testCarNumbers as $carNumber) {
    $isValid = preg_match($pattern, $carNumber);
    $status = $isValid ? '✅' : '❌';
    echo "{$status} {$carNumber}\n";
}

echo "\n2. 测试cURL API调用\n";
echo "------------------\n";

$url = 'https://icbc.dev.hiwsoft.com/api/pay';
$data = [
    'car_number' => '京A12345',
    'amount' => '0.01',
    'payment_method' => 'wechat'
];

echo "请求URL: {$url}\n";
echo "请求数据: " . json_encode($data) . "\n\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "❌ cURL错误: {$error}\n";
} else {
    echo "✅ 请求成功\n";
    echo "响应内容: " . ($response ?: '(空)') . "\n\n";
    
    if ($response) {
        $responseData = json_decode($response, true);
        if ($responseData) {
            echo "解析后的响应:\n";
            print_r($responseData);
        } else {
            echo "⚠️ 响应不是有效的JSON格式\n";
        }
    }
}

echo "\n🏁 测试完成\n"; 