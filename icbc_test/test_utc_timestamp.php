<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🌐 测试工商银行支付UTC时间戳修复\n";
    echo "=================================\n\n";
    
    echo "⏰ 时间信息对比：\n";
    echo "系统时间 (CST): " . date('Y-m-d H:i:s') . "\n";
    echo "UTC时间: " . gmdate('Y-m-d H:i:s') . "\n";
    echo "Unix时间戳: " . time() . "\n";
    echo "Laravel时区: " . config('app.timezone') . "\n";
    echo "PHP时区: " . date_default_timezone_get() . "\n";
    
    echo "\n创建支付客户端...\n";
    $client = app(IcbcPayClient::class);
    echo "✅ 客户端创建成功\n";
    
    echo "\n生成支付表单（使用UTC时间）...\n";
    $testOrderData = [
        'order_id' => 'UTC_TEST_' . time(),
        'amount' => 0.01,
        'subject' => 'UTC时间戳测试订单',
        'payment_method' => 'wechat'
    ];
    
    $formHtml = $client->buildForm($testOrderData);
    echo "✅ 支付表单生成成功\n";
    
    echo "\n📋 UTC时间戳验证：\n";
    
    // 提取timestamp
    if (preg_match('/name="timestamp" value="([^"]+)"/', $formHtml, $matches)) {
        $timestamp = $matches[1];
        echo "✅ 请求时间戳: " . $timestamp . "\n";
        
        // 当前UTC时间
        $currentUtc = gmdate('Y-m-d H:i:s');
        echo "✅ 当前UTC时间: " . $currentUtc . "\n";
        
        // 时间差分析
        $timestampUnix = strtotime($timestamp . ' UTC');
        $currentUnix = time();
        $timeDiff = abs($currentUnix - $timestampUnix);
        echo "✅ 时间差: " . $timeDiff . " 秒\n";
        echo "✅ 时间同步: " . ($timeDiff <= 5 ? '正常' : '需要调整') . "\n";
        
        // 检查是否是UTC格式
        $utcCheck = strtotime($timestamp . ' UTC');
        $localCheck = strtotime($timestamp);
        echo "✅ UTC格式验证: " . ($utcCheck && $utcCheck !== $localCheck ? 'UTC格式' : '本地格式') . "\n";
    } else {
        echo "❌ 未找到timestamp字段\n";
    }
    
    // 提取msg_id检查UTC时间一致性
    if (preg_match('/name="msg_id" value="([^"]+)"/', $formHtml, $matches)) {
        $msgId = $matches[1];
        $msgIdTime = substr($msgId, 0, 14); // 前14位是时间
        $msgIdFormatted = 
            substr($msgIdTime, 0, 4) . '-' . 
            substr($msgIdTime, 4, 2) . '-' . 
            substr($msgIdTime, 6, 2) . ' ' . 
            substr($msgIdTime, 8, 2) . ':' . 
            substr($msgIdTime, 10, 2) . ':' . 
            substr($msgIdTime, 12, 2);
        
        echo "✅ MSG ID时间: " . $msgIdFormatted . "\n";
        echo "✅ MSG ID应为UTC格式\n";
        
        if (isset($timestamp)) {
            $timeDiff = abs(strtotime($timestamp . ' UTC') - strtotime($msgIdFormatted . ' UTC'));
            echo "✅ 时间一致性: " . ($timeDiff <= 2 ? '一致' : '不一致') . " (差异 {$timeDiff} 秒)\n";
        }
    }
    
    // 提取biz_content中的expire_time检查UTC
    if (preg_match('/name="biz_content" value="([^"]+)"/', $formHtml, $matches)) {
        $bizContent = html_entity_decode($matches[1]);
        $bizData = json_decode($bizContent, true);
        if ($bizData && isset($bizData['expire_time'])) {
            $expireTime = $bizData['expire_time'];
            echo "✅ 过期时间: " . $expireTime . " (应为UTC)\n";
            
            if (isset($timestamp)) {
                $expireDiff = strtotime($expireTime . ' UTC') - strtotime($timestamp . ' UTC');
                echo "✅ 有效期: " . round($expireDiff / 60) . " 分钟\n";
            }
        }
    }
    
    echo "\n🔄 UTC时间稳定性测试：\n";
    for ($i = 1; $i <= 3; $i++) {
        echo "第 {$i} 次测试: ";
        $testForm = $client->buildForm([
            'order_id' => 'UTC_STABILITY_' . time() . '_' . $i,
            'amount' => 0.01,
            'subject' => "UTC稳定性测试 #{$i}",
            'payment_method' => 'wechat'
        ]);
        
        if (preg_match('/name="timestamp" value="([^"]+)"/', $testForm, $matches)) {
            $testTimestamp = $matches[1];
            echo $testTimestamp . " ✅\n";
        } else {
            echo "❌ 时间戳提取失败\n";
        }
        
        sleep(1); // 等待1秒
    }
    
    echo "\n🎯 时区对比分析：\n";
    $currentTime = time();
    echo "Unix时间戳: " . $currentTime . "\n";
    echo "北京时间 (CST): " . date('Y-m-d H:i:s', $currentTime) . "\n";
    echo "UTC时间: " . gmdate('Y-m-d H:i:s', $currentTime) . "\n";
    echo "时区差异: 8小时\n";
    
    echo "\n💡 建议：\n";
    echo "如果工行服务器使用UTC时间，现在的UTC格式应该可以解决超时问题。\n";
    echo "如果仍有问题，可能需要微调时间偏移量。\n";
    
    echo "\n✅ UTC时间戳修复测试完成！\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
} 