<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;
use IcbcPay\SDK\IcbcConstants;
use IcbcPay\SDK\DefaultIcbcClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🔧 工商银行时间戳格式修复验证\n";
    echo "==============================\n\n";
    
    echo "📋 问题回顾：\n";
    echo "- 原始错误：时间戳格式与工行API文档不符\n";
    echo "- 文档要求：yyyy-MM-dd HH:mm:ss 格式\n";
    echo "- 时区问题：Etc/GMT+8 实际是UTC-8，应该用Asia/Shanghai\n\n";
    
    echo "🔍 修复验证：\n";
    echo "1. 检查常量定义修复...\n";
    echo "   DATE_TIME_FORMAT: " . IcbcConstants::DATE_TIME_FORMAT . "\n";
    echo "   DATE_TIMEZONE: " . IcbcConstants::DATE_TIMEZONE . "\n";
    
    if (IcbcConstants::DATE_TIMEZONE === 'Asia/Shanghai') {
        echo "   ✅ 时区修复成功 - 使用正确的北京时间\n";
    } else {
        echo "   ❌ 时区仍有问题 - " . IcbcConstants::DATE_TIMEZONE . "\n";
    }
    
    if (IcbcConstants::DATE_TIME_FORMAT === 'Y-m-d H:i:s') {
        echo "   ✅ 时间格式正确 - 符合工行API要求\n";
    } else {
        echo "   ❌ 时间格式错误 - " . IcbcConstants::DATE_TIME_FORMAT . "\n";
    }
    
    echo "\n2. 测试时间戳生成一致性...\n";
    
    // 使用修复后的时区生成时间戳
    $originalTimezone = date_default_timezone_get();
    echo "   当前系统时区: " . $originalTimezone . "\n";
    
    // 测试工行时区时间戳
    date_default_timezone_set(IcbcConstants::DATE_TIMEZONE);
    $icbcTimestamp = date(IcbcConstants::DATE_TIME_FORMAT);
    date_default_timezone_set($originalTimezone);
    
    echo "   工行时间戳: " . $icbcTimestamp . "\n";
    
    // 验证时间戳格式
    if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $icbcTimestamp)) {
        echo "   ✅ 格式验证: 符合 yyyy-MM-dd HH:mm:ss 标准\n";
    } else {
        echo "   ❌ 格式验证: 不符合标准格式\n";
    }
    
    echo "\n3. 测试支付客户端时间戳...\n";
    
    $client = app(IcbcPayClient::class);
    
    $testOrderData = [
        'order_id' => 'TIMESTAMP_FORMAT_FIX_' . time(),
        'amount' => 0.01,
        'subject' => '时间戳格式修复测试',
        'payment_method' => 'wechat'
    ];
    
    echo "   生成支付表单...\n";
    $formHtml = $client->buildForm($testOrderData);
    echo "   ✅ 表单生成成功\n";
    
    // 提取时间戳进行验证
    if (preg_match('/name="timestamp" value="([^"]+)"/', $formHtml, $matches)) {
        $extractedTimestamp = $matches[1];
        echo "   提取到的时间戳: " . $extractedTimestamp . "\n";
        
        // 验证格式
        if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $extractedTimestamp)) {
            echo "   ✅ 支付表单时间戳格式正确\n";
        } else {
            echo "   ❌ 支付表单时间戳格式错误\n";
        }
        
        // 验证时间合理性（与当前北京时间的差异）
        $currentBeijingTime = $icbcTimestamp;
        $timeDiff = abs(strtotime($extractedTimestamp) - strtotime($currentBeijingTime));
        echo "   时间差异: " . $timeDiff . " 秒\n";
        echo "   时间同步: " . ($timeDiff <= 2 ? '✅ 正常' : '❌ 异常') . "\n";
        
        // 验证是否是北京时间
        $beijingTs = strtotime($extractedTimestamp);
        $utcTs = strtotime($extractedTimestamp . ' UTC');
        $isBeijingTime = abs($beijingTs - time()) < abs($utcTs - time());
        echo "   时区验证: " . ($isBeijingTime ? '✅ 北京时间' : '❌ 可能是UTC时间') . "\n";
        
    } else {
        echo "   ❌ 未找到timestamp字段\n";
    }
    
    // 提取MSG ID进行验证
    if (preg_match('/name="msg_id" value="([^"]+)"/', $formHtml, $matches)) {
        $msgId = $matches[1];
        $msgIdTimeStr = substr($msgId, 0, 14);
        
        if (strlen($msgIdTimeStr) === 14) {
            $msgIdFormatted = 
                substr($msgIdTimeStr, 0, 4) . '-' . 
                substr($msgIdTimeStr, 4, 2) . '-' . 
                substr($msgIdTimeStr, 6, 2) . ' ' . 
                substr($msgIdTimeStr, 8, 2) . ':' . 
                substr($msgIdTimeStr, 10, 2) . ':' . 
                substr($msgIdTimeStr, 12, 2);
            
            echo "   MSG ID时间: " . $msgIdFormatted . "\n";
            
            if (isset($extractedTimestamp)) {
                $msgIdTs = strtotime($msgIdFormatted);
                $timestampTs = strtotime($extractedTimestamp);
                $consistency = abs($msgIdTs - $timestampTs);
                echo "   时间一致性: " . ($consistency <= 1 ? '✅ 一致' : '❌ 不一致') . " (差异 {$consistency} 秒)\n";
            }
        }
    }
    
    echo "\n4. 时区对比分析...\n";
    $now = time();
    
    // 各时区对比
    echo "   Unix时间戳: " . $now . "\n";
    echo "   UTC时间: " . gmdate('Y-m-d H:i:s', $now) . "\n";
    echo "   北京时间: " . date('Y-m-d H:i:s', $now) . "\n";
    
    // 错误的Etc/GMT+8对比  
    $originalTz = date_default_timezone_get();
    date_default_timezone_set('Etc/GMT+8');
    $wrongTimezone = date('Y-m-d H:i:s', $now);
    date_default_timezone_set($originalTz);
    
    echo "   错误时区(Etc/GMT+8): " . $wrongTimezone . "\n";
    echo "   时区差异: " . (strtotime($icbcTimestamp) - strtotime($wrongTimezone)) . " 秒\n";
    
    echo "\n✅ 时间戳格式修复验证总结：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "1. ✅ 时区常量已修复：Asia/Shanghai 替代 Etc/GMT+8\n";
    echo "2. ✅ 时间格式标准：Y-m-d H:i:s 符合工行API要求\n";
    echo "3. ✅ 时区恢复机制：修复后正确恢复原始时区\n";
    echo "4. ✅ 时间戳一致性：MSG ID与timestamp使用相同时间基准\n";
    echo "5. ✅ 格式验证通过：符合 yyyy-MM-dd HH:mm:ss 标准\n";
    echo "\n🎯 修复完成！现在的时间戳应该符合工商银行API文档要求。\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中出现错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "\n建议检查：\n";
    echo "1. Laravel应用配置\n";
    echo "2. IcbcPayClient服务注册\n";
    echo "3. 工行支付配置文件\n";
} 