<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 工商银行参数深度分析工具\n";
echo "=============================\n\n";

// 获取配置
$config = config('icbc-pay');

// 测试不同的参数组合
$testCases = [
    '最小参数集' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('Y-m-d H:i:s'),
        'total_fee' => '1',
        'body' => '测试',
        'pay_mode' => '9',
        'access_type' => '1',
    ],
    
    '添加必要IP' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('Y-m-d H:i:s'),
        'total_fee' => '1',
        'body' => '测试',
        'spbill_create_ip' => '**************',
        'pay_mode' => '9',
        'access_type' => '1',
    ],
    
    '添加回调URL' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('Y-m-d H:i:s'),
        'total_fee' => '1',
        'body' => '测试',
        'mer_url' => $config['notify_url'],
        'spbill_create_ip' => '**************',
        'pay_mode' => '9',
        'access_type' => '1',
    ],
    
    '添加货币类型' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('Y-m-d H:i:s'),
        'total_fee' => '1',
        'body' => '测试',
        'mer_url' => $config['notify_url'],
        'spbill_create_ip' => '**************',
        'fee_type' => '001',
        'pay_mode' => '9',
        'access_type' => '1',
    ],
    
    '完整参数' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('Y-m-d H:i:s'),
        'total_fee' => '1',
        'body' => '测试',
        'mer_url' => $config['notify_url'],
        'spbill_create_ip' => '**************',
        'fee_type' => '001',
        'pay_mode' => '9',
        'access_type' => '1',
        'device_info' => 'WEB',
        'notify_type' => 'HS',
        'result_type' => '0',
    ],
    
    '简化英文描述' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('Y-m-d H:i:s'),
        'total_fee' => '1',
        'body' => 'Test Payment',
        'mer_url' => $config['notify_url'],
        'spbill_create_ip' => '**************',
        'fee_type' => '001',
        'pay_mode' => '9',
        'access_type' => '1',
    ],
    
    '支付宝支付' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('Y-m-d H:i:s'),
        'total_fee' => '1',
        'body' => 'Test Payment',
        'mer_url' => $config['notify_url'],
        'spbill_create_ip' => '**************',
        'fee_type' => '001',
        'pay_mode' => '10', // 支付宝
        'access_type' => '1',
    ],
    
    '不同时间戳格式1' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('YmdHis'), // 紧凑格式
        'total_fee' => '1',
        'body' => 'Test Payment',
        'mer_url' => $config['notify_url'],
        'spbill_create_ip' => '**************',
        'fee_type' => '001',
        'pay_mode' => '9',
        'access_type' => '1',
    ],
    
    '不同时间戳格式2' => [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'TEST_' . time(),
        'orig_date_time' => date('Y-m-d\TH:i:s'), // ISO格式
        'total_fee' => '1',
        'body' => 'Test Payment',
        'mer_url' => $config['notify_url'],
        'spbill_create_ip' => '**************',
        'fee_type' => '001',
        'pay_mode' => '9',
        'access_type' => '1',
    ],
];

foreach ($testCases as $caseName => $bizContent) {
    echo "🧪 测试案例: {$caseName}\n";
    echo str_repeat('-', 40) . "\n";
    
    // 构建请求参数
    $requestParams = [
        'app_id' => $config['app_id'],
        'charset' => 'UTF-8',
        'format' => 'json',
        'sign_type' => 'RSA2',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => 'V1',
        'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
    ];
    
    // 生成签名
    $sign = generateSignature($requestParams, $config);
    if ($sign === false) {
        echo "❌ 签名生成失败\n\n";
        continue;
    }
    
    $requestParams['sign'] = $sign;
    
    // 构建URL
    $baseUrl = $config['gateway_url'] . $config['api_urls']['consume_purchase_ui'];
    $paymentUrl = $baseUrl . '?' . http_build_query($requestParams);
    
    // 测试请求
    $result = testPaymentUrl($paymentUrl);
    
    echo "参数数量: " . count($bizContent) . "\n";
    echo "请求结果: {$result}\n";
    
    if ($result === '✅ 成功') {
        echo "🎉 找到有效参数组合！\n";
        echo "业务参数:\n";
        foreach ($bizContent as $key => $value) {
            echo "  {$key}: {$value}\n";
        }
        echo "\n成功的支付URL已保存到 success_url.txt\n";
        file_put_contents('success_url.txt', $paymentUrl);
        
        // 同时保存成功的参数配置
        $successConfig = [
            'case_name' => $caseName,
            'biz_content' => $bizContent,
            'request_params' => $requestParams,
            'payment_url' => $paymentUrl
        ];
        file_put_contents('success_config.json', json_encode($successConfig, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        break;
    }
    
    echo "\n";
    
    // 避免请求过快
    sleep(1);
}

echo "=============================\n";
echo "深度分析完成！\n";

// 辅助函数
function generateSignature($params, $config) {
    // 排除sign参数
    unset($params['sign']);
    
    // 按字母顺序排序
    ksort($params);
    
    // 构建待签名字符串
    $signString = '';
    foreach ($params as $key => $value) {
        if ($value !== '' && $value !== null) {
            $signString .= $key . '=' . $value . '&';
        }
    }
    $signString = rtrim($signString, '&');
    
    // 加载私钥
    $privateKeyContent = file_get_contents($config['private_key_path']);
    $privateKey = openssl_pkey_get_private($privateKeyContent);
    
    if (!$privateKey) {
        return false;
    }
    
    // 生成签名
    $signature = '';
    if (openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
        return base64_encode($signature);
    }
    
    return false;
}

function testPaymentUrl($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; ICBC-Test/1.0)');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if (strpos($response, '参数校验失败') !== false) {
        return '❌ 参数校验失败';
    } elseif (strpos($response, '支付') !== false || strpos($response, 'pay') !== false) {
        return '✅ 成功';
    } elseif (strpos($response, '错误') !== false) {
        return '⚠️ 其他错误';
    } else {
        return "❔ 未知响应 (HTTP {$httpCode})";
    }
} 