<?php

require_once '../vendor/autoload.php';

try {
    $app = require_once '../bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "📋 工商银行订单号格式修复验证\n";
    echo "=================================\n\n";
    
    echo "🔍 问题分析：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "根据工商银行API文档，out_trade_no 参数要求：\n";
    echo "✅ 只能包含数字、大小写字母\n";
    echo "✅ 长度不超过50字符\n";
    echo "✅ 在同一商户号下唯一\n";
    echo "❌ 不能包含下划线、连字符等特殊字符\n\n";
    
    echo "📊 修复前后对比：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "修复前（错误）：PARK_20250524161059123456789  ❌ 包含下划线\n";
    echo "修复后（正确）：PARK20250524161059123456789   ✅ 只包含字母数字\n\n";
    
    // 1. 测试新的订单号生成
    echo "🔧 第一步：测试新的订单号生成逻辑\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // 测试默认前缀
    echo "默认前缀测试：\n";
    for ($i = 1; $i <= 5; $i++) {
        $orderNo = \IcbcPay\Models\PaymentRecord::generateUniqueOrderNo();
        $isValid = preg_match('/^[A-Za-z0-9]+$/', $orderNo);
        $length = strlen($orderNo);
        
        echo "  订单号 {$i}：{$orderNo}\n";
        echo "    格式检查：" . ($isValid ? "✅ 通过" : "❌ 失败") . "\n";
        echo "    长度检查：{$length} 字符 " . ($length <= 50 ? "✅ 通过" : "❌ 失败") . "\n";
        echo "    唯一性：" . (strlen($orderNo) > 10 ? "✅ 具有唯一性" : "❌ 可能重复") . "\n\n";
        
        usleep(10000); // 10ms延迟确保时间戳不同
    }
    
    // 2. 测试不同前缀
    echo "🧪 第二步：测试不同前缀处理\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $testPrefixes = [
        'PARK' => '正常前缀',
        'PARKING' => '长前缀',
        'TEST_ORDER_' => '包含下划线（需要清理）',
        'PAY-2024' => '包含连字符（需要清理）',
        'ORDER@123' => '包含特殊字符（需要清理）',
        'VERYLONGPREFIXTOTEST' => '超长前缀（需要截断）',
        '123ABC' => '数字开头',
        '' => '空前缀',
    ];
    
    foreach ($testPrefixes as $prefix => $description) {
        echo "测试前缀：\"{$prefix}\" ({$description})\n";
        
        $orderNo = \IcbcPay\Models\PaymentRecord::generateUniqueOrderNo($prefix);
        $isValid = preg_match('/^[A-Za-z0-9]+$/', $orderNo);
        $length = strlen($orderNo);
        
        echo "  生成结果：{$orderNo}\n";
        echo "  格式检查：" . ($isValid ? "✅ 通过" : "❌ 失败") . "\n";
        echo "  长度检查：{$length} 字符 " . ($length <= 50 ? "✅ 通过" : "❌ 失败") . "\n";
        
        // 检查前缀处理是否正确
        $cleanPrefix = preg_replace('/[^A-Za-z0-9]/', '', $prefix);
        if (strlen($cleanPrefix) > 4) {
            $cleanPrefix = substr($cleanPrefix, 0, 4);
        }
        
        if ($cleanPrefix && strpos($orderNo, $cleanPrefix) === 0) {
            echo "  前缀处理：✅ 正确清理并应用\n";
        } elseif (empty($cleanPrefix)) {
            echo "  前缀处理：✅ 空前缀正确处理\n";
        } else {
            echo "  前缀处理：⚠️  可能有问题\n";
        }
        echo "\n";
        
        usleep(10000); // 延迟避免重复
    }
    
    // 3. 测试订单号格式验证函数
    echo "🔍 第三步：验证函数测试\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $testOrderNos = [
        'PARK20250524161059123456789' => true,   // 正确格式
        'PARK_20250524161059123456789' => false, // 包含下划线
        'PARK-20250524161059123456789' => false, // 包含连字符
        'PARK@20250524161059123456789' => false, // 包含特殊字符
        'park20250524161059123456789' => true,   // 小写字母
        'PARK20250524161059' => true,            // 较短
        str_repeat('A', 51) => false,            // 超长
        '123456789012345678901234567890' => true, // 纯数字
        'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890' => true, // 字母数字混合
    ];
    
    echo "订单号格式验证测试：\n";
    foreach ($testOrderNos as $orderNo => $expected) {
        $isValid = preg_match('/^[A-Za-z0-9]+$/', $orderNo) && strlen($orderNo) <= 50;
        $result = $isValid === $expected ? "✅ 通过" : "❌ 失败";
        $length = strlen($orderNo);
        
        echo "  \"{$orderNo}\" ({$length}字符)\n";
        echo "    期望：" . ($expected ? "有效" : "无效") . " | 实际：" . ($isValid ? "有效" : "无效") . " | {$result}\n\n";
    }
    
    // 4. 测试批量生成的唯一性
    echo "🎯 第四步：批量生成唯一性测试\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $batchSize = 20;
    $generatedOrderNos = [];
    $duplicateFound = false;
    
    echo "生成 {$batchSize} 个订单号测试唯一性：\n";
    
    for ($i = 1; $i <= $batchSize; $i++) {
        $orderNo = \IcbcPay\Models\PaymentRecord::generateUniqueOrderNo();
        
        if (in_array($orderNo, $generatedOrderNos)) {
            echo "❌ 发现重复订单号：{$orderNo}\n";
            $duplicateFound = true;
        } else {
            $generatedOrderNos[] = $orderNo;
        }
        
        echo "  {$i}. {$orderNo}\n";
        usleep(1000); // 1ms延迟
    }
    
    echo "\n唯一性测试结果：" . ($duplicateFound ? "❌ 发现重复" : "✅ 全部唯一") . "\n\n";
    
    // 5. 模拟支付订单创建
    echo "💳 第五步：模拟支付订单创建测试\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    try {
        // 模拟创建支付记录
        $testOrderData = [
            'car_number' => '测试A123',
            'amount' => '0.01',
            'payment_method' => 'wechat',
            'parking_duration' => 30
        ];
        
        echo "模拟创建支付订单：\n";
        echo "  车牌号：{$testOrderData['car_number']}\n";
        echo "  金额：{$testOrderData['amount']} 元\n";
        echo "  支付方式：{$testOrderData['payment_method']}\n";
        echo "  停车时长：{$testOrderData['parking_duration']} 分钟\n\n";
        
        $paymentRecord = \IcbcPay\Models\PaymentRecord::create([
            'out_trade_no' => \IcbcPay\Models\PaymentRecord::generateUniqueOrderNo(),
            'total_amount' => $testOrderData['amount'],
            'subject' => '停车费支付',
            'payment_method' => $testOrderData['payment_method'],
            'car_number' => $testOrderData['car_number'],
            'parking_duration' => $testOrderData['parking_duration'],
            'status' => 'pending',
        ]);
        
        $orderNo = $paymentRecord->out_trade_no;
        $isValid = preg_match('/^[A-Za-z0-9]+$/', $orderNo) && strlen($orderNo) <= 50;
        
        echo "✅ 支付记录创建成功\n";
        echo "  订单号：{$orderNo}\n";
        echo "  格式验证：" . ($isValid ? "✅ 符合工商银行要求" : "❌ 格式错误") . "\n";
        echo "  数据库ID：{$paymentRecord->id}\n";
        echo "  创建时间：{$paymentRecord->created_at}\n";
        
        // 清理测试数据
        $paymentRecord->delete();
        echo "  ✅ 测试数据已清理\n\n";
        
    } catch (Exception $e) {
        echo "❌ 支付订单创建失败：{$e->getMessage()}\n\n";
    }
    
    // 6. 兼容性检查
    echo "🔄 第六步：向后兼容性检查\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // 检查现有订单号格式
    $existingOrders = \IcbcPay\Models\PaymentRecord::orderBy('created_at', 'desc')->limit(5)->get();
    
    if ($existingOrders->count() > 0) {
        echo "检查现有订单号格式：\n";
        foreach ($existingOrders as $order) {
            $orderNo = $order->out_trade_no;
            $isValid = preg_match('/^[A-Za-z0-9]+$/', $orderNo) && strlen($orderNo) <= 50;
            
            echo "  {$orderNo} - " . ($isValid ? "✅ 符合要求" : "⚠️  需要注意") . "\n";
        }
    } else {
        echo "✅ 暂无现有订单，新系统可以直接使用修复后的格式\n";
    }
    echo "\n";
    
    // 7. 最终总结
    echo "🎉 修复总结\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "✅ 问题根因：订单号包含工商银行不允许的特殊字符（下划线等）\n";
    echo "✅ 修复方案：\n";
    echo "   - 移除默认前缀中的下划线（PARK_ → PARK）\n";
    echo "   - 添加字符过滤，只保留字母和数字\n";
    echo "   - 增加长度检查，确保不超过50字符\n";
    echo "   - 优化UUID后备方案，移除特殊字符\n";
    echo "✅ 格式保证：\n";
    echo "   - 只包含数字、大小写字母\n";
    echo "   - 长度控制在50字符以内\n";
    echo "   - 保持唯一性\n";
    echo "   - 向后兼容\n\n";
    
    echo "🔔 重要说明：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "1. 此修复解决了订单号格式不符合工商银行要求的问题\n";
    echo "2. 新生成的订单号完全符合工行API规范\n";
    echo "3. 既有订单号如果包含特殊字符，仍然可以查询和处理\n";
    echo "4. 建议重新测试支付流程，观察工行响应改善\n";
    echo "5. 结合金额转换修复，应该能显著提升支付成功率\n\n";
    
    echo "💡 下一步建议：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "1. 在浏览器中测试新订单号格式的支付\n";
    echo "2. 观察工行网关是否接受新格式\n";
    echo "3. 监控支付成功率是否提升\n";
    echo "4. 检查回调处理是否正常\n";
    echo "5. 更新测试脚本，使用新的订单号格式\n";
    
} catch (Exception $e) {
    echo "❌ 测试执行出错：" . $e->getMessage() . "\n";
    echo "📄 错误文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}

echo "\n" . "🎊 订单号格式修复验证完成！" . "\n";
echo "=" . str_repeat("=", 45) . "\n"; 