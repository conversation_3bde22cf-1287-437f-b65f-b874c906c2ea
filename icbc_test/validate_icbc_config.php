<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "==========================================\n";
echo "工商银行支付配置验证\n";
echo "==========================================\n\n";

// 获取配置
$config = config('icbc-pay');

echo "1. 基础配置检查\n";
echo "----------------\n";
echo "APP_ID: " . ($config['app_id'] ?: '❌ 未配置') . "\n";
echo "MER_ID: " . ($config['mer_id'] ?: '❌ 未配置') . "\n";
echo "MER_PRTCL_NO: " . ($config['mer_prtcl_no'] ?: '❌ 未配置') . "\n";
echo "GATEWAY_URL: " . ($config['gateway_url'] ?: '❌ 未配置') . "\n";
echo "NOTIFY_URL: " . ($config['notify_url'] ?: '❌ 未配置') . "\n";
echo "\n";

echo "2. 密钥配置检查\n";
echo "----------------\n";

// 检查私钥
$privateKeyExists = false;
if (!empty($config['private_key'])) {
    echo "私钥: ✅ 环境变量配置\n";
    $privateKeyExists = true;
} elseif (!empty($config['private_key_path']) && file_exists($config['private_key_path'])) {
    echo "私钥文件: ✅ " . $config['private_key_path'] . "\n";
    echo "文件大小: " . filesize($config['private_key_path']) . " 字节\n";
    $privateKeyExists = true;
} else {
    echo "私钥: ❌ 未配置或文件不存在\n";
}

// 检查公钥
if (!empty($config['public_key_path']) && file_exists($config['public_key_path'])) {
    echo "公钥文件: ✅ " . $config['public_key_path'] . "\n";
    echo "文件大小: " . filesize($config['public_key_path']) . " 字节\n";
} else {
    echo "公钥文件: ❌ 未配置或文件不存在\n";
}

// 检查网关公钥
if (!empty($config['apigw_public_key_path']) && file_exists($config['apigw_public_key_path'])) {
    echo "网关公钥文件: ✅ " . $config['apigw_public_key_path'] . "\n";
    echo "文件大小: " . filesize($config['apigw_public_key_path']) . " 字节\n";
} else {
    echo "网关公钥文件: ❌ 未配置或文件不存在\n";
}

echo "\n";

echo "3. 支付配置检查\n";
echo "----------------\n";
echo "交易币种: " . $config['payment_config']['fee_type'] . "\n";
echo "订单有效期: " . $config['payment_config']['expire_time'] . " 分钟\n";
echo "通知类型: " . $config['payment_config']['notify_type'] . "\n";
echo "结果发送类型: " . $config['payment_config']['result_type'] . "\n";
echo "UI模式: " . ($config['payment_config']['use_ui_mode'] ? '是' : '否') . "\n";
echo "\n";

echo "4. API接口检查\n";
echo "----------------\n";
foreach ($config['api_urls'] as $name => $url) {
    echo ucfirst($name) . ": " . $url . "\n";
}
echo "\n";

echo "5. 网络连接测试\n";
echo "----------------\n";
$testUrl = $config['gateway_url'] . $config['api_urls']['consume_purchase_ui'];
echo "测试URL: " . $testUrl . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'ICBC-Config-Validator/1.0');
curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取HTTP头

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "网络连接: ❌ " . $error . "\n";
} else {
    echo "HTTP状态码: " . $httpCode . "\n";
    if ($httpCode == 200 || $httpCode == 400 || $httpCode == 405) {
        echo "网络连接: ✅ 可以访问工商银行网关\n";
    } else {
        echo "网络连接: ⚠️  状态码异常\n";
    }
}

echo "\n";

echo "6. 私钥验证测试\n";
echo "----------------\n";
if ($privateKeyExists) {
    try {
        // 获取私钥内容
        $privateKeyContent = '';
        if (!empty($config['private_key'])) {
            $privateKeyContent = $config['private_key'];
        } elseif (!empty($config['private_key_path']) && file_exists($config['private_key_path'])) {
            $privateKeyContent = file_get_contents($config['private_key_path']);
        }
        
        // 尝试加载私钥
        $privateKey = openssl_pkey_get_private($privateKeyContent);
        if ($privateKey) {
            echo "私钥格式: ✅ 私钥格式正确\n";
            
            // 测试签名
            $testData = 'test_signature_data';
            $signature = '';
            if (openssl_sign($testData, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
                echo "签名测试: ✅ 可以正常生成签名\n";
                echo "签名长度: " . strlen(base64_encode($signature)) . " 字符\n";
            } else {
                echo "签名测试: ❌ 签名生成失败\n";
            }
            
            openssl_pkey_free($privateKey);
        } else {
            echo "私钥格式: ❌ 私钥格式错误或无法加载\n";
            echo "OpenSSL错误: " . openssl_error_string() . "\n";
        }
    } catch (Exception $e) {
        echo "私钥验证: ❌ " . $e->getMessage() . "\n";
    }
} else {
    echo "私钥验证: ❌ 没有私钥可供验证\n";
}

echo "\n";

echo "7. 环境变量检查\n";
echo "----------------\n";
$envVars = [
    'ICBC_APP_ID',
    'ICBC_MER_ID', 
    'ICBC_MER_PRTCL_NO',
    'ICBC_GATEWAY_URL',
    'ICBC_NOTIFY_URL',
    'ICBC_USE_UI_MODE',
    'ICBC_PRIVATE_KEY_PATH'
];

foreach ($envVars as $var) {
    $value = env($var);
    if ($value !== null) {
        echo "{$var}: ✅ " . (strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value) . "\n";
    } else {
        echo "{$var}: ❌ 未设置\n";
    }
}

echo "\n==========================================\n";
echo "配置验证完成\n";
echo "==========================================\n"; 