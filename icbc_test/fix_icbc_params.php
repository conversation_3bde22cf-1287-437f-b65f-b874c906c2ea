<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

echo "工商银行参数校验失败 - 诊断修复工具\n";
echo "==========================================\n\n";

// 获取配置
$config = config('icbc-pay');

echo "🔍 开始诊断参数问题...\n\n";

// 创建测试订单
$paymentRecord = PaymentRecord::create([
    'out_trade_no' => 'FIX_' . time() . rand(1000, 9999),
    'total_amount' => 0.01,
    'subject' => '参数修复测试',
    'payment_method' => 'wechat',
    'car_number' => '修复A123',
    'parking_duration' => 30,
    'status' => 'pending'
]);

echo "测试订单号: {$paymentRecord->out_trade_no}\n\n";

// 问题1: 检查时间戳格式
echo "1. 🕒 检查时间戳格式问题\n";
echo "   工商银行可能要求特定的时间戳格式\n";

$formats = [
    'Y-m-d H:i:s',           // 标准格式
    'Y-m-d\TH:i:s',          // ISO格式 
    'Y-m-d\TH:i:s\Z',        // UTC格式
    'YmdHis',                // 紧凑格式
    'Y-m-d H:i:s.u',         // 微秒格式
];

foreach ($formats as $format) {
    $timestamp = date($format);
    echo "   - {$format}: {$timestamp}\n";
}

// 问题2: 检查必需参数完整性
echo "\n2. 📋 检查必需参数完整性\n";

$requiredBizParams = [
    'mer_id' => $config['mer_id'],
    'mer_prtcl_no' => $config['mer_prtcl_no'],
    'out_trade_no' => $paymentRecord->out_trade_no,
    'orig_date_time' => date('Y-m-d H:i:s'),
    'total_fee' => '1',
    'body' => '测试',
    'mer_url' => $config['notify_url'],
    'spbill_create_ip' => '127.0.0.1', // 修改IP地址
    'fee_type' => '001',
    'pay_mode' => '9',
    'access_type' => '1',
];

// 可能缺失的重要参数
$optionalParams = [
    'device_info' => 'WEB',
    'limit_credit_pay' => 'N',
    'time_start' => date('YmdHis'),
    'time_expire' => date('YmdHis', time() + 1800),
    'goods_tag' => '',
    'attach' => '',
];

echo "   必需参数检查:\n";
foreach ($requiredBizParams as $key => $value) {
    $status = !empty($value) ? '✅' : '❌';
    echo "   {$status} {$key}: {$value}\n";
}

echo "\n   可选但重要的参数:\n";
foreach ($optionalParams as $key => $value) {
    echo "   ⚠️  {$key}: {$value}\n";
}

// 问题3: 尝试不同的参数组合
echo "\n3. 🧪 测试不同参数组合\n";

$testCases = [
    'minimal' => $requiredBizParams,
    'with_device_info' => array_merge($requiredBizParams, ['device_info' => 'WEB']),
    'with_ip_fixed' => array_merge($requiredBizParams, ['spbill_create_ip' => '**************']),
    'iso_timestamp' => array_merge($requiredBizParams, ['orig_date_time' => date('Y-m-d\TH:i:s')]),
    'compact_timestamp' => array_merge($requiredBizParams, ['orig_date_time' => date('YmdHis')]),
];

foreach ($testCases as $caseName => $bizContent) {
    echo "\n   测试案例: {$caseName}\n";
    
    // 构建请求参数
    $requestParams = [
        'app_id' => $config['app_id'],
        'charset' => 'UTF-8',
        'format' => 'json',
        'sign_type' => 'RSA2',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => 'V1',
        'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
    ];
    
    // 生成签名
    $sign = generateSign($requestParams, $config);
    $requestParams['sign'] = $sign;
    
    // 构建URL
    $url = $config['gateway_url'] . $config['api_urls']['consume_purchase_ui'] . '?' . http_build_query($requestParams);
    
    // 测试请求
    $result = testRequest($url);
    echo "   结果: {$result}\n";
    
    if ($result === '✅ 成功' || strpos($result, '支付') !== false) {
        echo "   🎉 找到有效配置！\n";
        echo "   URL: {$url}\n";
        break;
    }
}

// 问题4: 检查字符编码问题
echo "\n4. 🔤 检查字符编码问题\n";

$testStrings = [
    '测试' => [
        'original' => '测试',
        'utf8' => mb_convert_encoding('测试', 'UTF-8'),
        'urlencode' => urlencode('测试'),
        'json_encode' => json_encode('测试', JSON_UNESCAPED_UNICODE),
    ]
];

foreach ($testStrings as $label => $encodings) {
    echo "   字符串 '{$label}' 的不同编码:\n";
    foreach ($encodings as $type => $encoded) {
        echo "     {$type}: {$encoded}\n";
    }
}

// 问题5: 签名调试
echo "\n5. 🔐 签名算法调试\n";

$testParams = [
    'app_id' => $config['app_id'],
    'charset' => 'UTF-8',
    'format' => 'json',
    'sign_type' => 'RSA2',
    'timestamp' => '2025-05-23 16:30:00', // 固定时间戳
    'version' => 'V1',
    'biz_content' => '{"mer_id":"' . $config['mer_id'] . '","out_trade_no":"TEST123","total_fee":"1"}',
];

echo "   测试签名生成:\n";
$signString = buildSignString($testParams);
echo "   待签名字符串: {$signString}\n";
echo "   字符串长度: " . strlen($signString) . "\n";
echo "   字符串MD5: " . md5($signString) . "\n";

$signature = generateSign($testParams, $config);
echo "   生成的签名: " . substr($signature, 0, 50) . "...\n";
echo "   签名长度: " . strlen($signature) . "\n";

// 问题6: 提供修复建议
echo "\n6. 💡 修复建议\n";

$suggestions = [
    "1. 时间戳格式",
    "   推荐使用: Y-m-d H:i:s 格式",
    "   避免使用微秒和时区信息",
    "",
    "2. IP地址",
    "   spbill_create_ip 使用真实的公网IP",
    "   避免使用 127.0.0.1 或内网IP",
    "",
    "3. 字符编码", 
    "   确保所有字符串都是UTF-8编码",
    "   中文字符使用 JSON_UNESCAPED_UNICODE",
    "",
    "4. 必需参数",
    "   确保所有必需参数都不为空",
    "   检查 mer_url 是否可访问",
    "",
    "5. 签名算法",
    "   参数排序必须按字母顺序",
    "   空值参数不参与签名",
    "   使用 RSA2 (SHA256) 算法",
];

foreach ($suggestions as $suggestion) {
    echo "   {$suggestion}\n";
}

echo "\n==========================================\n";
echo "诊断完成！请根据上述结果调整参数配置。\n";
echo "==========================================\n";

// 辅助函数
function buildSignString($params) {
    unset($params['sign']);
    ksort($params);
    $signString = '';
    foreach ($params as $key => $value) {
        if ($value !== '' && $value !== null) {
            $signString .= $key . '=' . $value . '&';
        }
    }
    return rtrim($signString, '&');
}

function generateSign($params, $config) {
    $signString = buildSignString($params);
    $privateKeyContent = file_get_contents($config['private_key_path']);
    $privateKey = openssl_pkey_get_private($privateKeyContent);
    
    if (!$privateKey) {
        return 'ERROR: 私钥加载失败';
    }
    
    $signature = '';
    if (openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
        return base64_encode($signature);
    }
    
    return 'ERROR: 签名生成失败';
}

function testRequest($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; ICBC-Fix/1.0)');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if (strpos($response, '参数校验失败') !== false) {
        return '❌ 参数校验失败';
    } elseif (strpos($response, '支付') !== false) {
        return '✅ 成功';
    } else {
        return "⚠️ 未知响应 (HTTP {$httpCode})";
    }
} 