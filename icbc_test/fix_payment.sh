#!/bin/bash
echo "应用支付问题修复补丁..."

# 清理缓存
php artisan cache:clear
php artisan config:clear

# 重新生成自动加载
composer dump-autoload

# 设置正确的文件权限
if [ -f "/www/wwwroot/icbc-pay.test/storage/keys/icbc_private_key.pem" ]; then
    chmod 600 "/www/wwwroot/icbc-pay.test/storage/keys/icbc_private_key.pem"
    echo "✅ 私钥文件权限已修复"
fi

# 检查storage目录权限
chmod -R 755 storage/
chmod -R 755 storage/keys/

echo "✅ 修复补丁应用完成"
