<?php

echo "🔧 工商银行证书配置设置工具\n";
echo "============================\n\n";

// 检查storage/keys目录
$keysDir = 'storage/keys';
if (!is_dir($keysDir)) {
    mkdir($keysDir, 0755, true);
    echo "✅ 创建证书目录: {$keysDir}\n";
} else {
    echo "✅ 证书目录已存在: {$keysDir}\n";
}

echo "\n📋 当前配置状态\n";
echo "================\n";

$privateKeyPath = 'storage/keys/icbc_private_key.pem';
$publicKeyPath = 'storage/keys/icbc_public_key.pem';

echo "私钥文件路径: {$privateKeyPath}\n";
echo "公钥文件路径: {$publicKeyPath}\n";

$privateKeyExists = file_exists($privateKeyPath);
$publicKeyExists = file_exists($publicKeyPath);

echo "私钥文件状态: " . ($privateKeyExists ? "✅ 存在" : "❌ 不存在") . "\n";
echo "公钥文件状态: " . ($publicKeyExists ? "✅ 存在" : "❌ 不存在") . "\n";

if (!$privateKeyExists || !$publicKeyExists) {
    echo "\n🚨 证书文件缺失！\n";
    echo "=================\n";
    echo "工商银行支付功能无法正常工作，因为证书文件缺失。\n\n";
    
    echo "⚠️ 重要提醒：\n";
    echo "工商银行的正式证书必须从工商银行开放平台获取，\n";
    echo "这些证书与您的商户账户和APP_ID关联。\n\n";
    
    echo "📞 获取正式证书的步骤：\n";
    echo "1. 登录工商银行开放平台 (https://open.icbc.com.cn)\n";
    echo "2. 进入您的商户管理后台\n";
    echo "3. 下载与您的APP_ID关联的证书文件\n";
    echo "4. 将证书文件放置到指定目录\n\n";
    
    $createDemo = false;
    
    // 检查是否为交互式环境
    if (php_sapi_name() === 'cli') {
        echo "🧪 是否创建演示用的测试证书？(仅用于开发测试) [y/N]: ";
        $input = trim(fgets(STDIN));
        $createDemo = (strtolower($input) === 'y' || strtolower($input) === 'yes');
    }
    
    if ($createDemo) {
        echo "\n🔧 创建演示证书\n";
        echo "================\n";
        
        // 生成测试用的RSA密钥对
        $config = [
            "digest_alg" => "sha256",
            "private_key_bits" => 2048,
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ];
        
        $res = openssl_pkey_new($config);
        
        if ($res) {
            openssl_pkey_export($res, $privateKey);
            $publicKeyDetails = openssl_pkey_get_details($res);
            $publicKey = $publicKeyDetails["key"];
            
            // 保存私钥
            file_put_contents($privateKeyPath, $privateKey);
            echo "✅ 创建演示私钥: {$privateKeyPath}\n";
            
            // 保存公钥
            file_put_contents($publicKeyPath, $publicKey);
            echo "✅ 创建演示公钥: {$publicKeyPath}\n";
            
            // 设置权限
            chmod($privateKeyPath, 0600);
            chmod($publicKeyPath, 0644);
            
            echo "\n⚠️ 重要警告：\n";
            echo "这些是演示用的测试证书，仅用于开发测试！\n";
            echo "不能用于生产环境或真实的工商银行支付！\n";
            echo "生产环境必须使用工商银行提供的正式证书！\n\n";
            
            // 创建配置说明文件
            $configGuide = "# 工商银行证书配置说明

## 当前状态
- 使用演示测试证书
- 仅用于开发测试
- 无法连接真实的工商银行API

## 配置正式证书的步骤

### 1. 获取正式证书
1. 登录工商银行开放平台: https://open.icbc.com.cn
2. 进入商户管理后台
3. 找到您的应用 (APP_ID: " . config('icbc-pay.app_id', '未配置') . ")
4. 下载与该应用关联的证书文件

### 2. 替换证书文件
将下载的正式证书文件分别重命名并替换：
- 商户私钥 -> {$privateKeyPath}
- 商户公钥 -> {$publicKeyPath}

### 3. 验证配置
运行验证脚本检查配置是否正确：
```
php fix_icbc_certificate.php
```

### 4. 重要提醒
- 私钥文件权限应设为 600 (仅所有者可读写)
- 公钥文件权限应设为 644 (所有者可读写，其他人只读)
- 确保 APP_ID 与证书对应
- 确保商户号和协议号正确

## 技术支持
如遇问题请联系：
- 工商银行技术支持: 95588
- 开放平台在线客服
";
            
            file_put_contents('storage/keys/README.md', $configGuide);
            echo "✅ 创建配置说明: storage/keys/README.md\n";
            
        } else {
            echo "❌ 创建演示证书失败: " . openssl_error_string() . "\n";
        }
    } else {
        echo "\n📋 手动配置指南\n";
        echo "===============\n";
        echo "请按以下步骤配置正式证书：\n\n";
        echo "1. 从工商银行开放平台下载您的证书文件\n";
        echo "2. 将私钥文件保存为: {$privateKeyPath}\n";
        echo "3. 将公钥文件保存为: {$publicKeyPath}\n";
        echo "4. 设置正确的文件权限:\n";
        echo "   chmod 600 {$privateKeyPath}\n";
        echo "   chmod 644 {$publicKeyPath}\n";
        echo "5. 运行验证脚本: php fix_icbc_certificate.php\n\n";
    }
}

// 如果证书文件存在，验证配置
if ($privateKeyExists && $publicKeyExists) {
    echo "\n🔍 验证现有证书\n";
    echo "================\n";
    
    // 检查私钥
    $privateKeyContent = file_get_contents($privateKeyPath);
    $privateKey = openssl_pkey_get_private($privateKeyContent);
    
    if ($privateKey) {
        echo "✅ 私钥格式正确\n";
        
        // 检查公钥
        $publicKeyContent = file_get_contents($publicKeyPath);
        $publicKey = openssl_pkey_get_public($publicKeyContent);
        
        if ($publicKey) {
            echo "✅ 公钥格式正确\n";
            
            // 测试匹配性
            $testData = 'certificate_test_' . time();
            $signature = '';
            
            if (openssl_sign($testData, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
                $verifyResult = openssl_verify($testData, $signature, $publicKey, OPENSSL_ALGO_SHA256);
                
                if ($verifyResult === 1) {
                    echo "✅ 私钥和公钥匹配\n";
                    echo "\n🎉 证书配置正确！可以运行完整的诊断脚本：\n";
                    echo "   php fix_icbc_certificate.php\n";
                } else {
                    echo "❌ 私钥和公钥不匹配\n";
                    echo "\n🚨 这是导致支付失败的主要原因！\n";
                    echo "请确认证书文件是否为同一套证书。\n";
                }
            } else {
                echo "❌ 私钥签名测试失败\n";
            }
            
            openssl_pkey_free($publicKey);
        } else {
            echo "❌ 公钥格式错误\n";
        }
        
        openssl_pkey_free($privateKey);
    } else {
        echo "❌ 私钥格式错误\n";
        echo "错误信息: " . openssl_error_string() . "\n";
    }
}

echo "\n💡 接下来的步骤\n";
echo "===============\n";

if (!$privateKeyExists || !$publicKeyExists) {
    echo "1. 🔑 配置正式的工商银行证书文件\n";
    echo "2. 🧪 运行证书验证脚本\n";
    echo "3. 🚀 测试支付功能\n";
} else {
    echo "1. 🧪 运行完整诊断: php fix_icbc_certificate.php\n";
    echo "2. 🚀 测试支付功能\n";
    echo "3. 📞 如仍有问题，联系工商银行技术支持\n";
}

echo "\n============================\n";
echo "证书配置工具完成！\n";
echo "============================\n"; 