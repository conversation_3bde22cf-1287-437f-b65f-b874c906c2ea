<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 工商银行基础配置问题排查\n";
echo "============================\n\n";

// 获取配置
$config = config('icbc-pay');

echo "1️⃣ 检查API路径配置\n";
echo "==================\n";

$apiUrls = $config['api_urls'];
echo "可用的API端点:\n";
foreach ($apiUrls as $name => $path) {
    echo "  {$name}: {$path}\n";
}

echo "\n当前使用的端点:\n";
$currentEndpoint = $config['gateway_url'] . $config['api_urls']['consume_purchase_ui'];
echo "  完整URL: {$currentEndpoint}\n";

// 测试不同的API端点
echo "\n🧪 测试不同的API端点:\n";

$testEndpoints = [
    'UI支付页面' => $config['api_urls']['consume_purchase_ui'],
    'API直接支付' => '/cardbusiness/aggregatepay/b2c/online/consumepurchase/V1',
    '无UI直接支付' => '/cardbusiness/aggregatepay/b2c/online/api/consumepurchase/V1',
];

foreach ($testEndpoints as $name => $path) {
    echo "\n🔗 测试: {$name}\n";
    $fullUrl = $config['gateway_url'] . $path;
    echo "URL: {$fullUrl}\n";
    
    // 简单的连通性测试
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $fullUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取headers
    curl_setopt($ch, CURLOPT_USERAGENT, 'ICBC-Config-Check/1.0');
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP状态: {$httpCode}\n";
    if ($error) {
        echo "错误: {$error}\n";
    } else {
        if ($httpCode == 200) {
            echo "✅ 端点可访问\n";
        } elseif ($httpCode == 400 || $httpCode == 405) {
            echo "⚠️ 端点存在但方法/参数有问题\n";
        } elseif ($httpCode == 404) {
            echo "❌ 端点不存在\n";
        } else {
            echo "❔ 未知状态\n";
        }
    }
}

echo "\n\n2️⃣ 检查证书和密钥匹配性\n";
echo "=======================\n";

// 检查私钥和公钥是否匹配
$privateKeyPath = $config['private_key_path'];
$publicKeyPath = $config['public_key_path'];

echo "私钥路径: {$privateKeyPath}\n";
echo "公钥路径: {$publicKeyPath}\n";

if (!file_exists($privateKeyPath)) {
    echo "❌ 私钥文件不存在\n";
} else {
    echo "✅ 私钥文件存在\n";
    
    // 检查私钥格式
    $privateKeyContent = file_get_contents($privateKeyPath);
    $privateKey = openssl_pkey_get_private($privateKeyContent);
    
    if ($privateKey) {
        echo "✅ 私钥格式正确\n";
        
        // 获取私钥详情
        $keyDetails = openssl_pkey_get_details($privateKey);
        echo "私钥类型: " . $keyDetails['type'] . "\n";
        echo "私钥位数: " . $keyDetails['bits'] . "\n";
        
        // 测试签名功能
        $testData = 'test_sign_data';
        $signature = '';
        if (openssl_sign($testData, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            echo "✅ 私钥可以正常签名\n";
            
            // 尝试用公钥验证（如果公钥存在）
            if (file_exists($publicKeyPath)) {
                $publicKeyContent = file_get_contents($publicKeyPath);
                $publicKey = openssl_pkey_get_public($publicKeyContent);
                
                if ($publicKey) {
                    if (openssl_verify($testData, $signature, $publicKey, OPENSSL_ALGO_SHA256) === 1) {
                        echo "✅ 私钥和公钥匹配\n";
                    } else {
                        echo "❌ 私钥和公钥不匹配\n";
                    }
                    openssl_pkey_free($publicKey);
                } else {
                    echo "⚠️ 公钥格式错误\n";
                }
            } else {
                echo "⚠️ 公钥文件不存在，无法验证匹配性\n";
            }
        } else {
            echo "❌ 私钥签名失败\n";
        }
        
        openssl_pkey_free($privateKey);
    } else {
        echo "❌ 私钥格式错误: " . openssl_error_string() . "\n";
    }
}

echo "\n\n3️⃣ 检查商户信息有效性\n";
echo "====================\n";

$requiredFields = [
    'app_id' => 'APP_ID',
    'mer_id' => '商户号',
    'mer_prtcl_no' => '商户协议号',
    'gateway_url' => '网关地址',
    'notify_url' => '回调地址'
];

foreach ($requiredFields as $field => $name) {
    $value = $config[$field];
    if (empty($value)) {
        echo "❌ {$name}: 未配置\n";
    } else {
        echo "✅ {$name}: {$value}\n";
        
        // 额外验证
        if ($field === 'app_id' && !preg_match('/^\d+$/', $value)) {
            echo "   ⚠️ APP_ID格式可能不正确（应该是纯数字）\n";
        }
        
        if ($field === 'mer_id' && !preg_match('/^\d+$/', $value)) {
            echo "   ⚠️ 商户号格式可能不正确（应该是纯数字）\n";
        }
        
        if ($field === 'notify_url' && !filter_var($value, FILTER_VALIDATE_URL)) {
            echo "   ⚠️ 回调URL格式不正确\n";
        }
    }
}

echo "\n\n4️⃣ 尝试不同的请求方法\n";
echo "===================\n";

// 创建最简单的测试参数
$simpleBizContent = [
    'mer_id' => $config['mer_id'],
    'out_trade_no' => 'SIMPLE_TEST_' . time(),
    'total_fee' => '1',
    'body' => 'Test',
];

$simpleParams = [
    'app_id' => $config['app_id'],
    'timestamp' => date('Y-m-d H:i:s'),
    'biz_content' => json_encode($simpleBizContent),
];

// 尝试不同的签名参数组合
$signVariations = [
    '完整参数' => [
        'app_id' => $config['app_id'],
        'charset' => 'UTF-8',
        'format' => 'json',
        'sign_type' => 'RSA2',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => 'V1',
        'biz_content' => json_encode($simpleBizContent),
    ],
    '简化参数' => [
        'app_id' => $config['app_id'],
        'timestamp' => date('Y-m-d H:i:s'),
        'biz_content' => json_encode($simpleBizContent),
    ],
    '传统参数' => [
        'appid' => $config['app_id'], // 注意：小写的appid
        'timestamp' => date('Y-m-d H:i:s'),
        'biz_content' => json_encode($simpleBizContent),
    ],
];

foreach ($signVariations as $name => $params) {
    echo "\n🧪 测试签名变体: {$name}\n";
    
    // 生成签名
    ksort($params);
    $signString = '';
    foreach ($params as $key => $value) {
        if ($value !== '' && $value !== null) {
            $signString .= $key . '=' . $value . '&';
        }
    }
    $signString = rtrim($signString, '&');
    
    echo "待签名字符串: " . substr($signString, 0, 100) . "...\n";
    
    // 使用私钥签名
    $privateKeyContent = file_get_contents($config['private_key_path']);
    $privateKey = openssl_pkey_get_private($privateKeyContent);
    
    if ($privateKey) {
        $signature = '';
        if (openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            $sign = base64_encode($signature);
            echo "签名长度: " . strlen($sign) . "\n";
            echo "签名前10字符: " . substr($sign, 0, 10) . "...\n";
        } else {
            echo "❌ 签名生成失败\n";
        }
        openssl_pkey_free($privateKey);
    }
}

echo "\n\n5️⃣ 建议的解决方案\n";
echo "================\n";

echo "基于以上检查，建议按以下步骤排查：\n\n";
echo "1. 确认工商银行环境\n";
echo "   - 确认使用的是生产环境还是测试环境\n";
echo "   - 确认API网关地址是否正确\n\n";

echo "2. 确认商户资质\n";
echo "   - APP_ID、商户号、协议号是否匹配\n";
echo "   - 证书是否是工商银行为该商户签发\n\n";

echo "3. 确认API权限\n";
echo "   - 该商户是否开通了聚合支付权限\n";
echo "   - 是否签署了相关协议\n\n";

echo "4. 联系技术支持\n";
echo "   - 提供完整的请求参数给工商银行技术支持\n";
echo "   - 确认具体的参数要求和格式\n\n";

echo "============================\n";
echo "基础配置检查完成！\n";
echo "============================\n"; 