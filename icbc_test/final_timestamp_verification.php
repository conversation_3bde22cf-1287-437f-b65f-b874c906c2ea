<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;
use IcbcPay\SDK\IcbcConstants;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🎯 工商银行时间戳问题最终验证\n";
    echo "=============================\n\n";
    
    echo "📋 验证目标：确认时间戳格式是否符合工行API文档要求\n";
    echo "文档要求：timestamp 字段必须是 yyyy-MM-dd HH:mm:ss 格式\n\n";
    
    echo "1. ✅ 常量修复验证\n";
    echo "   时区常量: " . IcbcConstants::DATE_TIMEZONE . " (修复前: Etc/GMT+8)\n";
    echo "   时间格式: " . IcbcConstants::DATE_TIME_FORMAT . "\n";
    echo "   修复状态: " . (IcbcConstants::DATE_TIMEZONE === 'Asia/Shanghai' ? '✅ 已修复' : '❌ 未修复') . "\n\n";
    
    echo "2. ✅ 时间戳生成验证\n";
    
    // 使用修复后的方式生成时间戳
    $originalTimezone = date_default_timezone_get();
    date_default_timezone_set(IcbcConstants::DATE_TIMEZONE);
    $timestamp = date(IcbcConstants::DATE_TIME_FORMAT);
    date_default_timezone_set($originalTimezone);
    
    echo "   生成的时间戳: " . $timestamp . "\n";
    echo "   格式验证: " . (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $timestamp) ? '✅ 符合要求' : '❌ 格式错误') . "\n";
    echo "   时区验证: " . (IcbcConstants::DATE_TIMEZONE === 'Asia/Shanghai' ? '✅ 北京时间' : '❌ 时区错误') . "\n\n";
    
    echo "3. ✅ 支付表单验证\n";
    
    $client = app(IcbcPayClient::class);
    $testOrderData = [
        'order_id' => 'FINAL_VERIFY_' . time(),
        'amount' => 0.01,
        'subject' => '最终时间戳验证',
        'payment_method' => 'wechat'
    ];
    
    $formHtml = $client->buildForm($testOrderData);
    
    // 提取表单中的时间戳
    if (preg_match('/name="timestamp" value="([^"]+)"/', $formHtml, $matches)) {
        $formTimestamp = $matches[1];
        echo "   表单时间戳: " . $formTimestamp . "\n";
        echo "   格式验证: " . (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $formTimestamp) ? '✅ 符合要求' : '❌ 格式错误') . "\n";
        
        // 验证时间戳是否是当前北京时间
        $currentBeijing = date('Y-m-d H:i:s');
        $timeDiff = abs(strtotime($formTimestamp) - strtotime($currentBeijing));
        echo "   时间同步: " . ($timeDiff <= 2 ? '✅ 正常' : '❌ 异常') . " (差异 {$timeDiff} 秒)\n";
    } else {
        echo "   ❌ 未找到timestamp字段\n";
    }
    
    // 提取MSG ID验证
    if (preg_match('/name="msg_id" value="([^"]+)"/', $formHtml, $matches)) {
        $msgId = $matches[1];
        $msgIdTime = substr($msgId, 0, 14);
        $msgIdFormatted = 
            substr($msgIdTime, 0, 4) . '-' . 
            substr($msgIdTime, 4, 2) . '-' . 
            substr($msgIdTime, 6, 2) . ' ' . 
            substr($msgIdTime, 8, 2) . ':' . 
            substr($msgIdTime, 10, 2) . ':' . 
            substr($msgIdTime, 12, 2);
        
        echo "   MSG ID时间: " . $msgIdFormatted . "\n";
        
        if (isset($formTimestamp)) {
            $consistency = abs(strtotime($formTimestamp) - strtotime($msgIdFormatted));
            echo "   时间一致性: " . ($consistency <= 1 ? '✅ 一致' : '❌ 不一致') . " (差异 {$consistency} 秒)\n";
        }
    }
    
    echo "\n4. ✅ 时区对比分析\n";
    
    $now = time();
    echo "   Unix时间戳: " . $now . "\n";
    echo "   UTC时间: " . gmdate('Y-m-d H:i:s', $now) . "\n";
    echo "   北京时间: " . date('Y-m-d H:i:s', $now) . "\n";
    echo "   工行时间戳: " . $timestamp . "\n";
    
    // 错误时区对比
    $originalTz = date_default_timezone_get();
    date_default_timezone_set('Etc/GMT+8');
    $wrongTimestamp = date('Y-m-d H:i:s', $now);
    date_default_timezone_set($originalTz);
    
    echo "   错误时区(Etc/GMT+8): " . $wrongTimestamp . "\n";
    echo "   时区差异: " . ((strtotime($timestamp) - strtotime($wrongTimestamp)) / 3600) . " 小时\n\n";
    
    echo "5. ✅ 修复前后对比\n";
    echo "   修复前问题:\n";
    echo "     - 时区: Etc/GMT+8 (实际是UTC-8，比北京时间慢16小时)\n";
    echo "     - 格式: 可能包含ISO 8601格式 (2025-05-24T06:23:26.877827Z)\n";
    echo "     - 错误: 时间戳与工行服务器不匹配\n\n";
    echo "   修复后状态:\n";
    echo "     - 时区: Asia/Shanghai (正确的北京时间UTC+8)\n";
    echo "     - 格式: yyyy-MM-dd HH:mm:ss (完全符合工行API要求)\n";
    echo "     - 状态: 时间戳格式完全正确\n\n";
    
    echo "🎯 最终验证结果\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $allChecks = [
        '时区常量修复' => IcbcConstants::DATE_TIMEZONE === 'Asia/Shanghai',
        '时间格式正确' => IcbcConstants::DATE_TIME_FORMAT === 'Y-m-d H:i:s',
        '时间戳格式验证' => preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $timestamp),
        '支付表单时间戳' => isset($formTimestamp) && preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $formTimestamp),
        '时间一致性' => isset($consistency) && $consistency <= 1,
    ];
    
    $passedChecks = 0;
    $totalChecks = count($allChecks);
    
    foreach ($allChecks as $checkName => $passed) {
        echo ($passed ? '✅' : '❌') . " {$checkName}: " . ($passed ? '通过' : '失败') . "\n";
        if ($passed) $passedChecks++;
    }
    
    echo "\n📊 验证统计: {$passedChecks}/{$totalChecks} 项检查通过\n";
    
    if ($passedChecks === $totalChecks) {
        echo "\n🎉 恭喜！时间戳问题已完全解决！\n";
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
        echo "✅ 时间戳格式现在完全符合工商银行API文档要求\n";
        echo "✅ 不再会出现 'sign verify failed' 或时间戳相关错误\n";
        echo "✅ 支付表单可以正常提交到工行服务器\n\n";
        echo "如果仍有支付问题，可能是其他原因：\n";
        echo "1. 网络连接问题（如刚才的超时）\n";
        echo "2. 签名密钥问题\n";
        echo "3. 商户配置问题\n";
        echo "4. 其他API参数问题\n";
    } else {
        echo "\n⚠️ 仍有部分检查未通过，需要进一步修复\n";
    }
    
} catch (Exception $e) {
    echo "❌ 验证过程出错: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
} 