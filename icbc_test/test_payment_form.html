<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>工商银行支付测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-container { max-width: 600px; margin: 0 auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class='form-container'>
        <h2>🏦 工商银行支付测试</h2>
        <div class='info'>
            <h3>测试信息</h3>
            <p><strong>订单号:</strong> FIX_SIGN_1748028668</p>
            <p><strong>金额:</strong> ¥0.01</p>
            <p><strong>商品:</strong> 签名修复测试</p>
            <p><strong>支付方式:</strong> 微信支付</p>
        </div>
        
        <form id="icbcPayForm_6830ccfcb83a5" method="POST" action="https://gw.open.icbc.com.cn/sandbox/api/cardbusiness/aggregatepay/consumepurchase"><input type="hidden" name="app_id" value="11000000000000052474"><input type="hidden" name="msg_id" value="202505240331086383296510"><input type="hidden" name="format" value="json"><input type="hidden" name="charset" value="UTF-8"><input type="hidden" name="sign_type" value="RSA2"><input type="hidden" name="timestamp" value="2025-05-24 03:31:08"><input type="hidden" name="version" value="1.0.0"><input type="hidden" name="biz_content" value="{&quot;mer_id&quot;:&quot;301055420003&quot;,&quot;mer_prtcl_no&quot;:&quot;3010554200030201&quot;,&quot;out_trade_no&quot;:&quot;FIX_SIGN_1748028668&quot;,&quot;order_amt&quot;:&quot;0.01&quot;,&quot;pay_mode&quot;:&quot;9&quot;,&quot;access_type&quot;:&quot;1&quot;,&quot;mer_url&quot;:&quot;https:\/\/icbc.dev.hiwsoft.com\/icbc-pay\/notify&quot;,&quot;goods_body&quot;:&quot;签名修复测试&quot;,&quot;goods_detail&quot;:&quot;签名修复测试&quot;,&quot;expire_time&quot;:&quot;2025-05-24 04:01:08&quot;,&quot;page_url&quot;:&quot;https:\/\/icbc.dev.hiwsoft.com\/icbc-pay\/return&quot;,&quot;currency&quot;:&quot;CNY&quot;}"><input type="hidden" name="sign" value="Gn+zEch4dftr1czrMPXU9UoZIWdyQ0J/XGxcs6bYP0fnykdZTMPkErpl+cabFYLzB2GnpDSzTAqLh9fU3oc4LAJ7Qoxo5BXDbx7PlzvLyfQMPRiyPDWt+WQH/3jhqtWqQbf0oi2HCWMHfBGUg1baE8DzgUJa3a77ICVwUoEesMoBGPYcxJPnxaa6LWszvI+YuC2R5PROJqToqq3CbbXOHkLv5pyS/zjktjuiqWLh9SQ4fAY+Grrz6rUPeeFyAggMkfV+A1Lz++QJBRYJbedg78xZ0OdAsrcw/LJ8xPRdMG1rgbCQus+GMnIJqDchBK6UJKDEDgAsBs+BEZ9sYjpIyQ=="></form>
        
        <script>
            // 自动提交表单进行测试
            function submitPayment() {
                if (confirm('确定要提交支付测试吗？')) {
                    document.forms[0].submit();
                }
            }
            
            // 添加提交按钮
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.querySelector('form');
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn';
                button.textContent = '提交支付测试';
                button.onclick = submitPayment;
                form.appendChild(button);
            });
        </script>
    </div>
</body>
</html>