<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "⏰ 测试工商银行支付时间戳修复\n";
    echo "===============================\n\n";
    
    echo "创建支付客户端...\n";
    $client = app(IcbcPayClient::class);
    echo "✅ 客户端创建成功\n";
    
    echo "\n⏰ 时间戳分析：\n";
    echo "服务器时间: " . date('Y-m-d H:i:s') . "\n";
    echo "Unix时间戳: " . time() . "\n";
    echo "时区: " . date_default_timezone_get() . "\n";
    
    echo "\n生成支付表单...\n";
    $testOrderData = [
        'order_id' => 'TIMESTAMP_TEST_' . time(),
        'amount' => 0.01,
        'subject' => '时间戳测试订单',
        'payment_method' => 'wechat'
    ];
    
    $formHtml = $client->buildForm($testOrderData);
    echo "✅ 支付表单生成成功\n";
    
    echo "\n📋 时间戳验证：\n";
    
    // 提取timestamp
    if (preg_match('/name="timestamp" value="([^"]+)"/', $formHtml, $matches)) {
        $timestamp = $matches[1];
        echo "✅ 请求时间戳: " . $timestamp . "\n";
        
        // 解析时间戳
        $timestampUnix = strtotime($timestamp);
        if ($timestampUnix) {
            $currentUnix = time();
            $timeDiff = abs($currentUnix - $timestampUnix);
            echo "✅ Unix时间戳: " . $timestampUnix . "\n";
            echo "✅ 时间差: " . $timeDiff . " 秒\n";
            echo "✅ 时间同步: " . ($timeDiff <= 5 ? '正常' : '异常') . "\n";
        } else {
            echo "❌ 时间戳格式解析失败\n";
        }
    } else {
        echo "❌ 未找到timestamp字段\n";
    }
    
    // 提取msg_id检查时间一致性
    if (preg_match('/name="msg_id" value="([^"]+)"/', $formHtml, $matches)) {
        $msgId = $matches[1];
        $msgIdTime = substr($msgId, 0, 14); // 前14位是时间
        $msgIdFormatted = 
            substr($msgIdTime, 0, 4) . '-' . 
            substr($msgIdTime, 4, 2) . '-' . 
            substr($msgIdTime, 6, 2) . ' ' . 
            substr($msgIdTime, 8, 2) . ':' . 
            substr($msgIdTime, 10, 2) . ':' . 
            substr($msgIdTime, 12, 2);
        
        echo "✅ MSG ID时间: " . $msgIdFormatted . "\n";
        
        if (isset($timestamp)) {
            $timeDiff = abs(strtotime($timestamp) - strtotime($msgIdFormatted));
            echo "✅ 时间一致性: " . ($timeDiff <= 2 ? '一致' : '不一致') . " (差异 {$timeDiff} 秒)\n";
        }
    }
    
    // 提取biz_content中的expire_time
    if (preg_match('/name="biz_content" value="([^"]+)"/', $formHtml, $matches)) {
        $bizContent = html_entity_decode($matches[1]);
        $bizData = json_decode($bizContent, true);
        if ($bizData && isset($bizData['expire_time'])) {
            $expireTime = $bizData['expire_time'];
            echo "✅ 过期时间: " . $expireTime . "\n";
            
            if (isset($timestamp)) {
                $expireDiff = strtotime($expireTime) - strtotime($timestamp);
                echo "✅ 有效期: " . round($expireDiff / 60) . " 分钟\n";
            }
        }
    }
    
    echo "\n🔄 连续测试（验证时间戳稳定性）：\n";
    for ($i = 1; $i <= 3; $i++) {
        echo "第 {$i} 次测试: ";
        $testForm = $client->buildForm([
            'order_id' => 'STABILITY_TEST_' . time() . '_' . $i,
            'amount' => 0.01,
            'subject' => "稳定性测试 #{$i}",
            'payment_method' => 'wechat'
        ]);
        
        if (preg_match('/name="timestamp" value="([^"]+)"/', $testForm, $matches)) {
            $testTimestamp = $matches[1];
            echo $testTimestamp . " ✅\n";
        } else {
            echo "❌ 时间戳提取失败\n";
        }
        
        sleep(1); // 等待1秒
    }
    
    echo "\n🎯 建议配置（如果还有时间同步问题）：\n";
    echo "在config/icbc.php中添加：\n";
    echo "'time_offset' => 0, // 时间偏移秒数，可设置为 -60 到 60\n";
    echo "\n如果工行服务器快，设置负值；如果慢，设置正值。\n";
    
    echo "\n✅ 时间戳修复测试完成！\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
} 