<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

echo "工商银行支付URL分析工具\n";
echo "==========================\n\n";

try {
    // 创建测试支付记录
    $paymentRecord = PaymentRecord::create([
        'out_trade_no' => 'ANALYZE_' . time() . rand(1000, 9999),
        'total_amount' => 0.01,
        'subject' => 'URL分析测试',
        'payment_method' => 'wechat',
        'car_number' => '分析A123',
        'parking_duration' => 15,
        'status' => 'pending'
    ]);

    echo "创建测试订单: {$paymentRecord->out_trade_no}\n\n";

    // 创建支付服务
    $icbcPayService = new IcbcPayService();
    $result = $icbcPayService->createPayment($paymentRecord);

    if ($result['success']) {
        $paymentUrl = $result['payment_url'];
        
        echo "生成的支付URL:\n";
        echo str_repeat('-', 80) . "\n";
        echo $paymentUrl . "\n";
        echo str_repeat('-', 80) . "\n\n";
        
        // 解析URL
        $parsedUrl = parse_url($paymentUrl);
        parse_str($parsedUrl['query'], $params);
        
        echo "URL组成部分:\n";
        echo "  协议: " . $parsedUrl['scheme'] . "\n";
        echo "  主机: " . $parsedUrl['host'] . "\n";
        echo "  路径: " . $parsedUrl['path'] . "\n\n";
        
        echo "请求参数:\n";
        foreach ($params as $key => $value) {
            echo "  {$key}: ";
            if ($key === 'biz_content') {
                echo "\n";
                $bizContent = json_decode(urldecode($value), true);
                if ($bizContent) {
                    foreach ($bizContent as $bizKey => $bizValue) {
                        echo "    {$bizKey}: {$bizValue}\n";
                    }
                } else {
                    echo "    ❌ JSON解析失败\n";
                    echo "    原始值: " . urldecode($value) . "\n";
                }
            } elseif ($key === 'sign') {
                echo strlen($value) . " 字符 (签名)\n";
            } else {
                echo urldecode($value) . "\n";
            }
        }
        
        echo "\n参数校验:\n";
        
        // 检查必需参数
        $requiredParams = ['app_id', 'charset', 'format', 'sign_type', 'timestamp', 'version', 'biz_content', 'sign'];
        foreach ($requiredParams as $param) {
            if (isset($params[$param]) && !empty($params[$param])) {
                echo "  ✅ {$param}: 存在\n";
            } else {
                echo "  ❌ {$param}: 缺失\n";
            }
        }
        
        // 检查biz_content中的必需参数
        if (isset($params['biz_content'])) {
            $bizContent = json_decode(urldecode($params['biz_content']), true);
            if ($bizContent) {
                echo "\nbiz_content参数校验:\n";
                $requiredBizParams = ['mer_id', 'mer_prtcl_no', 'out_trade_no', 'orig_date_time', 'total_fee', 'body', 'pay_mode', 'access_type'];
                foreach ($requiredBizParams as $param) {
                    if (isset($bizContent[$param]) && !empty($bizContent[$param])) {
                        echo "  ✅ {$param}: " . $bizContent[$param] . "\n";
                    } else {
                        echo "  ❌ {$param}: 缺失\n";
                    }
                }
                
                // 检查金额格式
                if (isset($bizContent['total_fee'])) {
                    $totalFee = $bizContent['total_fee'];
                    if (is_numeric($totalFee) && $totalFee > 0) {
                        echo "  ✅ total_fee格式: 正确 ({$totalFee}分)\n";
                    } else {
                        echo "  ❌ total_fee格式: 错误\n";
                    }
                }
                
                // 检查时间格式
                if (isset($bizContent['orig_date_time'])) {
                    $dateTime = $bizContent['orig_date_time'];
                    if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $dateTime)) {
                        echo "  ✅ orig_date_time格式: 正确 ({$dateTime})\n";
                    } else {
                        echo "  ❌ orig_date_time格式: 错误\n";
                    }
                }
                
                // 检查支付方式
                if (isset($bizContent['pay_mode'])) {
                    $payMode = $bizContent['pay_mode'];
                    if (in_array($payMode, ['9', '10'])) {
                        echo "  ✅ pay_mode: 正确 (" . ($payMode == '9' ? '微信' : '支付宝') . ")\n";
                    } else {
                        echo "  ❌ pay_mode: 错误 ({$payMode})\n";
                    }
                }
                
                // 检查接入方式
                if (isset($bizContent['access_type'])) {
                    $accessType = $bizContent['access_type'];
                    if (in_array($accessType, ['1', '5', '7', '8', '9'])) {
                        echo "  ✅ access_type: 正确 ({$accessType})\n";
                    } else {
                        echo "  ❌ access_type: 可能有问题 ({$accessType})\n";
                    }
                }
            }
        }
        
        echo "\nURL测试:\n";
        
        // 测试URL访问
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $paymentUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "  HTTP状态码: {$httpCode}\n";
        
        if ($error) {
            echo "  ❌ CURL错误: {$error}\n";
        } else {
            // 分析响应内容
            if (strpos($response, '参数校验失败') !== false) {
                echo "  ❌ 工商银行返回: 参数校验失败\n";
                
                // 尝试找出具体错误信息
                if (preg_match('/错误代码[:：]\s*(\w+)/', $response, $matches)) {
                    echo "  错误代码: " . $matches[1] . "\n";
                }
                if (preg_match('/错误信息[:：]\s*([^<\n]+)/', $response, $matches)) {
                    echo "  错误信息: " . trim($matches[1]) . "\n";
                }
            } elseif (strpos($response, '支付') !== false || strpos($response, 'pay') !== false) {
                echo "  ✅ 响应正常，似乎到达了支付页面\n";
            } else {
                echo "  ⚠️  响应内容未知\n";
            }
            
            // 显示响应的前200字符
            echo "  响应预览: " . mb_substr(strip_tags($response), 0, 200) . "...\n";
        }
        
    } else {
        echo "❌ 支付URL生成失败\n";
    }

} catch (Exception $e) {
    echo "❌ 发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n分析完成\n"; 