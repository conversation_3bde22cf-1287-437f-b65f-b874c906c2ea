<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🔧 工商银行支付时间戳超时问题修复\n";
    echo "=====================================\n\n";
    
    echo "📋 问题分析：\n";
    echo "错误代码: 400011\n";
    echo "错误信息: request timeout.check your request's timestamp\n";
    echo "原因: 时间戳格式或时区问题\n\n";
    
    echo "🛠️ 修复方案：\n";
    echo "1. 使用北京时间而不是UTC时间\n";
    echo "2. 统一时间戳格式为 Y-m-d H:i:s\n";
    echo "3. 添加时间偏移配置支持\n";
    echo "4. 确保MSG ID和timestamp时间一致\n\n";
    
    echo "⏰ 当前时间信息：\n";
    echo "系统时区: " . date_default_timezone_get() . "\n";
    echo "系统时间: " . date('Y-m-d H:i:s') . "\n";
    echo "Unix时间戳: " . time() . "\n";
    echo "北京时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    echo "🔧 创建修复后的支付客户端...\n";
    $client = app(IcbcPayClient::class);
    echo "✅ 客户端创建成功\n\n";
    
    echo "🧪 测试修复后的时间戳生成：\n";
    
    // 测试数据
    $testOrderData = [
        'order_id' => 'TIMESTAMP_FIX_' . time(),
        'amount' => 0.01,
        'subject' => '时间戳修复测试',
        'payment_method' => 'wechat'
    ];
    
    echo "生成支付表单...\n";
    $formHtml = $client->buildForm($testOrderData);
    echo "✅ 表单生成成功\n\n";
    
    echo "📊 时间戳验证结果：\n";
    
    // 提取并验证timestamp
    if (preg_match('/name="timestamp" value="([^"]+)"/', $formHtml, $matches)) {
        $timestamp = $matches[1];
        echo "✅ 时间戳格式: " . $timestamp . "\n";
        
        // 验证时间戳格式
        if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $timestamp)) {
            echo "✅ 格式验证: 正确 (Y-m-d H:i:s)\n";
        } else {
            echo "❌ 格式验证: 错误\n";
        }
        
        // 验证时间差
        $timestampUnix = strtotime($timestamp);
        $currentUnix = time();
        $timeDiff = abs($currentUnix - $timestampUnix);
        echo "✅ 时间差: " . $timeDiff . " 秒\n";
        echo "✅ 时间同步: " . ($timeDiff <= 5 ? '正常' : '需要调整') . "\n";
        
        // 验证时区
        $beijingTime = date('Y-m-d H:i:s');
        if ($timestamp === $beijingTime || abs(strtotime($timestamp) - strtotime($beijingTime)) <= 1) {
            echo "✅ 时区验证: 北京时间 ✓\n";
        } else {
            echo "⚠️ 时区验证: 可能不是北京时间\n";
        }
    } else {
        echo "❌ 未找到timestamp字段\n";
    }
    
    // 提取并验证msg_id
    if (preg_match('/name="msg_id" value="([^"]+)"/', $formHtml, $matches)) {
        $msgId = $matches[1];
        echo "✅ MSG ID: " . $msgId . "\n";
        
        // 解析MSG ID中的时间部分
        $msgIdTime = substr($msgId, 0, 14);
        if (strlen($msgIdTime) === 14) {
            $msgIdFormatted = 
                substr($msgIdTime, 0, 4) . '-' . 
                substr($msgIdTime, 4, 2) . '-' . 
                substr($msgIdTime, 6, 2) . ' ' . 
                substr($msgIdTime, 8, 2) . ':' . 
                substr($msgIdTime, 10, 2) . ':' . 
                substr($msgIdTime, 12, 2);
            
            echo "✅ MSG ID时间: " . $msgIdFormatted . "\n";
            
            // 验证MSG ID和timestamp的时间一致性
            if (isset($timestamp)) {
                $timeDiff = abs(strtotime($timestamp) - strtotime($msgIdFormatted));
                echo "✅ 时间一致性: " . ($timeDiff <= 2 ? '一致' : '不一致') . " (差异 {$timeDiff} 秒)\n";
            }
        }
    }
    
    echo "\n🔄 连续测试（验证稳定性）：\n";
    for ($i = 1; $i <= 3; $i++) {
        echo "第 {$i} 次: ";
        $testForm = $client->buildForm([
            'order_id' => 'STABILITY_' . time() . '_' . $i,
            'amount' => 0.01,
            'subject' => "稳定性测试 #{$i}",
            'payment_method' => 'wechat'
        ]);
        
        if (preg_match('/name="timestamp" value="([^"]+)"/', $testForm, $matches)) {
            $testTimestamp = $matches[1];
            echo $testTimestamp . " ✅\n";
        } else {
            echo "❌ 时间戳提取失败\n";
        }
        
        sleep(1);
    }
    
    echo "\n📝 修复总结：\n";
    echo "✅ 时间戳格式: 统一使用 Y-m-d H:i:s 格式\n";
    echo "✅ 时区设置: 强制使用北京时间 (Asia/Shanghai)\n";
    echo "✅ MSG ID格式: 使用北京时间的 YmdHis 格式\n";
    echo "✅ 时间一致性: MSG ID和timestamp使用相同时间源\n";
    echo "✅ 偏移支持: 支持通过配置调整时间偏移\n\n";
    
    echo "🎯 如果仍有时间戳问题，请尝试：\n";
    echo "1. 在 .env 文件中设置时间偏移：\n";
    echo "   ICBC_TIME_OFFSET=-30  # 如果本地时间快30秒\n";
    echo "   ICBC_TIME_OFFSET=30   # 如果本地时间慢30秒\n\n";
    
    echo "2. 检查服务器时区设置：\n";
    echo "   sudo timedatectl set-timezone Asia/Shanghai\n\n";
    
    echo "3. 在Laravel配置中确认时区：\n";
    echo "   config/app.php: 'timezone' => 'Asia/Shanghai'\n\n";
    
    echo "✅ 时间戳超时问题修复完成！\n";
    echo "现在可以重新测试支付功能。\n";
    
} catch (Exception $e) {
    echo "❌ 修复过程中出现错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "\n请检查：\n";
    echo "1. Laravel应用是否正确启动\n";
    echo "2. IcbcPayClient类是否正确注册\n";
    echo "3. 配置文件是否存在\n";
}
