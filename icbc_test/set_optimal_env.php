<?php

echo "🔧 设置工商银行支付最佳环境变量\n";
echo "===============================\n\n";

$envFile = '.env';
$envExampleFile = '.env.example';

// 检查.env文件是否存在
if (!file_exists($envFile)) {
    if (file_exists($envExampleFile)) {
        echo "📋 .env文件不存在，从.env.example复制...\n";
        copy($envExampleFile, $envFile);
        echo "✅ .env文件已创建\n";
    } else {
        echo "❌ .env和.env.example文件都不存在\n";
        exit(1);
    }
}

// 简单的env读取函数
function getEnvValue($key, $default = null) {
    global $envFile;
    if (!file_exists($envFile)) {
        return $default;
    }
    
    $content = file_get_contents($envFile);
    $pattern = '/^' . preg_quote($key) . '=(.*)$/m';
    
    if (preg_match($pattern, $content, $matches)) {
        return trim($matches[1], '"\'');
    }
    
    return $default;
}

// 读取现有的.env内容
$envContent = file_get_contents($envFile);

// 定义ICBC相关的环境变量设置
$icbcEnvVars = [
    'ICBC_TIME_OFFSET' => '0',
    'ICBC_USE_UTC' => 'true',
    'ICBC_AUTO_SYNC' => 'true',
    'ICBC_TIME_TOLERANCE' => '300',
    'ICBC_ENVIRONMENT' => 'sandbox',
    'ICBC_MOCK_ENABLED' => 'true',
    'ICBC_DEBUG_ENABLED' => 'true',
    'ICBC_TEST_MODE' => 'true',
];

echo "📝 检查和更新环境变量：\n";

$updatedContent = $envContent;
$updated = false;

foreach ($icbcEnvVars as $key => $value) {
    $pattern = '/^' . preg_quote($key) . '=.*$/m';
    $newLine = $key . '=' . $value;
    
    if (preg_match($pattern, $envContent)) {
        // 变量已存在，更新它
        $updatedContent = preg_replace($pattern, $newLine, $updatedContent);
        echo "🔄 更新 {$key}={$value}\n";
        $updated = true;
    } else {
        // 变量不存在，添加它
        if (!str_contains($updatedContent, '# ICBC支付配置')) {
            $updatedContent .= "\n# ICBC支付配置\n";
        }
        $updatedContent .= $newLine . "\n";
        echo "➕ 添加 {$key}={$value}\n";
        $updated = true;
    }
}

// 如果有更新，写入文件
if ($updated) {
    file_put_contents($envFile, $updatedContent);
    echo "\n✅ 环境变量已更新到 .env 文件\n";
} else {
    echo "\n📋 所有环境变量已经是最新的\n";
}

echo "\n🎯 当前ICBC相关环境变量：\n";
foreach ($icbcEnvVars as $key => $value) {
    $currentValue = getEnvValue($key, '未设置');
    echo "{$key}={$currentValue}\n";
}

echo "\n📋 时区相关设置检查：\n";
$appTimezone = getEnvValue('APP_TIMEZONE', 'UTC');
echo "APP_TIMEZONE={$appTimezone}\n";
echo "系统时区: " . date_default_timezone_get() . "\n";
echo "当前本地时间: " . date('Y-m-d H:i:s') . "\n";
echo "当前UTC时间: " . gmdate('Y-m-d H:i:s') . "\n";

echo "\n🚀 建议的最佳实践：\n";
echo "1. 使用UTC时间格式与工行服务器同步\n";
echo "2. 设置时间偏移为0（除非确定需要调整）\n";
echo "3. 启用调试模式以便于问题排查\n";
echo "4. 在生产环境中记得修改相关配置\n";

echo "\n✅ 环境变量配置完成！\n";
echo "请重启Laravel应用以使配置生效：php artisan config:cache\n"; 