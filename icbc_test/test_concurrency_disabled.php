<?php

require_once '../vendor/autoload.php';

try {
    $app = require_once '../bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🚫 工商银行并发限制关闭验证\n";
    echo "===============================\n\n";
    
    echo "🔍 验证状态：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "检查并发限制是否已在ParkingController中被关闭...\n\n";
    
    // 1. 检查控制器文件是否包含注释掉的并发检查
    echo "📋 第一步：检查控制器代码修改\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $controllerFile = '../app/Http/Controllers/ParkingController.php';
    
    if (file_exists($controllerFile)) {
        $content = file_get_contents($controllerFile);
        
        // 检查并发检查是否被注释
        $rateCheckDisabled = strpos($content, '// if (!IcbcRateLimiter::canMakePayment())') !== false;
        $recordingDisabled = strpos($content, '// IcbcRateLimiter::recordPayment()') !== false;
        $logNotesPresent = strpos($content, 'Concurrency control is turned off') !== false;
        
        echo "并发检查注释状态：" . ($rateCheckDisabled ? "✅ 已注释" : "❌ 未注释") . "\n";
        echo "记录功能注释状态：" . ($recordingDisabled ? "✅ 已注释" : "❌ 未注释") . "\n";
        echo "日志标记状态：    " . ($logNotesPresent ? "✅ 已添加" : "❌ 未添加") . "\n\n";
        
        if ($rateCheckDisabled && $recordingDisabled && $logNotesPresent) {
            echo "✅ 控制器修改验证通过：并发限制已成功关闭\n\n";
        } else {
            echo "❌ 控制器修改验证失败：请检查代码修改\n\n";
        }
    } else {
        echo "❌ 控制器文件不存在\n\n";
    }
    
    // 2. 模拟快速连续请求测试
    echo "🚀 第二步：模拟快速连续请求测试\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $testCount = 3;
    echo "将进行 {$testCount} 次快速连续的模拟支付请求...\n\n";
    
    for ($i = 1; $i <= $testCount; $i++) {
        echo "请求 {$i}：\n";
        
        // 准备测试数据
        $testData = [
            'car_number' => 'TEST' . str_pad($i, 3, '0', STR_PAD_LEFT),
            'amount' => '0.01',
            'payment_method' => 'wechat',
            'parking_duration' => 30
        ];
        
        echo "  测试数据：车牌号 {$testData['car_number']}, 金额 {$testData['amount']} 元\n";
        
        $startTime = microtime(true);
        
        try {
            // 创建支付记录（模拟控制器行为，但不触发实际支付）
            $paymentRecord = \IcbcPay\Models\PaymentRecord::create([
                'out_trade_no' => \IcbcPay\Models\PaymentRecord::generateUniqueOrderNo(),
                'total_amount' => $testData['amount'],
                'subject' => '测试停车费支付',
                'payment_method' => $testData['payment_method'],
                'car_number' => $testData['car_number'],
                'parking_duration' => $testData['parking_duration'],
                'status' => 'pending',
            ]);
            
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            echo "  ✅ 订单创建成功：{$paymentRecord->out_trade_no}\n";
            echo "  ⏱️ 处理时间：{$duration}ms\n";
            echo "  📝 记录ID：{$paymentRecord->id}\n";
            echo "  🕐 创建时间：{$paymentRecord->created_at->format('H:i:s.u')}\n";
            
            // 清理测试数据
            $paymentRecord->delete();
            echo "  🗑️ 测试数据已清理\n";
            
        } catch (Exception $e) {
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            echo "  ❌ 请求失败：{$e->getMessage()}\n";
            echo "  ⏱️ 处理时间：{$duration}ms\n";
        }
        
        echo "\n";
        
        // 极短间隔，测试是否还有并发限制
        if ($i < $testCount) {
            usleep(100000); // 100ms间隔
        }
    }
    
    // 3. 检查IcbcRateLimiter服务状态
    echo "📊 第三步：检查频率限制器服务状态\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    try {
        // 检查服务是否还能正常工作（即使不使用）
        echo "频率限制器服务可用性测试：\n";
        
        $canMakePayment = \App\Services\IcbcRateLimiter::canMakePayment();
        echo "  canMakePayment() 返回：" . ($canMakePayment ? "true" : "false") . "\n";
        
        $nextTime = \App\Services\IcbcRateLimiter::getNextAvailableTime();
        echo "  getNextAvailableTime() 返回：" . ($nextTime ? "有数据" : "null") . "\n";
        
        echo "  ✅ 服务仍然可用（虽然已不被调用）\n\n";
        
    } catch (Exception $e) {
        echo "  ⚠️ 服务测试失败：{$e->getMessage()}\n";
        echo "  📝 这可能是正常的，因为服务已不被使用\n\n";
    }
    
    // 4. 验证请求日志
    echo "📝 第四步：验证请求日志\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // 检查Laravel日志文件
    $logPath = '../storage/logs/laravel.log';
    
    if (file_exists($logPath) && is_readable($logPath)) {
        echo "检查Laravel日志文件...\n";
        
        // 读取最后1000行日志
        $lines = [];
        $file = new SplFileObject($logPath);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        if ($totalLines > 1000) {
            $startLine = $totalLines - 1000;
        } else {
            $startLine = 0;
        }
        
        $file->seek($startLine);
        
        $concurrencyDisabledCount = 0;
        $rateRecordingDisabledCount = 0;
        
        while (!$file->eof()) {
            $line = $file->current();
            
            if (strpos($line, 'Rate limit check disabled') !== false) {
                $concurrencyDisabledCount++;
            }
            
            if (strpos($line, 'Payment recording disabled') !== false) {
                $rateRecordingDisabledCount++;
            }
            
            $file->next();
        }
        
        echo "  找到 'Rate limit check disabled' 日志：{$concurrencyDisabledCount} 条\n";
        echo "  找到 'Payment recording disabled' 日志：{$rateRecordingDisabledCount} 条\n";
        
        if ($concurrencyDisabledCount > 0 || $rateRecordingDisabledCount > 0) {
            echo "  ✅ 日志验证通过：发现并发限制关闭的日志记录\n\n";
        } else {
            echo "  ℹ️ 暂未发现相关日志（可能是因为最近没有支付请求）\n\n";
        }
    } else {
        echo "  ⚠️ 无法读取Laravel日志文件\n\n";
    }
    
    // 5. 总结报告
    echo "🎉 并发限制关闭验证总结\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    echo "✅ 修改内容：\n";
    echo "   - IcbcRateLimiter::canMakePayment() 检查已注释\n";
    echo "   - IcbcRateLimiter::recordPayment() 记录已注释\n";
    echo "   - 添加了明确的日志标记说明\n";
    echo "   - 保留了服务类文件（便于将来重新启用）\n\n";
    
    echo "✅ 预期效果：\n";
    echo "   - 支付请求不再受频率限制\n";
    echo "   - 可以快速连续发起支付\n";
    echo "   - 不会出现 429 状态码的频率限制错误\n";
    echo "   - 不会记录支付频率数据\n\n";
    
    echo "⚠️ 注意事项：\n";
    echo "   - 关闭并发限制可能导致触发工行API限制\n";
    echo "   - 建议在测试环境使用，生产环境谨慎关闭\n";
    echo "   - 如需重新启用，取消注释相关代码即可\n";
    echo "   - 工行500032错误可能会重新出现\n\n";
    
    echo "🔔 下一步建议：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "1. 在浏览器中测试连续多次支付请求\n";
    echo "2. 观察是否还会出现频率限制错误\n";
    echo "3. 监控工行API响应，注意500032错误\n";
    echo "4. 如果需要，可以随时重新启用并发限制\n";
    echo "5. 考虑在前端添加防重复提交机制\n";
    
} catch (Exception $e) {
    echo "❌ 测试执行出错：" . $e->getMessage() . "\n";
    echo "📄 错误文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}

echo "\n" . "🎊 并发限制关闭验证完成！" . "\n";
echo "=" . str_repeat("=", 35) . "\n"; 