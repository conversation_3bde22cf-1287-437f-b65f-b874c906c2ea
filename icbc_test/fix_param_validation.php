<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

echo "🔧 工商银行参数校验失败修复工具\n";
echo "====================================\n\n";

// 获取配置
$config = config('icbc-pay');

echo "📋 当前配置检查:\n";
echo "APP_ID: {$config['app_id']}\n";
echo "MER_ID: {$config['mer_id']}\n";
echo "MER_PRTCL_NO: {$config['mer_prtcl_no']}\n";
echo "GATEWAY_URL: {$config['gateway_url']}\n";
echo "NOTIFY_URL: {$config['notify_url']}\n\n";

// 获取服务器真实公网IP
echo "🌐 获取服务器公网IP:\n";
$publicIP = getPublicIP();
echo "服务器公网IP: {$publicIP}\n\n";

// 创建测试订单
$paymentRecord = PaymentRecord::create([
    'out_trade_no' => 'PARAM_FIX_' . date('YmdHis') . rand(100, 999),
    'total_amount' => 0.01,
    'subject' => '参数修复测试',
    'payment_method' => 'wechat',
    'car_number' => '测试A888',
    'parking_duration' => 60,
    'status' => 'pending'
]);

echo "📝 测试订单信息:\n";
echo "订单号: {$paymentRecord->out_trade_no}\n";
echo "金额: ¥{$paymentRecord->total_amount}\n";
echo "车牌: {$paymentRecord->car_number}\n\n";

// 手动构建标准参数
echo "🛠️ 构建修复后的参数:\n";

// 构建业务参数 - 严格按工商银行规范
$bizContent = [
    'mer_id' => $config['mer_id'],
    'mer_prtcl_no' => $config['mer_prtcl_no'],
    'out_trade_no' => $paymentRecord->out_trade_no,
    'orig_date_time' => date('Y-m-d H:i:s'), // 标准时间格式
    'total_fee' => '1', // 1分钱，字符串格式
    'body' => '停车费支付', // 简化商品名称
    'mer_url' => $config['notify_url'],
    'spbill_create_ip' => $publicIP, // 使用真实公网IP
    'fee_type' => '001', // 人民币
    'pay_mode' => '9', // 微信支付
    'access_type' => '1', // 扫码支付
    'device_info' => 'WEB', // 设备信息
    'notify_type' => 'HS', // 主动通知
    'result_type' => '0', // 成功失败都通知
];

echo "业务参数 (biz_content):\n";
foreach ($bizContent as $key => $value) {
    echo "  {$key}: {$value}\n";
}

// 构建请求参数
$requestParams = [
    'app_id' => $config['app_id'],
    'charset' => 'UTF-8',
    'format' => 'json', 
    'sign_type' => 'RSA2',
    'timestamp' => date('Y-m-d H:i:s'), // 标准时间格式
    'version' => 'V1',
    'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
];

echo "\n请求参数:\n";
foreach ($requestParams as $key => $value) {
    if ($key === 'biz_content') {
        echo "  {$key}: {$value}\n";
    } else {
        echo "  {$key}: {$value}\n";
    }
}

// 生成签名
echo "\n🔐 生成签名:\n";
ksort($requestParams);
$signString = '';
foreach ($requestParams as $key => $value) {
    if ($value !== '' && $value !== null) {
        $signString .= $key . '=' . $value . '&';
    }
}
$signString = rtrim($signString, '&');

echo "待签名字符串长度: " . strlen($signString) . "\n";
echo "待签名字符串MD5: " . md5($signString) . "\n";

// 加载私钥
$privateKeyContent = file_get_contents($config['private_key_path']);
$privateKey = openssl_pkey_get_private($privateKeyContent);

if (!$privateKey) {
    echo "❌ 私钥加载失败！\n";
    exit;
}

// 生成RSA2签名
$signature = '';
if (openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
    $finalSign = base64_encode($signature);
    echo "✅ 签名生成成功\n";
    echo "签名长度: " . strlen($finalSign) . "\n";
} else {
    echo "❌ 签名生成失败！\n";
    exit;
}

$requestParams['sign'] = $finalSign;

// 构建支付URL
$baseUrl = $config['gateway_url'] . $config['api_urls']['consume_purchase_ui'];
$queryString = http_build_query($requestParams);
$paymentUrl = $baseUrl . '?' . $queryString;

echo "\n🔗 支付URL信息:\n";
echo "基础URL: {$baseUrl}\n";
echo "完整URL长度: " . strlen($paymentUrl) . "\n";

// 测试URL
echo "\n🧪 测试支付URL:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $paymentUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "❌ CURL错误: {$error}\n";
} else {
    if (strpos($response, '参数校验失败') !== false) {
        echo "❌ 仍然参数校验失败\n";
        
        // 保存响应到文件进行详细分析
        file_put_contents('icbc_error_response.html', $response);
        echo "错误响应已保存到 icbc_error_response.html\n";
        
        // 尝试提取更多错误信息
        echo "\n🔍 错误分析:\n";
        if (preg_match('/错误.*?[:：]\s*([^<\n\r]+)/i', $response, $matches)) {
            echo "错误信息: " . trim($matches[1]) . "\n";
        }
        
        echo "\n💡 进一步排查建议:\n";
        echo "1. 确认APP_ID与商户私钥匹配\n";
        echo "2. 确认商户号和协议号正确\n";
        echo "3. 确认回调URL可以正常访问\n";
        echo "4. 联系工商银行技术支持确认参数要求\n";
        
    } elseif (strpos($response, '支付') !== false || strpos($response, 'pay') !== false) {
        echo "✅ 成功！参数校验通过\n";
        echo "🎉 支付页面正常访问\n";
        
        // 保存成功的URL
        file_put_contents('success_payment_url.txt', $paymentUrl);
        echo "成功的支付URL已保存到 success_payment_url.txt\n";
        
    } else {
        echo "⚠️ 未知响应\n";
        echo "响应前500字符:\n";
        echo substr(strip_tags($response), 0, 500) . "\n";
    }
}

echo "\n📄 保存测试数据:\n";
$testData = [
    'order_no' => $paymentRecord->out_trade_no,
    'payment_url' => $paymentUrl,
    'biz_content' => $bizContent,
    'request_params' => $requestParams,
    'server_ip' => $publicIP,
    'timestamp' => date('Y-m-d H:i:s'),
    'http_code' => $httpCode,
    'success' => strpos($response, '参数校验失败') === false
];

file_put_contents('test_data.json', json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
echo "测试数据已保存到 test_data.json\n";

echo "\n====================================\n";
echo "修复测试完成！\n";
echo "====================================\n";

// 获取公网IP的辅助函数
function getPublicIP() {
    $services = [
        'https://api.ipify.org',
        'https://ipinfo.io/ip', 
        'http://icanhazip.com'
    ];
    
    foreach ($services as $service) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $service);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $ip = trim(curl_exec($ch));
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200 && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE)) {
                return $ip;
            }
        } catch (Exception $e) {
            continue;
        }
    }
    
    // 如果获取失败，返回一个默认的公网IP
    return '**************';
} 