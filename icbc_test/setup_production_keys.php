<?php

/**
 * 生产环境工商银行支付密钥设置脚本
 * 
 * 本脚本用于在生产环境中设置真实的工商银行支付证书
 * 请确保您已从工商银行获取了正确的证书文件
 */

echo "🔐 工商银行支付生产环境密钥设置\n";
echo "=====================================\n\n";

// 确保在命令行运行
if (php_sapi_name() !== 'cli') {
    die("此脚本只能在命令行中运行\n");
}

// 密钥目录
$keysDir = __DIR__ . '/storage/keys';

// 确保密钥目录存在
if (!is_dir($keysDir)) {
    mkdir($keysDir, 0755, true);
    echo "✅ 创建密钥目录: {$keysDir}\n";
}

echo "📋 请准备以下证书文件：\n";
echo "1. 商户私钥文件 (merchant_private_key.pem)\n";
echo "2. 商户公钥文件 (merchant_public_key.pem)\n"; 
echo "3. 工行网关公钥文件 (icbc_gateway_public_key.pem)\n";
echo "4. 工行CA根证书文件 (icbc_ca_cert.pem)\n\n";

// 交互式设置
echo "🔧 开始交互式设置...\n\n";

/**
 * 处理证书文件输入
 */
function handleCertificateFile($prompt, $targetPath, $validateCallback = null) {
    echo $prompt . "\n";
    echo "请输入证书文件路径 (或输入 'paste' 直接粘贴证书内容): ";
    
    $input = trim(fgets(STDIN));
    
    if ($input === 'paste') {
        echo "请粘贴证书内容 (输入 'END' 结束):\n";
        $content = '';
        while (($line = fgets(STDIN)) !== false) {
            if (trim($line) === 'END') {
                break;
            }
            $content .= $line;
        }
    } else {
        if (!file_exists($input)) {
            echo "❌ 文件不存在: {$input}\n";
            return false;
        }
        $content = file_get_contents($input);
    }
    
    // 验证证书内容
    if ($validateCallback && !$validateCallback($content)) {
        echo "❌ 证书格式验证失败\n";
        return false;
    }
    
    // 保存文件
    if (file_put_contents($targetPath, $content)) {
        chmod($targetPath, 0600);
        echo "✅ 证书已保存: {$targetPath}\n";
        return true;
    } else {
        echo "❌ 保存失败: {$targetPath}\n";
        return false;
    }
}

/**
 * 验证私钥格式
 */
function validatePrivateKey($content) {
    $key = openssl_pkey_get_private($content);
    if ($key) {
        openssl_pkey_free($key);
        return true;
    }
    return false;
}

/**
 * 验证公钥格式
 */
function validatePublicKey($content) {
    $key = openssl_pkey_get_public($content);
    if ($key) {
        openssl_pkey_free($key);
        return true;
    }
    return false;
}

// 1. 设置商户私钥
echo "1️⃣ 设置商户私钥\n";
echo "================\n";
$privateKeyPath = $keysDir . '/icbc_private_key.pem';
handleCertificateFile(
    "商户私钥用于签名支付请求",
    $privateKeyPath,
    'validatePrivateKey'
);

// 2. 设置商户公钥
echo "\n2️⃣ 设置商户公钥\n";
echo "================\n";
$publicKeyPath = $keysDir . '/merchant_public_key.pem';
handleCertificateFile(
    "商户公钥提供给工商银行验证签名",
    $publicKeyPath,
    'validatePublicKey'
);

// 3. 设置工行网关公钥
echo "\n3️⃣ 设置工行网关公钥\n";
echo "====================\n";
$icbcPublicKeyPath = $keysDir . '/icbc_public_key.pem';
handleCertificateFile(
    "工行网关公钥用于验证工行返回的数据签名",
    $icbcPublicKeyPath,
    'validatePublicKey'
);

// 4. 设置工行CA证书
echo "\n4️⃣ 设置工行CA证书\n";
echo "==================\n";
$caCertPath = $keysDir . '/icbc_gateway_key.pem';
handleCertificateFile(
    "工行CA根证书用于SSL连接验证",
    $caCertPath
);

echo "\n🔧 生成环境配置建议\n";
echo "==================\n";

$envConfig = [
    'ICBC_ENVIRONMENT' => 'production',
    'ICBC_APP_ID' => 'your_production_app_id',
    'ICBC_MER_ID' => 'your_production_merchant_id', 
    'ICBC_MER_PRTCL_NO' => 'your_production_protocol_no',
    'ICBC_NOTIFY_URL' => 'https://your-domain.com/icbc-pay/notify',
    'ICBC_RETURN_URL' => 'https://your-domain.com/icbc-pay/return',
    'ICBC_VERIFY_SSL' => 'true',
    'ICBC_MOCK_ENABLED' => 'false',
    'ICBC_DEBUG_ENABLED' => 'false',
    'ICBC_TEST_MODE' => 'false'
];

echo "请在 .env 文件中设置以下配置：\n\n";
foreach ($envConfig as $key => $value) {
    echo "{$key}={$value}\n";
}

echo "\n📝 配置文件检查\n";
echo "===============\n";

// 检查配置文件
$configPath = __DIR__ . '/config/icbc-pay.php';
if (file_exists($configPath)) {
    echo "✅ 配置文件存在: {$configPath}\n";
} else {
    echo "❌ 配置文件不存在，请运行: php artisan vendor:publish --tag=icbc-pay-config\n";
}

echo "\n🚀 部署检查清单\n";
echo "===============\n";

$checkList = [
    "✅ 商户私钥文件已设置",
    "✅ 商户公钥文件已设置", 
    "✅ 工行网关公钥已设置",
    "✅ 工行CA证书已设置",
    "⏳ 更新 .env 文件中的生产环境配置",
    "⏳ 清理应用缓存: php artisan cache:clear",
    "⏳ 运行数据库迁移: php artisan migrate",
    "⏳ 测试支付接口连通性",
    "⏳ 验证回调URL可访问性",
    "⏳ 设置服务器SSL证书",
    "⏳ 配置防火墙允许工行IP访问"
];

foreach ($checkList as $item) {
    echo $item . "\n";
}

echo "\n⚠️  重要提醒\n";
echo "============\n";
echo "1. 生产环境证书文件权限应设置为 600\n";
echo "2. 确保服务器时间与标准时间同步\n";
echo "3. 生产环境必须使用HTTPS协议\n";
echo "4. 定期备份证书文件\n";
echo "5. 监控支付接口日志\n";

echo "\n🔒 安全建议\n";
echo "===========\n";
echo "1. 将证书文件存储在Web根目录之外\n";
echo "2. 定期轮换证书（建议1年）\n";
echo "3. 设置证书文件访问权限（仅应用可读）\n";
echo "4. 配置日志监控异常支付行为\n";
echo "5. 使用WAF保护支付接口\n";

echo "\n✅ 生产环境密钥设置完成！\n";
echo "========================\n";
echo "请按照检查清单完成剩余配置步骤。\n";
echo "如有问题，请联系工商银行技术支持。\n"; 