<?php

require_once '../vendor/autoload.php';

try {
    $app = require_once '../bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "💰 工商银行金额转换修复验证\n";
    echo "==============================\n\n";
    
    echo "🔍 问题分析：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "根据工商银行API文档，order_amt 参数应该是以'分'为单位的整数\n";
    echo "之前发送：order_amt: '0.01' (错误 - 这是元为单位)\n";
    echo "现在应该：order_amt: '1' (正确 - 这是分为单位)\n\n";
    
    // 测试不同金额的转换
    echo "📊 金额转换测试：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $testAmounts = [
        '0.01' => '1',      // 1分
        '0.10' => '10',     // 1角
        '1.00' => '100',    // 1元
        '10.50' => '1050',  // 10元5角
        '99.99' => '9999',  // 99元99分
        '100' => '10000',   // 100元（整数）
        '5.5' => '550',     // 5元5角（一位小数）
    ];
    
    foreach ($testAmounts as $yuan => $expectedFen) {
        echo "测试金额：{$yuan} 元 → 期望：{$expectedFen} 分\n";
    }
    echo "\n";
    
    // 1. 测试支付表单生成
    echo "🔧 第一步：测试支付表单生成中的金额转换\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $icbcClient = app(\IcbcPay\IcbcPayClient::class);
    
    // 测试0.01元的转换
    $testOrderData = [
        'order_id' => 'AMOUNT_TEST_' . time(),
        'amount' => '0.01',  // 1分钱
        'subject' => '金额转换测试',
        'payment_method' => 'wechat',
        'body' => '测试工商银行金额转换从元到分',
    ];
    
    echo "测试订单数据：\n";
    echo "  订单号：{$testOrderData['order_id']}\n";
    echo "  金额（元）：{$testOrderData['amount']}\n";
    echo "  期望转换（分）：1\n\n";
    
    echo "🔨 生成支付表单...\n";
    $startTime = microtime(true);
    
    $paymentForm = $icbcClient->buildForm($testOrderData);
    
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    echo "✅ 表单生成完成，耗时：{$duration} 毫秒\n";
    echo "📏 表单长度：" . strlen($paymentForm) . " 字符\n\n";
    
    // 2. 解析表单内容验证金额转换
    echo "🔍 第二步：解析表单验证金额转换\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // 提取biz_content
    if (preg_match('/name="biz_content" value="([^"]+)"/', $paymentForm, $matches)) {
        $bizContentRaw = html_entity_decode($matches[1]);
        $bizContent = json_decode($bizContentRaw, true);
        
        if ($bizContent && isset($bizContent['order_amt'])) {
            $extractedAmount = $bizContent['order_amt'];
            echo "✅ 成功提取 order_amt：{$extractedAmount}\n";
            
            if ($extractedAmount === '1') {
                echo "🎉 金额转换正确！0.01元 → 1分\n";
                echo "✅ 修复生效：现在发送给工行的是分为单位的整数\n";
            } else {
                echo "❌ 金额转换错误！期望：1，实际：{$extractedAmount}\n";
            }
            
            echo "\n完整的 biz_content：\n";
            echo json_encode($bizContent, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
        } else {
            echo "❌ 无法解析 biz_content 或未找到 order_amt\n";
        }
    } else {
        echo "❌ 无法从表单中提取 biz_content\n";
    }
    
    // 3. 测试不同金额的转换
    echo "🧪 第三步：测试多种金额转换\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    foreach ($testAmounts as $yuan => $expectedFen) {
        echo "测试 {$yuan} 元：";
        
        $testData = [
            'order_id' => 'TEST_' . time() . '_' . str_replace('.', '', $yuan),
            'amount' => $yuan,
            'subject' => "金额测试 {$yuan} 元",
            'payment_method' => 'wechat',
        ];
        
        try {
            $form = $icbcClient->buildForm($testData);
            
            if (preg_match('/name="biz_content" value="([^"]+)"/', $form, $matches)) {
                $bizContent = json_decode(html_entity_decode($matches[1]), true);
                $actualFen = $bizContent['order_amt'] ?? 'NOT_FOUND';
                
                if ($actualFen === $expectedFen) {
                    echo " ✅ 正确 ({$yuan}元 → {$actualFen}分)\n";
                } else {
                    echo " ❌ 错误 (期望:{$expectedFen}分, 实际:{$actualFen}分)\n";
                }
            } else {
                echo " ❌ 无法解析表单\n";
            }
        } catch (Exception $e) {
            echo " ❌ 转换失败：{$e->getMessage()}\n";
        }
        
        usleep(100000); // 100ms延迟避免太快
    }
    echo "\n";
    
    // 4. 检查日志中的转换记录
    echo "📋 第四步：检查转换日志\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $logFile = storage_path('logs/laravel.log');
    if (file_exists($logFile)) {
        $logContent = file_get_contents($logFile);
        $conversionLogs = [];
        
        // 查找金额转换日志
        if (preg_match_all('/ICBC AMOUNT CONVERSION: Yuan to Fen.*?converted_amount_fen.*?(\d+)/s', $logContent, $matches)) {
            echo "✅ 找到 " . count($matches[0]) . " 条金额转换日志\n";
            
            // 显示最近的5条
            $recentLogs = array_slice($matches[0], -5);
            foreach ($recentLogs as $log) {
                if (preg_match('/original_amount_yuan.*?([0-9.]+).*?converted_amount_fen.*?(\d+)/s', $log, $detailMatch)) {
                    echo "  {$detailMatch[1]} 元 → {$detailMatch[2]} 分\n";
                }
            }
        } else {
            echo "⚠️  未找到金额转换日志，可能需要触发一次支付请求\n";
        }
    } else {
        echo "❌ 日志文件不存在：{$logFile}\n";
    }
    echo "\n";
    
    // 5. 对比修复前后
    echo "📊 第五步：修复效果对比\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "修复前（错误）：\n";
    echo "  用户支付 0.01 元\n";
    echo "  发送给工行：order_amt = '0.01'  ❌ 错误！工行期望分为单位\n";
    echo "  工行可能拒绝或理解为0分\n\n";
    
    echo "修复后（正确）：\n";
    echo "  用户支付 0.01 元\n";
    echo "  发送给工行：order_amt = '1'     ✅ 正确！1分 = 0.01元\n";
    echo "  工行能正确处理金额\n\n";
    
    // 6. 回调验证逻辑检查
    echo "🔍 第六步：回调金额验证逻辑检查\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // 模拟回调验证
    $testAmount = 0.01; // 数据库中存储的金额（元）
    $expectedFenInCallback = bcmul($testAmount, 100, 0); // 转换为分
    
    echo "数据库金额（元）：{$testAmount}\n";
    echo "转换为分用于验证：{$expectedFenInCallback}\n";
    echo "回调中接收到的应该是：1 分\n";
    
    if ($expectedFenInCallback === '1') {
        echo "✅ 回调验证逻辑正确\n";
    } else {
        echo "❌ 回调验证逻辑有问题\n";
    }
    echo "\n";
    
    // 7. 最终总结
    echo "🎉 修复总结\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "✅ 问题根因：order_amt 参数单位错误（元 vs 分）\n";
    echo "✅ 修复方案：添加 convertToFen() 方法进行单位转换\n";
    echo "✅ 修复位置：IcbcPayClient->buildForm() 方法\n";
    echo "✅ 转换逻辑：金额（元） × 100 = 金额（分）\n";
    echo "✅ 精度控制：最多支持两位小数，四舍五入到整数分\n";
    echo "✅ 数据验证：检查金额格式、正负数、精度等\n";
    echo "✅ 日志记录：转换过程详细记录便于调试\n\n";
    
    echo "🔔 重要说明：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "1. 此修复可能解决之前遇到的部分支付问题\n";
    echo "2. 金额单位错误可能导致工行API拒绝请求或理解错误\n";
    echo "3. 建议重新测试完整的支付流程\n";
    echo "4. 特别注意小额支付（如0.01元）的处理\n";
    echo "5. 回调验证逻辑已经是正确的，无需修改\n\n";
    
    echo "💡 下一步建议：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "1. 在浏览器中测试 0.01 元支付\n";
    echo "2. 观察工行网关响应是否有改善\n";
    echo "3. 检查支付表单是否能正常提交\n";
    echo "4. 监控是否还有并发错误或其他API错误\n";
    echo "5. 考虑测试不同金额（1元、10元等）\n";
    
} catch (Exception $e) {
    echo "❌ 测试执行出错：" . $e->getMessage() . "\n";
    echo "📄 错误文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}

echo "\n" . "🎊 金额转换修复验证完成！" . "\n";
echo "=" . str_repeat("=", 40) . "\n"; 