<?php

declare(strict_types=1);

/**
 * Web调试支付功能
 */

require_once __DIR__ . '/vendor/autoload.php';

use IcbcPay\IcbcPayClient;
use IcbcPay\Models\PaymentRecord;

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', '1');

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 设置内容类型
header('Content-Type: application/json; charset=utf-8');

try {
    // 初始化Laravel应用（简化版）
    $app = new \Illuminate\Foundation\Application(realpath(__DIR__));
    
    // 绑定核心服务
    $app->singleton(
        \Illuminate\Contracts\Http\Kernel::class,
        \App\Http\Kernel::class
    );
    
    $app->singleton(
        \Illuminate\Contracts\Console\Kernel::class,
        \App\Console\Kernel::class
    );
    
    $app->singleton(
        \Illuminate\Contracts\Debug\ExceptionHandler::class,
        \App\Exceptions\Handler::class
    );

    // 创建内核
    $kernel = $app->make(\Illuminate\Contracts\Http\Kernel::class);
    
    // 启动应用
    $app->make(\Illuminate\Contracts\Console\Kernel::class);

    // 创建ICBC支付客户端
    $config = [
        'app_id' => '11000000000000052474',
        'merchant_id' => 'TEST_MERCHANT_001',
        'environment' => 'sandbox',
        'private_key_path' => __DIR__ . '/storage/keys/icbc_private_key.pem',
        'icbc_public_key_path' => __DIR__ . '/storage/keys/icbc_public_key.pem',
        'notify_url' => 'http://localhost:8000/icbc-pay/notify',
        'return_url' => 'http://localhost:8000/icbc-pay/return',
    ];

    $icbcClient = new IcbcPayClient($config);

    // 处理不同的请求
    $path = $_SERVER['REQUEST_URI'] ?? '/';
    $method = $_SERVER['REQUEST_METHOD'];

    if ($path === '/debug-pay' && $method === 'POST') {
        // 处理支付请求
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('无效的请求数据');
        }

        $orderData = [
            'order_id' => 'DEBUG_ORDER_' . time(),
            'amount' => $input['amount'] ?? '0.01',
            'subject' => '调试支付测试',
            'payment_method' => $input['payment_method'] ?? 'wechat',
            'body' => "调试支付 - 车牌：" . ($input['car_number'] ?? '测试A123'),
        ];

        $result = $icbcClient->pay($orderData);
        
        echo json_encode([
            'success' => true,
            'message' => '支付订单创建成功',
            'data' => $result
        ], JSON_UNESCAPED_UNICODE);

    } elseif ($path === '/debug-query' && $method === 'GET') {
        // 处理查询请求
        $orderId = $_GET['order_id'] ?? '';
        
        if (!$orderId) {
            throw new Exception('缺少订单号');
        }

        $result = $icbcClient->query($orderId);
        
        echo json_encode([
            'success' => true,
            'message' => '查询成功',
            'data' => $result
        ], JSON_UNESCAPED_UNICODE);

    } elseif ($path === '/debug-status' && $method === 'GET') {
        // 系统状态检查
        $status = [
            'php_version' => PHP_VERSION,
            'icbc_client' => 'initialized',
            'config_environment' => $icbcClient->getConfig('environment'),
            'config_app_id' => $icbcClient->getConfig('app_id'),
            'private_key_exists' => file_exists($config['private_key_path']),
            'public_key_exists' => file_exists($config['icbc_public_key_path']),
            'timestamp' => date('Y-m-d H:i:s'),
        ];

        echo json_encode([
            'success' => true,
            'message' => '系统状态正常',
            'data' => $status
        ], JSON_UNESCAPED_UNICODE);

    } else {
        // 显示调试页面
        ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICBC支付调试工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="max-w-4xl mx-auto px-4">
        <h1 class="text-3xl font-bold text-center mb-8">ICBC支付调试工具</h1>
        
        <!-- 系统状态 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">系统状态</h2>
            <div id="system-status" class="text-gray-600">加载中...</div>
        </div>

        <!-- 支付测试 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">支付测试</h2>
            <form id="payment-form" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">车牌号</label>
                        <input type="text" name="car_number" value="京A12345" class="w-full border rounded-lg px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">金额</label>
                        <input type="text" name="amount" value="0.01" class="w-full border rounded-lg px-3 py-2">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">支付方式</label>
                    <select name="payment_method" class="w-full border rounded-lg px-3 py-2">
                        <option value="wechat">微信支付</option>
                        <option value="alipay">支付宝</option>
                        <option value="unionpay">银联支付</option>
                    </select>
                </div>
                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                    创建支付订单
                </button>
            </form>
            <div id="payment-result" class="mt-4 hidden"></div>
        </div>

        <!-- 查询测试 -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">订单查询</h2>
            <form id="query-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">订单号</label>
                    <input type="text" name="order_id" placeholder="输入订单号" class="w-full border rounded-lg px-3 py-2">
                </div>
                <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700">
                    查询订单状态
                </button>
            </form>
            <div id="query-result" class="mt-4 hidden"></div>
        </div>
    </div>

    <script>
        // 加载系统状态
        function loadSystemStatus() {
            $.get('/debug-status')
                .done(function(response) {
                    if (response.success) {
                        let html = '<div class="grid grid-cols-2 gap-4 text-sm">';
                        for (let key in response.data) {
                            let value = response.data[key];
                            if (typeof value === 'boolean') {
                                value = value ? '是' : '否';
                            }
                            html += `<div><strong>${key}:</strong> ${value}</div>`;
                        }
                        html += '</div>';
                        $('#system-status').html(html);
                    }
                })
                .fail(function() {
                    $('#system-status').html('<div class="text-red-600">状态加载失败</div>');
                });
        }

        // 处理支付表单
        $('#payment-form').on('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                car_number: $('input[name="car_number"]').val(),
                amount: $('input[name="amount"]').val(),
                payment_method: $('select[name="payment_method"]').val()
            };

            $.ajax({
                url: '/debug-pay',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(response) {
                    $('#payment-result').removeClass('hidden').html(
                        '<div class="bg-green-50 border border-green-200 rounded p-4">' +
                        '<h3 class="font-semibold text-green-800">支付订单创建成功</h3>' +
                        '<pre class="text-sm mt-2">' + JSON.stringify(response.data, null, 2) + '</pre>' +
                        '</div>'
                    );
                    
                    // 自动填入订单号到查询表单
                    if (response.data.order_id) {
                        $('input[name="order_id"]').val(response.data.order_id);
                    }
                },
                error: function() {
                    $('#payment-result').removeClass('hidden').html(
                        '<div class="bg-red-50 border border-red-200 rounded p-4 text-red-600">' +
                        '支付订单创建失败' +
                        '</div>'
                    );
                }
            });
        });

        // 处理查询表单
        $('#query-form').on('submit', function(e) {
            e.preventDefault();
            
            const orderId = $('input[name="order_id"]').val();
            
            $.get('/debug-query', { order_id: orderId })
                .done(function(response) {
                    $('#query-result').removeClass('hidden').html(
                        '<div class="bg-blue-50 border border-blue-200 rounded p-4">' +
                        '<h3 class="font-semibold text-blue-800">查询结果</h3>' +
                        '<pre class="text-sm mt-2">' + JSON.stringify(response.data, null, 2) + '</pre>' +
                        '</div>'
                    );
                })
                .fail(function() {
                    $('#query-result').removeClass('hidden').html(
                        '<div class="bg-red-50 border border-red-200 rounded p-4 text-red-600">' +
                        '查询失败' +
                        '</div>'
                    );
                });
        });

        // 页面加载时获取系统状态
        $(document).ready(function() {
            loadSystemStatus();
        });
    </script>
</body>
</html>
        <?php
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?> 