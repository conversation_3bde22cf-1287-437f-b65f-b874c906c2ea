<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;
use IcbcPay\SDK\DefaultIcbcClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🧪 工商银行支付实际请求测试\n";
    echo "==========================\n\n";
    
    echo "📋 测试目标：验证修复后的时间戳是否解决了API调用问题\n\n";
    
    // 创建支付客户端
    $client = app(IcbcPayClient::class);
    
    // 生成测试订单
    $testOrderData = [
        'order_id' => 'REAL_TEST_' . time(),
        'amount' => 0.01,
        'subject' => '时间戳修复验证',
        'payment_method' => 'wechat'
    ];
    
    echo "🔧 生成支付参数...\n";
    
    // 直接获取支付客户端的配置
    $config = $client->getConfig();
    
    // 创建DefaultIcbcClient进行API调用
    $icbcClient = new DefaultIcbcClient(
        $config['app_id'],
        $config['private_key_content'],
        'RSA2',
        'UTF-8',
        'json',
        $config['icbc_public_key_content'] ?? null
    );
    
    // 准备API请求参数
    $requestParams = [
        'serviceUrl' => $config['gateway_url'] . '/api/cardbusiness/aggregatepay/consumepurchase',
        'method' => 'POST',
        'biz_content' => [
            'mer_id' => $config['mer_id'],
            'mer_prtcl_no' => $config['mer_prtcl_no'],
            'out_trade_no' => $testOrderData['order_id'],
            'order_amt' => (string)$testOrderData['amount'],
            'pay_mode' => '9', // 微信支付
            'access_type' => '1',
            'mer_url' => $config['notify_url'],
            'goods_body' => $testOrderData['subject'],
            'goods_detail' => $testOrderData['subject'],
            'expire_time' => date('Y-m-d H:i:s', time() + 1800), // 30分钟后过期
            'page_url' => $config['return_url'],
            'currency' => 'CNY',
        ],
        'isNeedEncrypt' => false,
    ];
    
    // 生成MSG ID
    $msgId = date('YmdHis') . sprintf('%010d', mt_rand(0, 9999999999));
    
    echo "📤 发送API请求...\n";
    echo "   服务URL: " . $requestParams['serviceUrl'] . "\n";
    echo "   MSG ID: " . $msgId . "\n";
    echo "   订单号: " . $testOrderData['order_id'] . "\n";
    
    try {
        // 执行API请求
        $response = $icbcClient->execute($requestParams, $msgId);
        
        echo "\n✅ API请求成功!\n";
        echo "📋 响应内容:\n";
        echo "============\n";
        
        // 解析响应
        $responseData = json_decode($response, true);
        if ($responseData) {
            echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            
            // 检查响应状态
            if (isset($responseData['return_code'])) {
                $returnCode = $responseData['return_code'];
                $returnMsg = $responseData['return_msg'] ?? '';
                
                echo "\n📊 响应分析:\n";
                echo "   返回码: " . $returnCode . "\n";
                echo "   返回信息: " . $returnMsg . "\n";
                
                if ($returnCode === '0' || $returnCode === 0) {
                    echo "   ✅ 状态: 成功\n";
                } else {
                    echo "   ❌ 状态: 失败\n";
                    
                    // 具体错误分析
                    switch ($returnCode) {
                        case '400017':
                            echo "   🔍 错误分析: 签名验证失败\n";
                            break;
                        case '400011':
                            echo "   🔍 错误分析: 请求超时或时间戳问题\n";
                            break;
                        case '400001':
                            echo "   🔍 错误分析: 参数错误\n";
                            break;
                        default:
                            echo "   🔍 错误分析: 其他错误 (错误码: {$returnCode})\n";
                    }
                }
            }
        } else {
            echo $response . "\n";
        }
        
    } catch (Exception $e) {
        echo "\n❌ API请求失败!\n";
        echo "📋 错误信息:\n";
        echo "============\n";
        echo "错误类型: " . get_class($e) . "\n";
        echo "错误消息: " . $e->getMessage() . "\n";
        echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
        
        // 如果是HTTP错误，尝试获取更多信息
        if (method_exists($e, 'getResponse')) {
            $response = $e->getResponse();
            if ($response) {
                echo "HTTP状态码: " . $response->getStatusCode() . "\n";
                echo "响应内容: " . $response->getBody() . "\n";
            }
        }
    }
    
    echo "\n🔍 时间戳检查:\n";
    
    // 检查当前生成的时间戳
    date_default_timezone_set('Asia/Shanghai');
    $currentTimestamp = date('Y-m-d H:i:s');
    echo "   当前时间戳: " . $currentTimestamp . "\n";
    echo "   格式验证: " . (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $currentTimestamp) ? '✅ 正确' : '❌ 错误') . "\n";
    echo "   时区: Asia/Shanghai (北京时间)\n";
    
    // 与UTC时间对比
    $utcTime = gmdate('Y-m-d H:i:s');
    echo "   UTC时间: " . $utcTime . "\n";
    echo "   时差: " . ((strtotime($currentTimestamp) - strtotime($utcTime)) / 3600) . " 小时\n";
    
    echo "\n💡 如果仍有时间戳问题，可能的原因：\n";
    echo "1. 工行服务器时间与本地时间有细微差异\n";
    echo "2. 网络延迟导致请求时间与生成时间不一致\n";
    echo "3. 工行服务器对时间戳精度有特殊要求\n";
    echo "4. 其他API参数问题（非时间戳相关）\n";
    
} catch (Exception $e) {
    echo "❌ 脚本执行错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
} 