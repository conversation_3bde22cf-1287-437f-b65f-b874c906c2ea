<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "创建客户端实例...\n";
    $client = app(IcbcPayClient::class);
    echo "✅ 客户端创建成功\n";
    
    echo "\n检查配置信息...\n";
    $config = $client->getConfig();
    echo "Environment: " . gettype($config['environment']) . " = ";
    var_dump($config['environment']);
    
    echo "Gateways: ";
    var_dump($config['gateways']);
    
    echo "\n准备测试数据...\n";
    $testOrderData = [
        'order_id' => 'TEST_' . time(),
        'amount' => 0.01,
        'subject' => '客户端测试',
        'payment_method' => 'wechat'
    ];
    echo "✅ 测试数据准备完成\n";
    
    echo "调用pay方法...\n";
    $result = $client->pay($testOrderData);
    echo "✅ 调用成功\n";
    
    echo "结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "堆栈: " . $e->getTraceAsString() . "\n";
} 