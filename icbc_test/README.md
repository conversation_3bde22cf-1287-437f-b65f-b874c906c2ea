# ICBC 支付系统测试文件目录

本目录包含所有与 ICBC 支付系统相关的测试、修复和调试文件。

## 🧪 主要测试文件

### 支付功能测试
- `test_icbc_payment.php` - 基础支付功能测试
- `test_complete_payment_flow.php` - 完整支付流程测试
- `test_icbc_after_rate_limit_fix.php` - 频率限制修复后的测试
- `test_payment.php` - 支付功能测试
- `test_api_simple.php` - 简单API测试
- `test_minimal_icbc.php` - 最小化ICBC测试

### 签名相关测试
- `test_icbc_signature_fix.php` - 签名修复测试
- `fix_sign_verify_failed.php` - 签名验证失败修复工具

### 时间戳相关测试
- `test_timestamp_fix.php` - 时间戳修复测试
- `test_simple_timestamp.php` - 简单时间戳测试
- `test_timestamp_format_fix.php` - 时间戳格式修复测试
- `test_utc_timestamp.php` - UTC时间戳测试
- `final_timestamp_verification.php` - 最终时间戳验证

### 实时请求测试
- `test_icbc_real_request.php` - 真实ICBC请求测试
- `test_rate_limiter_status.php` - 频率限制状态测试

## 🛠️ 修复和调试工具

### 配置和参数修复
- `fix_icbc_params.php` - ICBC参数修复
- `fix_param_validation.php` - 参数验证修复
- `fix_payment_issues.php` - 支付问题修复
- `validate_icbc_config.php` - ICBC配置验证

### 证书和签名修复
- `fix_icbc_certificate.php` - ICBC证书修复
- `fix_icbc_signature_issue.php` - ICBC签名问题修复
- `fix_signature_verification.php` - 签名验证修复

### 时间和消息ID修复
- `fix_timezone_issues.php` - 时区问题修复
- `fix_timestamp_timeout.php` - 时间戳超时修复
- `test_msg_id_fix.php` - 消息ID修复测试

### 调试工具
- `debug_payment.php` - 支付调试
- `debug_payment_api.php` - 支付API调试
- `debug_payment_web.php` - Web支付调试
- `test_debug.php` - 基础调试测试

## 🔧 配置和部署工具

### 环境配置
- `set_optimal_env.php` - 设置最优环境配置
- `check_basic_config.php` - 基础配置检查

### 证书设置
- `setup_icbc_certificates.php` - 设置ICBC证书
- `setup_test_keys.php` - 设置测试密钥
- `setup_production_keys.php` - 设置生产环境密钥

### 部署工具
- `deploy_payment_system.php` - 部署支付系统
- `fix_payment.sh` - 支付修复脚本

## 📊 分析和检查工具

- `analyze_payment_url.php` - 分析支付URL
- `deep_param_analysis.php` - 深度参数分析
- `get_icbc_error_detail.php` - 获取ICBC错误详情

## 🌐 HTML和数据文件

- `test_payment_form.html` - 测试支付表单
- `icbc_error.html` - ICBC错误页面
- `icbc_error_response.html` - ICBC错误响应页面
- `sign_test_unknown.html` - 签名测试页面
- `test_data.json` - 测试数据文件

## 📝 快速测试

- `quick_test.php` - 快速测试工具
- `test_icbc_api.php` - ICBC API测试

## 使用说明

1. **运行测试前确保配置正确**：先运行 `validate_icbc_config.php`
2. **签名问题**：使用 `fix_sign_verify_failed.php`
3. **时间戳问题**：使用相关的时间戳测试文件
4. **完整测试**：运行 `test_complete_payment_flow.php`

## 注意事项

- 所有测试文件都可以直接在浏览器中运行
- 修复工具会自动备份原始配置
- 建议按顺序运行测试，从基础配置到完整流程 