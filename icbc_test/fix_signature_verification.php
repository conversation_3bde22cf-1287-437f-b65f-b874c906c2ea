<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🔐 工商银行支付签名验证失败问题修复\n";
    echo "=====================================\n\n";
    
    echo "📋 问题分析：\n";
    echo "错误代码: 400017\n";
    echo "错误信息: sign verify failed\n";
    echo "原因: RSA2签名生成或验证不正确\n\n";
    
    echo "🛠️ 修复方案：\n";
    echo "1. 检查私钥文件格式和路径\n";
    echo "2. 验证签名字符串构建规则\n";
    echo "3. 确保RSA2算法正确使用\n";
    echo "4. 检查参数排序和编码\n\n";
    
    echo "🔧 创建支付客户端...\n";
    $client = app(IcbcPayClient::class);
    echo "✅ 客户端创建成功\n\n";
    
    echo "🔍 检查密钥文件：\n";
    $privateKeyPath = storage_path('keys/icbc_private_key.pem');
    $publicKeyPath = storage_path('keys/icbc_public_key.pem');
    
    echo "私钥路径: " . $privateKeyPath . "\n";
    echo "私钥存在: " . (file_exists($privateKeyPath) ? '✅ 是' : '❌ 否') . "\n";
    
    if (file_exists($privateKeyPath)) {
        $privateKeyContent = file_get_contents($privateKeyPath);
        echo "私钥长度: " . strlen($privateKeyContent) . " 字节\n";
        echo "私钥格式: " . (strpos($privateKeyContent, '-----BEGIN') !== false ? '✅ PEM格式' : '⚠️ 可能需要格式化') . "\n";
        
        // 测试私钥是否可用
        $testKey = openssl_pkey_get_private($privateKeyContent);
        if ($testKey) {
            echo "私钥验证: ✅ 有效\n";
            openssl_pkey_free($testKey);
        } else {
            echo "私钥验证: ❌ 无效 - " . openssl_error_string() . "\n";
        }
    }
    
    echo "公钥路径: " . $publicKeyPath . "\n";
    echo "公钥存在: " . (file_exists($publicKeyPath) ? '✅ 是' : '❌ 否') . "\n\n";
    
    echo "🧪 测试签名生成：\n";
    
    // 测试数据
    $testOrderData = [
        'order_id' => 'SIGN_TEST_' . time(),
        'amount' => 0.01,
        'subject' => '签名测试订单',
        'payment_method' => 'wechat'
    ];
    
    echo "生成支付表单...\n";
    $formHtml = $client->buildForm($testOrderData);
    echo "✅ 表单生成成功\n\n";
    
    echo "📊 签名分析：\n";
    
    // 提取表单字段
    $fields = [];
    preg_match_all('/name="([^"]+)" value="([^"]*)"/', $formHtml, $matches, PREG_SET_ORDER);
    
    foreach ($matches as $match) {
        $fields[$match[1]] = html_entity_decode($match[2]);
    }
    
    if (isset($fields['sign'])) {
        $signature = $fields['sign'];
        unset($fields['sign']);
        
        echo "✅ 提取到签名: " . substr($signature, 0, 50) . "...\n";
        echo "✅ 签名长度: " . strlen($signature) . " 字符\n";
        echo "✅ 签名格式: " . (base64_decode($signature, true) !== false ? 'Base64编码' : '非Base64') . "\n";
        
        // 重新构建签名字符串验证
        ksort($fields);
        $signParts = [];
        foreach ($fields as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signParts[] = $key . '=' . $value;
            }
        }
        $signString = implode('&', $signParts);
        
        echo "\n📝 签名字符串构建：\n";
        echo "参数数量: " . count($fields) . "\n";
        echo "签名字符串长度: " . strlen($signString) . " 字符\n";
        echo "签名字符串预览: " . substr($signString, 0, 200) . "...\n\n";
        
        // 显示关键参数
        echo "🔑 关键参数验证：\n";
        $keyParams = ['app_id', 'msg_id', 'timestamp', 'biz_content'];
        foreach ($keyParams as $param) {
            if (isset($fields[$param])) {
                $value = $fields[$param];
                if ($param === 'biz_content') {
                    $value = substr($value, 0, 100) . '...';
                }
                echo "✅ {$param}: {$value}\n";
            } else {
                echo "❌ {$param}: 缺失\n";
            }
        }
        
    } else {
        echo "❌ 未找到签名字段\n";
    }
    
    echo "\n🔄 签名验证测试：\n";
    
    // 模拟工行返回的签名验证
    if (isset($signature) && isset($signString)) {
        echo "尝试验证生成的签名...\n";
        
        if (file_exists($privateKeyPath)) {
            $privateKeyContent = file_get_contents($privateKeyPath);
            
            // 处理私钥格式
            if (strpos($privateKeyContent, '-----BEGIN') === false) {
                $privateKeyContent = "-----BEGIN RSA PRIVATE KEY-----\n" . 
                                   chunk_split($privateKeyContent, 64, "\n") . 
                                   "-----END RSA PRIVATE KEY-----\n";
            }
            
            $privateKey = openssl_pkey_get_private($privateKeyContent);
            
            if ($privateKey) {
                // 重新生成签名进行对比
                $testSignature = '';
                if (openssl_sign($signString, $testSignature, $privateKey, OPENSSL_ALGO_SHA256)) {
                    $testSignatureBase64 = base64_encode($testSignature);
                    
                    echo "✅ 重新生成签名成功\n";
                    echo "原始签名: " . substr($signature, 0, 50) . "...\n";
                    echo "测试签名: " . substr($testSignatureBase64, 0, 50) . "...\n";
                    echo "签名匹配: " . ($signature === $testSignatureBase64 ? '✅ 一致' : '⚠️ 不一致') . "\n";
                } else {
                    echo "❌ 重新生成签名失败: " . openssl_error_string() . "\n";
                }
                
                openssl_pkey_free($privateKey);
            } else {
                echo "❌ 私钥加载失败: " . openssl_error_string() . "\n";
            }
        }
    }
    
    echo "\n🎯 修复建议：\n";
    
    if (!file_exists($privateKeyPath)) {
        echo "❌ 私钥文件不存在，请检查：\n";
        echo "   1. 运行 php setup_test_keys.php 生成测试密钥\n";
        echo "   2. 或者配置正确的私钥路径\n\n";
    }
    
    echo "📋 常见签名问题解决方案：\n";
    echo "1. 参数排序问题：\n";
    echo "   - 确保按ASCII码排序\n";
    echo "   - 过滤空值参数\n";
    echo "   - 排除sign字段\n\n";
    
    echo "2. 字符编码问题：\n";
    echo "   - 使用UTF-8编码\n";
    echo "   - 避免特殊字符转义\n";
    echo "   - JSON参数使用JSON_UNESCAPED_UNICODE\n\n";
    
    echo "3. 私钥格式问题：\n";
    echo "   - 确保PEM格式完整\n";
    echo "   - 检查换行符和空格\n";
    echo "   - 验证私钥有效性\n\n";
    
    echo "4. 算法问题：\n";
    echo "   - 使用OPENSSL_ALGO_SHA256\n";
    echo "   - 确保RSA2算法\n";
    echo "   - Base64编码结果\n\n";
    
    echo "🔧 快速修复命令：\n";
    echo "# 重新生成测试密钥\n";
    echo "php setup_test_keys.php\n\n";
    echo "# 验证配置\n";
    echo "php validate_icbc_config.php\n\n";
    echo "# 测试签名\n";
    echo "php test_icbc_api.php\n\n";
    
    echo "✅ 签名问题诊断完成！\n";
    echo "请根据上述建议修复相关问题。\n";
    
} catch (Exception $e) {
    echo "❌ 诊断过程中出现错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "\n请检查：\n";
    echo "1. Laravel应用是否正确启动\n";
    echo "2. IcbcPayClient类是否正确注册\n";
    echo "3. 配置文件是否存在\n";
    echo "4. 密钥文件路径是否正确\n";
}
