<?php

/**
 * 停车费支付系统部署脚本
 * 
 * 自动检查并设置Laravel + 工商银行支付系统的所有必要组件
 */

require_once __DIR__ . '/vendor/autoload.php';

echo "🚀 停车费支付系统部署脚本\n";
echo "=========================\n\n";

// 启动Laravel应用
try {
    $app = require_once __DIR__ . '/bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
} catch (Exception $e) {
    echo "❌ Laravel应用启动失败: " . $e->getMessage() . "\n";
    exit(1);
}

echo "✅ Laravel应用启动成功\n\n";

// 导入必要的类
use IcbcPay\Models\PaymentRecord;
use IcbcPay\IcbcPayClient;

/**
 * 检查基础环境
 */
function checkEnvironment() {
    echo "📋 1. 检查基础环境\n";
    echo "=================\n";

    // 检查PHP版本
    $phpVersion = PHP_VERSION;
    echo "PHP版本: {$phpVersion}";
    if (version_compare($phpVersion, '8.1.0', '>=')) {
        echo " ✅\n";
    } else {
        echo " ❌ (需要PHP 8.1+)\n";
        return false;
    }

    // 检查必要扩展
    $requiredExtensions = ['curl', 'openssl', 'json', 'mbstring', 'pdo'];
    foreach ($requiredExtensions as $ext) {
        echo "扩展 {$ext}: ";
        if (extension_loaded($ext)) {
            echo "✅\n";
        } else {
            echo "❌ 缺失\n";
            return false;
        }
    }

    // 检查目录权限
    $directories = ['storage', 'storage/keys', 'storage/logs', 'bootstrap/cache'];
    foreach ($directories as $dir) {
        $path = __DIR__ . '/' . $dir;
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
        echo "目录权限 {$dir}: ";
        if (is_writable($path)) {
            echo "✅\n";
        } else {
            echo "❌ 无写入权限\n";
            return false;
        }
    }

    return true;
}

/**
 * 检查数据库配置
 */
function checkDatabase() {
    echo "\n📋 2. 检查数据库配置\n";
    echo "==================\n";

    try {
        $connection = config('database.default');
        echo "数据库连接: {$connection} ";
        
        $pdo = DB::connection()->getPdo();
        echo "✅\n";

        // 检查是否有支付记录表
        echo "检查支付记录表: ";
        if (Schema::hasTable('icbc_payment_records')) {
            echo "✅ 已存在\n";
        } else {
            echo "❌ 不存在，需要运行迁移\n";
            return false;
        }

        return true;
    } catch (Exception $e) {
        echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * 检查ICBC支付配置
 */
function checkIcbcConfig() {
    echo "\n📋 3. 检查ICBC支付配置\n";
    echo "====================\n";

    $config = config('icbc-pay');
    
    if (empty($config)) {
        echo "❌ ICBC支付配置未找到\n";
        return false;
    }

    // 检查必要配置项
    $requiredKeys = ['app_id', 'mer_id', 'private_key_path'];
    foreach ($requiredKeys as $key) {
        echo "配置项 {$key}: ";
        if (!empty($config[$key])) {
            echo "✅\n";
        } else {
            echo "❌ 未设置\n";
            return false;
        }
    }

    // 检查私钥文件
    $privateKeyPath = $config['private_key_path'];
    echo "私钥文件: ";
    if (file_exists($privateKeyPath)) {
        echo "✅ 存在\n";
        
        // 测试私钥格式
        $privateKeyContent = file_get_contents($privateKeyPath);
        $privateKey = openssl_pkey_get_private($privateKeyContent);
        if ($privateKey) {
            echo "私钥格式: ✅ 正确\n";
            openssl_pkey_free($privateKey);
        } else {
            echo "私钥格式: ❌ 错误\n";
            return false;
        }
    } else {
        echo "❌ 不存在\n";
        return false;
    }

    return true;
}

/**
 * 检查路由配置
 */
function checkRoutes() {
    echo "\n📋 4. 检查路由配置\n";
    echo "================\n";

    $routes = [
        'parking.index' => '/',
        'parking.payment' => '/pay/{orderNo}',
        'parking.result' => '/payment/result/{orderNo}',
        'icbc.notify' => '/icbc-pay/notify',
        'icbc.return' => '/icbc-pay/return',
        'pay.create' => 'api/pay',
        'pay.query' => 'api/pay/query/{outTradeNo}'
    ];

    foreach ($routes as $name => $uri) {
        echo "路由 {$name}: ";
        try {
            $url = route($name, ['orderNo' => 'test', 'outTradeNo' => 'test']);
            echo "✅\n";
        } catch (Exception $e) {
            echo "❌ 路由不存在\n";
            return false;
        }
    }

    return true;
}

/**
 * 检查视图文件
 */
function checkViews() {
    echo "\n📋 5. 检查视图文件\n";
    echo "================\n";

    $views = [
        'parking.index',
        'parking.payment', 
        'parking.result',
        'parking.fallback'
    ];

    foreach ($views as $view) {
        echo "视图 {$view}: ";
        if (view()->exists($view)) {
            echo "✅\n";
        } else {
            echo "❌ 不存在\n";
            return false;
        }
    }

    return true;
}

/**
 * 创建测试订单
 */
function createTestOrder() {
    echo "\n📋 6. 创建测试订单\n";
    echo "================\n";

    try {
        $testOrder = PaymentRecord::create([
            'out_trade_no' => 'DEPLOY_TEST_' . time() . rand(1000, 9999),
            'total_amount' => 0.01,
            'subject' => '部署测试订单',
            'payment_method' => 'wechat',
            'car_number' => '部署测试001',
            'parking_duration' => 30,
            'status' => 'pending'
        ]);

        echo "测试订单创建: ✅ {$testOrder->out_trade_no}\n";

        // 清理测试订单
        $testOrder->delete();
        echo "测试订单清理: ✅\n";

        return true;
    } catch (Exception $e) {
        echo "❌ 测试订单创建失败: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * 测试支付客户端
 */
function testPaymentClient() {
    echo "\n📋 7. 测试支付客户端\n";
    echo "==================\n";

    try {
        $client = app(IcbcPayClient::class);
        echo "客户端实例化: ✅\n";

        // 测试支付方法调用
        $testOrderData = [
            'order_id' => 'TEST_' . time(),
            'amount' => 0.01,
            'subject' => '客户端测试',
            'payment_method' => 'wechat'
        ];

        $result = $client->pay($testOrderData);
        
        // 检查返回结果
        if (is_array($result) && isset($result['success']) && $result['success']) {
            echo "支付接口调用: ✅\n";
            
            // 测试其他方法
            $formHtml = $client->buildForm($testOrderData);
            if (is_string($formHtml) && !empty($formHtml)) {
                echo "支付表单生成: ✅\n";
            } else {
                echo "支付表单生成: ❌\n";
                return false;
            }
            
            // 测试查询方法
            $queryResult = $client->query($testOrderData['order_id']);
            if (is_array($queryResult) && isset($queryResult['success'])) {
                echo "订单查询方法: ✅\n";
            } else {
                echo "订单查询方法: ❌\n";
                return false;
            }
            
        } else {
            echo "支付接口调用: ❌ 返回结果异常\n";
            echo "返回值类型: " . gettype($result) . "\n";
            if (is_array($result)) {
                echo "返回内容: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
            }
            return false;
        }

        return true;
    } catch (Exception $e) {
        echo "❌ 支付客户端测试失败: " . $e->getMessage() . "\n";
        echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
        return false;
    }
}

/**
 * 运行所有检查
 */
function runAllChecks() {
    $checks = [
        'checkEnvironment',
        'checkDatabase', 
        'checkIcbcConfig',
        'checkRoutes',
        'checkViews',
        'createTestOrder',
        'testPaymentClient'
    ];

    $allPassed = true;
    foreach ($checks as $check) {
        if (!$check()) {
            $allPassed = false;
        }
    }

    return $allPassed;
}

/**
 * 自动修复问题
 */
function autoFix() {
    echo "\n🔧 自动修复问题\n";
    echo "===============\n";

    // 运行数据库迁移
    echo "运行数据库迁移: ";
    try {
        Artisan::call('migrate', ['--force' => true]);
        echo "✅\n";
    } catch (Exception $e) {
        echo "❌ " . $e->getMessage() . "\n";
    }

    // 清理缓存
    echo "清理应用缓存: ";
    try {
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        Artisan::call('view:clear');
        echo "✅\n";
    } catch (Exception $e) {
        echo "❌ " . $e->getMessage() . "\n";
    }

    // 生成应用密钥（如果需要）
    if (empty(config('app.key'))) {
        echo "生成应用密钥: ";
        try {
            Artisan::call('key:generate', ['--force' => true]);
            echo "✅\n";
        } catch (Exception $e) {
            echo "❌ " . $e->getMessage() . "\n";
        }
    }

    // 创建存储链接
    echo "创建存储链接: ";
    try {
        Artisan::call('storage:link');
        echo "✅\n";
    } catch (Exception $e) {
        echo "⚠️ 可能已存在\n";
    }
}

/**
 * 生成部署报告
 */
function generateReport($allPassed) {
    echo "\n📊 部署报告\n";
    echo "==========\n";

    if ($allPassed) {
        echo "🎉 恭喜！支付系统部署成功！\n\n";
        
        echo "✅ 所有检查都已通过\n";
        echo "✅ 系统已准备就绪\n\n";
        
        echo "🌐 访问地址:\n";
        echo "- 支付首页: " . route('parking.index') . "\n";
        echo "- API文档: /api/pay (POST)\n\n";
        
        echo "🔧 下一步操作:\n";
        echo "1. 配置真实的工行支付证书\n";
        echo "2. 设置生产环境域名和SSL\n";
        echo "3. 配置防火墙规则\n";
        echo "4. 设置监控和告警\n";
        echo "5. 进行压力测试\n";
    } else {
        echo "❌ 部署检查未完全通过\n\n";
        
        echo "🔧 建议操作:\n";
        echo "1. 检查上述失败项目\n";
        echo "2. 运行自动修复: php deploy_payment_system.php --fix\n";
        echo "3. 手动配置缺失项目\n";
        echo "4. 重新运行部署检查\n";
    }
}

// 主执行流程
$autoFixMode = in_array('--fix', $argv);

if ($autoFixMode) {
    autoFix();
}

$allPassed = runAllChecks();
generateReport($allPassed);

echo "\n" . str_repeat("=", 50) . "\n";
echo "部署脚本执行完成\n";
echo "如有问题，请查看详细日志: storage/logs/laravel.log\n";

exit($allPassed ? 0 : 1); 