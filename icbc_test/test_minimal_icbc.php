<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "最简化工商银行支付测试\n";
echo "==========================\n\n";

// 获取配置
$config = config('icbc-pay');

// 构建最基础的业务参数
$bizContent = [
    'mer_id' => $config['mer_id'],
    'mer_prtcl_no' => $config['mer_prtcl_no'],
    'out_trade_no' => 'MINIMAL_' . time(),
    'orig_date_time' => date('Y-m-d H:i:s'),
    'total_fee' => '1', // 1分钱
    'body' => '测试',
    'pay_mode' => '9',  // 微信
    'access_type' => '1' // 扫码支付
];

echo "业务参数:\n";
foreach ($bizContent as $key => $value) {
    echo "  {$key}: {$value}\n";
}
echo "\n";

// 构建请求参数
$requestParams = [
    'app_id' => $config['app_id'],
    'charset' => 'UTF-8',
    'format' => 'json',
    'sign_type' => 'RSA2',
    'timestamp' => date('Y-m-d H:i:s'),
    'version' => 'V1',
    'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
];

echo "请求参数:\n";
foreach ($requestParams as $key => $value) {
    if ($key === 'biz_content') {
        echo "  {$key}: " . $value . "\n";
    } else {
        echo "  {$key}: {$value}\n";
    }
}
echo "\n";

// 生成签名
ksort($requestParams);
$signString = '';
foreach ($requestParams as $key => $value) {
    if ($value !== '' && $value !== null) {
        $signString .= $key . '=' . $value . '&';
    }
}
$signString = rtrim($signString, '&');

echo "待签名字符串:\n";
echo $signString . "\n\n";

// 获取私钥
$privateKeyContent = file_get_contents($config['private_key_path']);
$privateKey = openssl_pkey_get_private($privateKeyContent);

if (!$privateKey) {
    echo "❌ 私钥加载失败\n";
    exit;
}

// 生成签名
$signature = '';
if (openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
    $finalSign = base64_encode($signature);
    echo "✅ 签名生成成功，长度: " . strlen($finalSign) . " 字符\n";
} else {
    echo "❌ 签名生成失败\n";
    exit;
}

$requestParams['sign'] = $finalSign;

// 构建支付URL
$baseUrl = $config['gateway_url'] . $config['api_urls']['consume_purchase_ui'];
$queryString = http_build_query($requestParams);
$paymentUrl = $baseUrl . '?' . $queryString;

echo "\n最终支付URL:\n";
echo $paymentUrl . "\n\n";

// 测试URL
echo "测试URL访问:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $paymentUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; ICBC-Test/1.0)');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: {$httpCode}\n";

if ($error) {
    echo "❌ CURL错误: {$error}\n";
} else {
    if (strpos($response, '参数校验失败') !== false) {
        echo "❌ 仍然是参数校验失败\n";
        
        // 保存错误页面到文件
        file_put_contents('icbc_error.html', $response);
        echo "错误页面已保存到 icbc_error.html\n";
    } elseif (strpos($response, '支付') !== false) {
        echo "✅ 成功到达支付页面\n";
    } else {
        echo "⚠️  未知响应\n";
        echo "响应前200字符: " . substr(strip_tags($response), 0, 200) . "\n";
    }
}

echo "\n测试完成\n"; 