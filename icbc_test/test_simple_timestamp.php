<?php

require_once 'vendor/autoload.php';

use IcbcPay\SDK\IcbcConstants;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🔍 工商银行时间戳问题诊断\n";
    echo "=======================\n\n";
    
    echo "1. 检查当前时间戳生成...\n";
    
    // 检查修复后的时区设置
    echo "   系统时区: " . date_default_timezone_get() . "\n";
    
    echo "   ICBC时区常量: " . IcbcConstants::DATE_TIMEZONE . "\n";
    echo "   ICBC时间格式: " . IcbcConstants::DATE_TIME_FORMAT . "\n";
    
    // 生成时间戳（模拟DefaultIcbcClient的方式）
    $originalTimezone = date_default_timezone_get();
    date_default_timezone_set(IcbcConstants::DATE_TIMEZONE);
    $timestamp = date(IcbcConstants::DATE_TIME_FORMAT);
    date_default_timezone_set($originalTimezone);
    
    echo "   修复后时间戳: " . $timestamp . "\n";
    echo "   格式验证: " . (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $timestamp) ? '✅ 正确' : '❌ 错误') . "\n";
    
    echo "\n2. 获取工行配置...\n";
    $config = config('icbc-pay');
    $appId = $config['app_id'];
    $merId = $config['mer_id'];
    $gateway = $config['gateways'][$config['environment']]['base_url'];
    
    echo "   APP ID: " . ($appId ? '✅ 已配置' : '❌ 未配置') . "\n";
    echo "   商户ID: " . ($merId ? '✅ 已配置' : '❌ 未配置') . "\n";
    echo "   网关URL: " . $gateway . "\n";
    
    echo "\n3. 生成测试请求参数...\n";
    
    // 生成MSG ID（与当前时间戳一致）
    date_default_timezone_set(IcbcConstants::DATE_TIMEZONE);
    $msgId = date('YmdHis') . sprintf('%010d', mt_rand(0, 9999999999));
    date_default_timezone_set($originalTimezone);
    
    echo "   MSG ID: " . $msgId . "\n";
    echo "   MSG ID时间部分: " . substr($msgId, 0, 14) . "\n";
    
    // 转换MSG ID时间为可读格式
    $msgIdTime = substr($msgId, 0, 14);
    $msgIdFormatted = 
        substr($msgIdTime, 0, 4) . '-' . 
        substr($msgIdTime, 4, 2) . '-' . 
        substr($msgIdTime, 6, 2) . ' ' . 
        substr($msgIdTime, 8, 2) . ':' . 
        substr($msgIdTime, 10, 2) . ':' . 
        substr($msgIdTime, 12, 2);
    
    echo "   MSG ID时间: " . $msgIdFormatted . "\n";
    echo "   时间一致性: " . (abs(strtotime($timestamp) - strtotime($msgIdFormatted)) <= 1 ? '✅ 一致' : '❌ 不一致') . "\n";
    
    echo "\n4. 构建测试API请求...\n";
    
    $testParams = [
        'app_id' => $appId,
        'msg_id' => $msgId,
        'format' => 'json',
        'charset' => 'UTF-8',
        'sign_type' => 'RSA2',
        'timestamp' => $timestamp,
        'version' => '1.0.0',
        'biz_content' => json_encode([
            'mer_id' => $merId,
            'out_trade_no' => 'SIMPLE_TEST_' . time(),
            'order_amt' => '0.01',
        ], JSON_UNESCAPED_UNICODE)
    ];
    
    echo "   请求参数:\n";
    foreach ($testParams as $key => $value) {
        if ($key === 'biz_content') {
            echo "     {$key}: " . substr($value, 0, 50) . "...\n";
        } else {
            echo "     {$key}: {$value}\n";
        }
    }
    
    echo "\n5. 时间对比分析...\n";
    
    $now = time();
    echo "   Unix时间戳: " . $now . "\n";
    echo "   系统本地时间: " . date('Y-m-d H:i:s', $now) . "\n";
    echo "   UTC时间: " . gmdate('Y-m-d H:i:s', $now) . "\n";
    echo "   ICBC时间戳: " . $timestamp . "\n";
    
    // 计算与当前时间的差异
    $timestampUnix = strtotime($timestamp);
    $timeDiff = abs($now - $timestampUnix);
    echo "   时间差异: " . $timeDiff . " 秒\n";
    echo "   同步状态: " . ($timeDiff <= 5 ? '✅ 同步正常' : '❌ 时间偏差过大') . "\n";
    
    echo "\n6. 模拟HTTP请求测试...\n";
    
    $testUrl = $gateway . '/api/cardbusiness/aggregatepay/consumepurchase';
    echo "   请求URL: " . $testUrl . "\n";
    
    // 使用curl测试连接性
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($testParams));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'User-Agent: ICBC-PHP-SDK/2.0'
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "   HTTP状态码: " . $httpCode . "\n";
    
    if ($error) {
        echo "   ❌ 网络错误: " . $error . "\n";
    } else {
        echo "   ✅ 网络连接正常\n";
        
        if ($response) {
            echo "   📋 响应内容:\n";
            $responseData = json_decode($response, true);
            if ($responseData) {
                if (isset($responseData['response_biz_content'])) {
                    $bizContent = $responseData['response_biz_content'];
                    echo "     返回码: " . ($bizContent['return_code'] ?? '未知') . "\n";
                    echo "     返回信息: " . ($bizContent['return_msg'] ?? '未知') . "\n";
                    
                    // 分析具体错误
                    if (isset($bizContent['return_code'])) {
                        $returnCode = $bizContent['return_code'];
                        switch ($returnCode) {
                            case '400017':
                                echo "     🔍 错误分析: 签名验证失败（可能是密钥问题）\n";
                                break;
                            case '400011':
                                echo "     🔍 错误分析: 时间戳超时问题\n";
                                echo "     💡 建议: 检查服务器时间同步\n";
                                break;
                            case '400001':
                                echo "     🔍 错误分析: 参数错误\n";
                                break;
                            case '0':
                                echo "     ✅ 请求成功!\n";
                                break;
                            default:
                                echo "     🔍 错误分析: 其他错误 (代码: {$returnCode})\n";
                        }
                    }
                } else {
                    echo "     原始响应: " . substr($response, 0, 200) . "...\n";
                }
            } else {
                echo "     原始响应: " . substr($response, 0, 200) . "...\n";
            }
        }
    }
    
    echo "\n✅ 诊断完成!\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "如果时间戳仍有问题，最可能的原因：\n";
    echo "1. 服务器时间与工行服务器有偏差\n";
    echo "2. 签名问题（不是时间戳本身的问题）\n";
    echo "3. 其他API参数错误\n";
    echo "4. 网络延迟导致请求时间与生成时间不一致\n";
    
} catch (Exception $e) {
    echo "❌ 诊断过程出错: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
} 