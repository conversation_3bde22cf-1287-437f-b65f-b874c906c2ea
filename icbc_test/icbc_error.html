<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>中国工商银行API开放平台</title>
<style type="text/css">
html, body {
	margin: 0;
	font-family: Microsoft YaHei, "Helvetica Neue", Helvetica, Arial,
		sans-serif;
	font-size: 16px;
	color: #333333;
	background-color: #ffffff;
}

div.gw-error-header {
	height: 50px;
	background-color: #A50022;
	width: 100%;
}

div.gw-error-header .gw-errir-title {
	width: 100%;
	text-align: center;
	color: #FFF;
	font-size: 1.5em;
	padding-top: 8px;
}

div.gw-error-body {
	padding: 10px 20px;
}

div.gw-error-footer {
	position: absolute;
	bottom: 0px;
	width: 100%;
	height: 25px;
	background-color: #E3E3E3;
}

div.gw-error-footer .gw-errir-copyright {
	width: 100%;
	text-align: center;
	font-size: 14px;
	padding-top: 2px;
	color: #404040;
}

h1 {
	font-size: 20px;
}

h2 {
	font-size: 18px;
}

.block-btn {
	font-family: Microsoft YaHei, "Helvetica Neue", Helvetica, Arial,
		sans-serif;
	display: inline-block;
	margin: 40px 10px 10px 0px;
	padding: 6px 12px;
	width: 100%;
	font-size: 16px;
	color: #333333;
	text-align: center;
	background-color: #f5f5f5;
	border: 1px solid #cccccc;
}

.block-btn:active, .block-btn:hover {
	color: #333333;
	background-color: #e6e6e6;
	border-color: #adadad;
}

.block-btn:active {
	background-color: #cccccc;
}
</style>
</head>
<body>
	<div class="gw-error-header">
		<div class="gw-errir-title">中国工商银行</div>
	</div>
	<div class="gw-error-body">
		<h1>API开放平台</h1>
		<h2>参数校验失败：</h2>
		<p>参数无效或非法，请检查请求参数。</p>
		<p>
			<button class="block-btn" onclick="javascript:history.back(-1);" >返回</button>
		</p>
	</div>
	<div class="gw-error-footer">
		<div class="gw-errir-copyright">copy right © 中国工商银行</div>
	</div>
</body>
</html>