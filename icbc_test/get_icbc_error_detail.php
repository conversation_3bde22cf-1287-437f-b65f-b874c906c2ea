<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

echo "获取工商银行详细错误信息\n";
echo "==============================\n\n";

try {
    // 创建测试支付记录
    $paymentRecord = PaymentRecord::create([
        'out_trade_no' => 'ERROR_CHECK_' . time() . rand(1000, 9999),
        'total_amount' => 0.01,
        'subject' => '错误检查测试',
        'payment_method' => 'wechat',
        'car_number' => '错误A123',
        'parking_duration' => 15,
        'status' => 'pending'
    ]);

    // 创建支付服务
    $icbcPayService = new IcbcPayService();
    $result = $icbcPayService->createPayment($paymentRecord);

    if ($result['success']) {
        $paymentUrl = $result['payment_url'];
        
        echo "测试支付URL: {$paymentUrl}\n\n";
        
        // 获取完整的错误页面
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $paymentUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "HTTP状态码: {$httpCode}\n";
        
        if ($error) {
            echo "CURL错误: {$error}\n";
        } else {
            echo "\n完整错误页面内容:\n";
            echo str_repeat('=', 80) . "\n";
            echo $response;
            echo "\n" . str_repeat('=', 80) . "\n";
            
            // 提取关键错误信息
            echo "\n关键信息提取:\n";
            echo str_repeat('-', 40) . "\n";
            
            // 查找错误代码
            if (preg_match('/错误代码[:：]\s*([^<\n\r]+)/', $response, $matches)) {
                echo "错误代码: " . trim($matches[1]) . "\n";
            }
            
            // 查找错误信息
            if (preg_match('/错误信息[:：]\s*([^<\n\r]+)/', $response, $matches)) {
                echo "错误信息: " . trim($matches[1]) . "\n";
            }
            
            // 查找错误描述
            if (preg_match('/错误描述[:：]\s*([^<\n\r]+)/', $response, $matches)) {
                echo "错误描述: " . trim($matches[1]) . "\n";
            }
            
            // 查找参数错误信息
            if (preg_match('/参数[:：]\s*([^<\n\r]+)/', $response, $matches)) {
                echo "参数错误: " . trim($matches[1]) . "\n";
            }
            
            // 查找所有包含"错误"的行
            $lines = explode("\n", $response);
            foreach ($lines as $line) {
                $line = trim(strip_tags($line));
                if (strpos($line, '错误') !== false && !empty($line)) {
                    echo "错误行: " . $line . "\n";
                }
            }
            
            // 查找所有包含"失败"的行
            foreach ($lines as $line) {
                $line = trim(strip_tags($line));
                if (strpos($line, '失败') !== false && !empty($line)) {
                    echo "失败行: " . $line . "\n";
                }
            }
            
            // 查找JavaScript中的错误信息
            if (preg_match('/<script[^>]*>(.*?)<\/script>/s', $response, $matches)) {
                $jsContent = $matches[1];
                if (strpos($jsContent, 'error') !== false || strpos($jsContent, '错误') !== false) {
                    echo "JavaScript错误信息:\n";
                    echo $jsContent . "\n";
                }
            }
        }
        
    } else {
        echo "❌ 支付URL生成失败\n";
    }

} catch (Exception $e) {
    echo "❌ 发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n完成\n"; 