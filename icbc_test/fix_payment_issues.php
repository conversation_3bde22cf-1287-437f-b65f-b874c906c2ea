<?php

require_once __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 支付失败问题诊断和修复工具\n";
echo "==============================\n\n";

echo "📋 1. 检查基础配置\n";
echo "=================\n";

$config = config('icbc-pay');
$issues = [];

// 检查必需配置
$requiredKeys = ['app_id', 'merchant_id', 'private_key_path'];
foreach ($requiredKeys as $key) {
    if (empty($config[$key])) {
        echo "❌ {$key}: 未配置\n";
        $issues[] = "{$key} 未配置";
    } else {
        echo "✅ {$key}: " . (strlen($config[$key]) > 50 ? substr($config[$key], 0, 50) . '...' : $config[$key]) . "\n";
    }
}

echo "\n🔐 2. 检查私钥文件\n";
echo "================\n";

$privateKeyPath = $config['private_key_path'];
echo "私钥路径: {$privateKeyPath}\n";

if (!file_exists($privateKeyPath)) {
    echo "❌ 私钥文件不存在！\n";
    $issues[] = "私钥文件不存在";
    
    // 检查是否在其他位置
    $alternativePaths = [
        storage_path('keys/icbc_private.key'),
        storage_path('keys/private_key.pem'),
        __DIR__ . '/storage/keys/icbc_private.key'
    ];
    
    echo "\n🔍 查找其他位置的私钥文件...\n";
    foreach ($alternativePaths as $altPath) {
        if (file_exists($altPath)) {
            echo "✅ 找到私钥文件: {$altPath}\n";
            // 复制到正确位置
            if (copy($altPath, $privateKeyPath)) {
                echo "✅ 已复制到正确位置: {$privateKeyPath}\n";
                chmod($privateKeyPath, 0600);
                echo "✅ 已设置文件权限\n";
            }
            break;
        }
    }
} else {
    echo "✅ 私钥文件存在\n";
    
    // 检查文件权限
    $perms = fileperms($privateKeyPath);
    $permsOctal = substr(sprintf('%o', $perms), -4);
    echo "文件权限: {$permsOctal}\n";
    
    if ($permsOctal !== '0600') {
        echo "⚠️ 建议设置文件权限为 0600\n";
        chmod($privateKeyPath, 0600);
        echo "✅ 已修复文件权限\n";
    }
    
    // 检查文件大小
    $fileSize = filesize($privateKeyPath);
    echo "文件大小: {$fileSize} 字节\n";
    
    if ($fileSize < 100) {
        echo "❌ 文件太小，可能损坏\n";
        $issues[] = "私钥文件可能损坏";
    }
    
    // 测试私钥格式
    $privateKeyContent = file_get_contents($privateKeyPath);
    $privateKey = openssl_pkey_get_private($privateKeyContent);
    
    if ($privateKey) {
        echo "✅ 私钥格式正确\n";
        
        // 测试签名功能
        $testData = 'test_sign_' . time();
        $signature = '';
        if (openssl_sign($testData, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            echo "✅ 私钥签名功能正常\n";
        } else {
            echo "❌ 私钥签名失败\n";
            $issues[] = "私钥签名功能异常";
        }
        
        openssl_pkey_free($privateKey);
    } else {
        echo "❌ 私钥格式错误\n";
        echo "OpenSSL错误: " . openssl_error_string() . "\n";
        $issues[] = "私钥格式错误";
    }
}

echo "\n🚦 3. 检查车牌验证规则\n";
echo "====================\n";

// 测试车牌验证
$testCarNumbers = ['京A12345', '新M88888', '沪B66666', '粤A88888'];
foreach ($testCarNumbers as $carNumber) {
    $isValid = preg_match('/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z0-9]{1}[A-Z0-9]{1,6}[A-Z0-9]{1,6}$/', $carNumber);
    $status = $isValid ? '✅' : '❌';
    echo "{$status} {$carNumber}\n";
}

echo "\n🌐 4. 测试API接口\n";
echo "===============\n";

try {
    $testResponse = file_get_contents('https://icbc.dev.hiwsoft.com/api/pay', false, stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => json_encode([
                'car_number' => '京A12345',
                'amount' => '0.01',
                'payment_method' => 'wechat'
            ]),
            'timeout' => 10
        ]
    ]));
    
    if ($testResponse !== false) {
        $response = json_decode($testResponse, true);
        if (isset($response['success'])) {
            if ($response['success']) {
                echo "✅ API接口正常\n";
            } else {
                echo "❌ API返回错误: " . ($response['error'] ?? 'Unknown error') . "\n";
                $issues[] = "API返回错误";
            }
        } else {
            echo "⚠️ API响应格式异常\n";
        }
    } else {
        echo "❌ API接口连接失败\n";
        $issues[] = "API接口连接失败";
    }
} catch (Exception $e) {
    echo "❌ API测试异常: " . $e->getMessage() . "\n";
    $issues[] = "API测试异常";
}

echo "\n📊 5. 总结报告\n";
echo "=============\n";

if (empty($issues)) {
    echo "🎉 所有检查通过！支付功能应该正常工作。\n";
    echo "\n💡 建议操作：\n";
    echo "1. 清理应用缓存: php artisan cache:clear\n";
    echo "2. 重启服务以确保配置生效\n";
    echo "3. 在生产环境中测试支付功能\n";
} else {
    echo "⚠️ 发现以下问题：\n";
    foreach ($issues as $issue) {
        echo "- {$issue}\n";
    }
    
    echo "\n🔧 建议修复步骤：\n";
    
    if (in_array('私钥文件不存在', $issues) || in_array('私钥格式错误', $issues)) {
        echo "1. 联系工商银行获取正确的私钥文件\n";
        echo "2. 将私钥文件放置到: {$privateKeyPath}\n";
        echo "3. 设置文件权限: chmod 600 {$privateKeyPath}\n";
    }
    
    if (in_array('API接口连接失败', $issues)) {
        echo "4. 检查网络连接和服务器配置\n";
        echo "5. 确认 https://icbc.dev.hiwsoft.com 域名解析正确\n";
    }
    
    echo "6. 运行: php artisan config:clear\n";
    echo "7. 重新测试支付功能\n";
}

echo "\n📝 6. 创建修复补丁\n";
echo "================\n";

// 创建一个自动修复脚本
$fixScript = '#!/bin/bash
echo "应用支付问题修复补丁..."

# 清理缓存
php artisan cache:clear
php artisan config:clear

# 重新生成自动加载
composer dump-autoload

# 设置正确的文件权限
if [ -f "' . $privateKeyPath . '" ]; then
    chmod 600 "' . $privateKeyPath . '"
    echo "✅ 私钥文件权限已修复"
fi

# 检查storage目录权限
chmod -R 755 storage/
chmod -R 755 storage/keys/

echo "✅ 修复补丁应用完成"
';

file_put_contents('fix_payment.sh', $fixScript);
chmod('fix_payment.sh', 0755);
echo "✅ 已创建修复脚本: ./fix_payment.sh\n";

echo "\n🏁 诊断完成！\n";
echo "============\n";
echo "如果问题仍然存在，请检查以下日志文件：\n";
echo "- storage/logs/laravel.log\n";
echo "- /var/log/nginx/error.log (如果使用Nginx)\n";
echo "- /var/log/apache2/error.log (如果使用Apache)\n"; 