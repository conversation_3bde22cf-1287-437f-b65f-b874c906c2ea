<?php

declare(strict_types=1);

/**
 * 调试支付API接口
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Http\Controllers\ParkingController;
use IcbcPay\IcbcPayClient;

echo "=== 调试支付API接口 ===\n\n";

try {
    // 创建Laravel应用
    $app = new Application(realpath(__DIR__));
    $app->singleton(
        Illuminate\Contracts\Http\Kernel::class,
        App\Http\Kernel::class
    );
    $app->singleton(
        Illuminate\Contracts\Console\Kernel::class,
        App\Console\Kernel::class
    );
    $app->singleton(
        Illuminate\Contracts\Debug\ExceptionHandler::class,
        App\Exceptions\Handler::class
    );

    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

    echo "1. Laravel应用初始化成功\n";

    // 模拟支付请求
    $requestData = [
        'car_number' => '京A12345',
        'amount' => '0.01',
        'payment_method' => 'wechat',
        'parking_duration' => '30'
    ];

    echo "2. 模拟支付请求数据:\n";
    foreach ($requestData as $key => $value) {
        echo "   {$key}: {$value}\n";
    }
    echo "\n";

    // 创建ICBC支付客户端
    $config = [
        'app_id' => '11000000000000052474',
        'merchant_id' => 'TEST_MERCHANT_001',
        'environment' => 'sandbox',
        'private_key_path' => __DIR__ . '/storage/keys/icbc_private_key.pem',
        'icbc_public_key_path' => __DIR__ . '/storage/keys/icbc_public_key.pem',
        'notify_url' => 'http://localhost:8000/icbc-pay/notify',
        'return_url' => 'http://localhost:8000/icbc-pay/return',
    ];

    $icbcClient = new IcbcPayClient($config);
    echo "3. ICBC支付客户端创建成功\n";

    // 测试订单创建
    $orderData = [
        'order_id' => 'TEST_ORDER_' . time(),
        'amount' => $requestData['amount'],
        'subject' => '停车费支付',
        'payment_method' => $requestData['payment_method'],
        'body' => "停车费支付 - 车牌：{$requestData['car_number']}",
    ];

    echo "4. 测试支付订单创建...\n";
    $paymentResult = $icbcClient->pay($orderData);
    
    if ($paymentResult['success']) {
        echo "✅ 支付订单创建成功\n";
        echo "   订单号: {$paymentResult['order_id']}\n";
        echo "   支付URL: {$paymentResult['payment_url']}\n";
        echo "   表单长度: " . strlen($paymentResult['form_html']) . " 字符\n\n";
    } else {
        echo "❌ 支付订单创建失败\n\n";
    }

    // 测试查询功能
    echo "5. 测试订单查询...\n";
    $queryResult = $icbcClient->query($orderData['order_id']);
    
    if ($queryResult['success']) {
        echo "✅ 订单查询成功\n";
        echo "   状态: {$queryResult['status']}\n";
        echo "   金额: {$queryResult['amount']}\n\n";
    } else {
        echo "❌ 订单查询失败\n\n";
    }

    // 测试回调处理
    echo "6. 测试回调处理...\n";
    $mockNotifyData = [
        'orderid' => $orderData['order_id'],
        'merordernum' => $orderData['order_id'],
        'payment_amount' => $orderData['amount'],
        'payment_currency' => 'CNY',
        'payment_status' => '1',
        'trade_no' => 'MOCK_TRADE_' . time(),
        'pay_time' => date('Y-m-d H:i:s'),
        'sign' => 'mock_signature_for_testing',
    ];

    $notifyResult = $icbcClient->notify($mockNotifyData);
    
    if ($notifyResult['success']) {
        echo "✅ 回调处理成功\n";
        echo "   订单号: {$notifyResult['order_id']}\n";
        echo "   交易号: {$notifyResult['trade_no']}\n";
        echo "   状态: {$notifyResult['status']}\n\n";
    } else {
        echo "❌ 回调处理失败\n\n";
    }

    echo "=== 调试完成 ===\n";
    echo "✅ 支付API接口功能正常\n";
    echo "✅ ICBC支付客户端工作正常\n";
    echo "\n";

    echo "建议检查事项:\n";
    echo "1. 确保Laravel服务器正在运行\n";
    echo "2. 检查数据库连接是否正常\n";
    echo "3. 验证路由配置是否正确\n";
    echo "4. 查看Laravel日志文件: storage/logs/laravel.log\n";
    echo "\n";

    echo "测试命令:\n";
    echo "curl -X POST http://localhost:8000/api/pay \\\n";
    echo "  -H 'Content-Type: application/json' \\\n";
    echo "  -H 'Accept: application/json' \\\n";
    echo "  -d '{\"car_number\":\"京A12345\",\"amount\":\"0.01\",\"payment_method\":\"wechat\"}'\n";

} catch (Exception $e) {
    echo "❌ 调试失败: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误追踪:\n" . $e->getTraceAsString() . "\n";
}
?> 