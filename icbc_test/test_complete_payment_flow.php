<?php

require_once 'vendor/autoload.php';

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🚀 工商银行支付完整流程测试\n";
    echo "==========================\n\n";
    
    echo "📝 测试目标：\n";
    echo "1. 模拟前端用户输入数据\n";
    echo "2. 调用ParkingController->createPayment()\n";
    echo "3. 跟踪支付表单生成过程\n";
    echo "4. 记录所有关键数据和工行响应\n";
    echo "5. 分析日志找出问题根源\n\n";
    
    // 模拟前端用户输入
    $userInput = [
        'car_number' => '京A12345',
        'amount' => 5.00,
        'payment_method' => 'wechat',
        'parking_duration' => 120
    ];
    
    echo "👤 模拟用户输入数据：\n";
    echo "   车牌号：{$userInput['car_number']}\n";
    echo "   金额：￥{$userInput['amount']}\n";
    echo "   支付方式：{$userInput['payment_method']}\n";
    echo "   停车时长：{$userInput['parking_duration']}分钟\n\n";
    
    // 模拟HTTP请求
    $request = new \Illuminate\Http\Request();
    $request->merge($userInput);
    $request->headers->set('Content-Type', 'application/json');
    $request->headers->set('User-Agent', 'Test-Client/1.0 (Complete Flow Test)');
    $request->headers->set('X-Requested-With', 'XMLHttpRequest');
    $request->server->set('REMOTE_ADDR', '127.0.0.1');
    
    echo "🌐 模拟HTTP请求环境：\n";
    echo "   Content-Type: " . $request->header('Content-Type') . "\n";
    echo "   User-Agent: " . $request->header('User-Agent') . "\n";
    echo "   IP: " . $request->ip() . "\n";
    echo "   Method: POST\n";
    echo "   URL: /api/pay\n\n";
    
    // 清理日志文件以便专注于本次测试
    $logFile = storage_path('logs/laravel.log');
    if (file_exists($logFile)) {
        file_put_contents($logFile, "=== 开始完整支付流程测试 ===\n时间：" . now()->format('Y-m-d H:i:s') . "\n\n");
    }
    
    echo "📋 第一步：调用 ParkingController->createPayment()\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // 创建控制器实例
    $parkingController = app(\App\Http\Controllers\ParkingController::class);
    
    try {
        // 调用创建支付方法
        $response = $parkingController->createPayment($request);
        $responseData = json_decode($response->getContent(), true);
        
        echo "✅ createPayment 调用成功！\n";
        echo "响应状态码：" . $response->getStatusCode() . "\n";
        echo "响应数据：\n";
        echo json_encode($responseData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
        
        if ($responseData['success'] ?? false) {
            $paymentUrl = $responseData['data']['payment_url'] ?? null;
            
            if ($paymentUrl) {
                // 提取订单号
                $orderNo = basename($paymentUrl);
                
                echo "📋 第二步：访问支付页面\n";
                echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
                echo "订单号：{$orderNo}\n";
                echo "支付URL：{$paymentUrl}\n\n";
                
                // 模拟访问支付页面
                $pageRequest = new \Illuminate\Http\Request();
                $pageRequest->headers->set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
                $pageRequest->headers->set('Referer', 'http://localhost/parking');
                $pageRequest->server->set('REMOTE_ADDR', '127.0.0.1');
                
                try {
                    ob_start();
                    $pageResponse = $parkingController->showPaymentPage($orderNo);
                    $pageContent = ob_get_clean();
                    
                    echo "✅ showPaymentPage 调用成功！\n";
                    
                    if ($pageResponse instanceof \Illuminate\View\View) {
                        echo "页面类型：View (正常渲染)\n";
                        
                        // 从view数据中获取支付表单
                        $viewData = $pageResponse->getData();
                        if (isset($viewData['paymentForm'])) {
                            $paymentForm = $viewData['paymentForm'];
                            
                            echo "支付表单长度：" . strlen($paymentForm) . " 字符\n\n";
                            
                            echo "📋 第三步：分析支付表单内容\n";
                            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
                            
                            // 提取关键表单字段
                            $formFields = [];
                            $patterns = [
                                'app_id' => '/name="app_id" value="([^"]+)"/',
                                'msg_id' => '/name="msg_id" value="([^"]+)"/',
                                'timestamp' => '/name="timestamp" value="([^"]+)"/',
                                'sign_type' => '/name="sign_type" value="([^"]+)"/',
                                'version' => '/name="version" value="([^"]+)"/',
                                'biz_content' => '/name="biz_content" value="([^"]+)"/',
                                'sign' => '/name="sign" value="([^"]+)"/',
                            ];
                            
                            foreach ($patterns as $field => $pattern) {
                                if (preg_match($pattern, $paymentForm, $matches)) {
                                    $value = html_entity_decode($matches[1]);
                                    $formFields[$field] = $value;
                                    
                                    if ($field === 'sign') {
                                        echo "🔐 {$field}: " . substr($value, 0, 50) . "... (长度: " . strlen($value) . ")\n";
                                    } elseif ($field === 'biz_content') {
                                        echo "📋 {$field}: ";
                                        $bizData = json_decode($value, true);
                                        if ($bizData) {
                                            echo json_encode($bizData, JSON_UNESCAPED_UNICODE) . "\n";
                                        } else {
                                            echo substr($value, 0, 100) . "...\n";
                                        }
                                    } else {
                                        echo "📊 {$field}: {$value}\n";
                                    }
                                }
                            }
                            
                            echo "\n📋 第四步：时间戳详细分析\n";
                            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
                            
                            if (isset($formFields['timestamp']) && isset($formFields['msg_id'])) {
                                $timestamp = $formFields['timestamp'];
                                $msgId = $formFields['msg_id'];
                                
                                echo "📅 表单时间戳：{$timestamp}\n";
                                echo "🆔 MSG ID：{$msgId}\n";
                                echo "🕐 MSG ID时间部分：" . substr($msgId, 0, 14) . "\n";
                                
                                // 转换MSG ID时间为可读格式
                                $msgIdTime = substr($msgId, 0, 14);
                                $msgIdFormatted = 
                                    substr($msgIdTime, 0, 4) . '-' . 
                                    substr($msgIdTime, 4, 2) . '-' . 
                                    substr($msgIdTime, 6, 2) . ' ' . 
                                    substr($msgIdTime, 8, 2) . ':' . 
                                    substr($msgIdTime, 10, 2) . ':' . 
                                    substr($msgIdTime, 12, 2);
                                
                                echo "📅 MSG ID时间：{$msgIdFormatted}\n";
                                
                                // 当前时间对比
                                $currentTime = now()->format('Y-m-d H:i:s');
                                echo "⏰ 当前时间：{$currentTime}\n";
                                
                                // 计算时间差
                                $timestampDiff = abs(strtotime($timestamp) - time());
                                $msgIdDiff = abs(strtotime($msgIdFormatted) - time());
                                $consistencyDiff = abs(strtotime($timestamp) - strtotime($msgIdFormatted));
                                
                                echo "📊 时间差异分析：\n";
                                echo "   表单时间戳与当前时间差：{$timestampDiff} 秒\n";
                                echo "   MSG ID时间与当前时间差：{$msgIdDiff} 秒\n";
                                echo "   时间戳与MSG ID一致性：{$consistencyDiff} 秒\n";
                                
                                // 验证时间戳格式
                                $formatCheck = preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $timestamp);
                                echo "✅ 时间戳格式验证：" . ($formatCheck ? '符合工行要求' : '格式错误') . "\n";
                                
                                // 检查时区
                                $beijingTime = date('Y-m-d H:i:s');
                                $utcTime = gmdate('Y-m-d H:i:s');
                                echo "🌍 时区信息：\n";
                                echo "   系统时区：" . date_default_timezone_get() . "\n";
                                echo "   系统北京时间：{$beijingTime}\n";
                                echo "   系统UTC时间：{$utcTime}\n";
                                echo "   表单时间戳：{$timestamp}\n";
                            }
                            
                            echo "\n📋 第五步：模拟提交到工行服务器\n";
                            echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
                            
                            // 提取表单action URL
                            if (preg_match('/action="([^"]+)"/', $paymentForm, $matches)) {
                                $actionUrl = html_entity_decode($matches[1]);
                                echo "🌐 提交URL：{$actionUrl}\n";
                                
                                // 准备POST数据
                                $postData = [];
                                foreach ($formFields as $key => $value) {
                                    $postData[$key] = $value;
                                }
                                
                                echo "📤 POST数据字段数量：" . count($postData) . "\n";
                                echo "📊 POST数据大小：" . strlen(http_build_query($postData)) . " 字节\n\n";
                                
                                // 尝试发送HTTP请求
                                echo "🔄 正在发送HTTP请求到工行服务器...\n";
                                
                                $ch = curl_init();
                                curl_setopt($ch, CURLOPT_URL, $actionUrl);
                                curl_setopt($ch, CURLOPT_POST, true);
                                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
                                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
                                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                                    'Content-Type: application/x-www-form-urlencoded',
                                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                                    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                                ]);
                                
                                $response = curl_exec($ch);
                                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                $error = curl_error($ch);
                                $info = curl_getinfo($ch);
                                curl_close($ch);
                                
                                echo "📊 HTTP响应状态码：{$httpCode}\n";
                                echo "⏱️ 请求耗时：" . round($info['total_time'], 3) . " 秒\n";
                                echo "🌐 连接耗时：" . round($info['connect_time'], 3) . " 秒\n";
                                
                                if ($error) {
                                    echo "❌ CURL错误：{$error}\n";
                                } else {
                                    echo "✅ 网络请求成功\n";
                                    
                                    if ($response) {
                                        echo "📋 响应内容长度：" . strlen($response) . " 字节\n";
                                        
                                        // 尝试解析响应
                                        if ($httpCode >= 200 && $httpCode < 300) {
                                            echo "✅ HTTP状态正常\n";
                                            
                                            // 检查是否是JSON响应
                                            $jsonResponse = json_decode($response, true);
                                            if ($jsonResponse) {
                                                echo "📋 JSON响应解析成功：\n";
                                                echo json_encode($jsonResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
                                                
                                                // 分析工行响应
                                                if (isset($jsonResponse['return_code'])) {
                                                    $returnCode = $jsonResponse['return_code'];
                                                    $returnMsg = $jsonResponse['return_msg'] ?? '';
                                                    
                                                    echo "🏦 工行响应分析：\n";
                                                    echo "   返回码：{$returnCode}\n";
                                                    echo "   返回信息：{$returnMsg}\n";
                                                    
                                                    switch ($returnCode) {
                                                        case '0':
                                                            echo "   ✅ 状态：成功！\n";
                                                            break;
                                                        case '400017':
                                                            echo "   ❌ 状态：签名验证失败\n";
                                                            echo "   💡 可能原因：\n";
                                                            echo "      - 时间戳格式问题（已修复）\n";
                                                            echo "      - 签名算法问题\n";
                                                            echo "      - 私钥配置问题\n";
                                                            echo "      - 参数编码问题\n";
                                                            break;
                                                        case '400011':
                                                            echo "   ❌ 状态：请求超时或时间戳问题\n";
                                                            echo "   💡 可能原因：\n";
                                                            echo "      - 服务器时间不同步\n";
                                                            echo "      - 时间戳格式错误\n";
                                                            break;
                                                        default:
                                                            echo "   ❓ 状态：其他错误 (代码: {$returnCode})\n";
                                                    }
                                                }
                                            } else {
                                                echo "📋 HTML/其他格式响应，前200字符：\n";
                                                echo substr($response, 0, 200) . "...\n";
                                            }
                                        } else {
                                            echo "❌ HTTP错误状态：{$httpCode}\n";
                                            echo "📋 错误响应前200字符：\n";
                                            echo substr($response, 0, 200) . "...\n";
                                        }
                                    }
                                }
                            }
                            
                        } else {
                            echo "❌ 未找到支付表单数据\n";
                        }
                    } else {
                        echo "页面响应类型：" . get_class($pageResponse) . "\n";
                    }
                    
                } catch (Exception $e) {
                    echo "❌ showPaymentPage 调用失败：\n";
                    echo "错误：" . $e->getMessage() . "\n";
                    echo "文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
                }
            } else {
                echo "❌ 响应中未找到支付URL\n";
            }
        } else {
            echo "❌ createPayment 失败：\n";
            echo "错误：" . ($responseData['error'] ?? '未知错误') . "\n";
            echo "消息：" . ($responseData['message'] ?? '无') . "\n";
        }
        
    } catch (Exception $e) {
        echo "❌ createPayment 调用异常：\n";
        echo "错误：" . $e->getMessage() . "\n";
        echo "文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
        echo "堆栈：\n" . $e->getTraceAsString() . "\n";
    }
    
    echo "\n📋 第六步：查看详细日志\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // 读取日志文件
    if (file_exists($logFile)) {
        $logContent = file_get_contents($logFile);
        $lines = explode("\n", $logContent);
        $recentLines = array_slice($lines, -50); // 最近50行
        
        echo "📊 最近50行日志：\n";
        foreach ($recentLines as $line) {
            if (trim($line)) {
                echo $line . "\n";
            }
        }
    } else {
        echo "❌ 日志文件不存在\n";
    }
    
    echo "\n🎯 测试总结\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "✅ 测试完成！请检查以上日志输出以识别问题根源。\n";
    echo "📋 重点关注项：\n";
    echo "1. 时间戳格式是否为 'Y-m-d H:i:s'\n";
    echo "2. MSG ID与时间戳是否一致\n";
    echo "3. 工行服务器的具体响应\n";
    echo "4. 详细的错误日志信息\n";
    
} catch (Exception $e) {
    echo "❌ 测试脚本执行失败：\n";
    echo "错误：" . $e->getMessage() . "\n";
    echo "文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
} 