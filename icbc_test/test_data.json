{"order_no": "PARAM_FIX_20250523165021602", "payment_url": "https://gw.open.icbc.com.cn/ui/cardbusiness/aggregatepay/b2c/online/ui/consumepurchaseshowpay/V1?app_id=11000000000000052474&biz_content=%7B%22mer_id%22%3A%22301055420003%22%2C%22mer_prtcl_no%22%3A%223010554200030201%22%2C%22out_trade_no%22%3A%22PARAM_FIX_20250523165021602%22%2C%22orig_date_time%22%3A%222025-05-23+16%3A50%3A22%22%2C%22total_fee%22%3A%221%22%2C%22body%22%3A%22%E5%81%9C%E8%BD%A6%E8%B4%B9%E6%94%AF%E4%BB%98%22%2C%22mer_url%22%3A%22https%3A%2F%2Ficbc.dev.hiwsoft.com%2Ficbc-pay%2Fnotify%22%2C%22spbill_create_ip%22%3A%22************%22%2C%22fee_type%22%3A%22001%22%2C%22pay_mode%22%3A%229%22%2C%22access_type%22%3A%221%22%2C%22device_info%22%3A%22WEB%22%2C%22notify_type%22%3A%22HS%22%2C%22result_type%22%3A%220%22%7D&charset=UTF-8&format=json&sign_type=RSA2&timestamp=2025-05-23+16%3A50%3A22&version=V1&sign=EhGfPQK312GXpIfkEzfNl1Jvg2Rhh1bcnpVyuYa8ovt8xMKQYwZRuOGcKh3wdDlpz9hyQfYn531qFfwOpONWu9%2FbtisLF0xS1dITeevlCFPduAxkX%2FKD5UtYQPrei%2F%2B%2BDjj6SfDP%2Bx9Sk2iDDKYMyAluLCDByl%2BVQx4%2Fe4OUZ%2BsOakVhQFc%2F3XMMjUdEgOpOcqnkoBYSlOIo9Xso3h3W%2BSNvg1eUITNJ8hNJyWl1CUQJBZ8uVxnijCL8ckLA%2B9WJI55WH1pcLvQkQgZ6%2BWwubb9L0tPBYwKnSZwuB1wGw9KPKhbau7u4I0yxmrAiwN9oBgXPofT1SOgWZOk2ikRj5A%3D%3D", "biz_content": {"mer_id": "301055420003", "mer_prtcl_no": "3010554200030201", "out_trade_no": "PARAM_FIX_20250523165021602", "orig_date_time": "2025-05-23 16:50:22", "total_fee": "1", "body": "停车费支付", "mer_url": "https://icbc.dev.hiwsoft.com/icbc-pay/notify", "spbill_create_ip": "************", "fee_type": "001", "pay_mode": "9", "access_type": "1", "device_info": "WEB", "notify_type": "HS", "result_type": "0"}, "request_params": {"app_id": "11000000000000052474", "biz_content": "{\"mer_id\":\"301055420003\",\"mer_prtcl_no\":\"3010554200030201\",\"out_trade_no\":\"PARAM_FIX_20250523165021602\",\"orig_date_time\":\"2025-05-23 16:50:22\",\"total_fee\":\"1\",\"body\":\"停车费支付\",\"mer_url\":\"https://icbc.dev.hiwsoft.com/icbc-pay/notify\",\"spbill_create_ip\":\"************\",\"fee_type\":\"001\",\"pay_mode\":\"9\",\"access_type\":\"1\",\"device_info\":\"WEB\",\"notify_type\":\"HS\",\"result_type\":\"0\"}", "charset": "UTF-8", "format": "json", "sign_type": "RSA2", "timestamp": "2025-05-23 16:50:22", "version": "V1", "sign": "EhGfPQK312GXpIfkEzfNl1Jvg2Rhh1bcnpVyuYa8ovt8xMKQYwZRuOGcKh3wdDlpz9hyQfYn531qFfwOpONWu9/btisLF0xS1dITeevlCFPduAxkX/KD5UtYQPrei/++Djj6SfDP+x9Sk2iDDKYMyAluLCDByl+VQx4/e4OUZ+sOakVhQFc/3XMMjUdEgOpOcqnkoBYSlOIo9Xso3h3W+SNvg1eUITNJ8hNJyWl1CUQJBZ8uVxnijCL8ckLA+9WJI55WH1pcLvQkQgZ6+Wwubb9L0tPBYwKnSZwuB1wGw9KPKhbau7u4I0yxmrAiwN9oBgXPofT1SOgWZOk2ikRj5A=="}, "server_ip": "************", "timestamp": "2025-05-23 16:50:22", "http_code": 200, "success": false}