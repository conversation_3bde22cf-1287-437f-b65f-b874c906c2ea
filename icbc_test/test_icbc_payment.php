<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;
use IcbcPay\IcbcPayClient;

// 创建一个新的支付记录
$paymentRecord = PaymentRecord::create([
    'out_trade_no' => 'TEST_PAYMENT_' . time() . rand(1000, 9999),
    'total_amount' => 0.01, // 1分钱测试
    'subject' => '停车费支付测试',
    'payment_method' => 'wechat',
    'car_number' => '测试A88888',
    'parking_duration' => 30,
    'status' => 'pending'
]);

echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <title>工商银行支付测试</title>\n";
echo "    <style>\n";
echo "        body { font-family: Arial, sans-serif; padding: 20px; }\n";
echo "        .info { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }\n";
echo "        .success { background: #d4edda; border: 1px solid #c3e6cb; }\n";
echo "        .error { background: #f8d7da; border: 1px solid #f5c6cb; }\n";
echo "        .payment-form { margin-top: 20px; }\n";
echo "    </style>\n";
echo "</head>\n";
echo "<body>\n";
echo "    <h1>工商银行支付测试页面</h1>\n";

echo "    <div class='info'>\n";
echo "        <h3>支付订单信息</h3>\n";
echo "        <p><strong>订单号:</strong> " . $paymentRecord->out_trade_no . "</p>\n";
echo "        <p><strong>支付金额:</strong> ¥" . number_format($paymentRecord->total_amount, 2) . "</p>\n";
echo "        <p><strong>车牌号:</strong> " . $paymentRecord->car_number . "</p>\n";
echo "        <p><strong>停车时长:</strong> " . $paymentRecord->parking_duration . " 分钟</p>\n";
echo "    </div>\n";

try {
    // 获取工商银行支付配置
    $config = config('icbc-pay');
    if (empty($config)) {
        throw new Exception('工商银行支付配置未找到，请检查 config/icbc-pay.php 文件');
    }
    
    echo "    <div class='info'>\n";
    echo "        <h3>📋 配置检查</h3>\n";
    echo "        <p><strong>APP_ID:</strong> " . ($config['app_id'] ? '✅ 已配置' : '❌ 未配置') . "</p>\n";
    echo "        <p><strong>商户号:</strong> " . ($config['mer_id'] ? '✅ 已配置' : '❌ 未配置') . "</p>\n";
    echo "        <p><strong>私钥文件:</strong> " . (file_exists($config['private_key_path']) ? '✅ 存在' : '❌ 不存在') . "</p>\n";
    echo "        <p><strong>签名类型:</strong> " . ($config['sign_type'] ?? 'RSA2') . "</p>\n";
    echo "    </div>\n";

    // 使用 IcbcPayClient 替代 IcbcPayService（因为构造函数问题）
    $icbcClient = app(IcbcPayClient::class);
    
    // 准备支付数据
    $orderData = [
        'order_id' => $paymentRecord->out_trade_no,
        'amount' => $paymentRecord->total_amount,
        'subject' => $paymentRecord->subject,
        'body' => '停车费支付：' . $paymentRecord->car_number . '，时长：' . $paymentRecord->parking_duration . '分钟',
        'payment_method' => $paymentRecord->payment_method,
    ];
    
    echo "    <div class='info'>\n";
    echo "        <h3>🔧 生成支付表单</h3>\n";
    echo "        <p>正在生成工商银行支付表单...</p>\n";
    echo "    </div>\n";
    
    // 生成支付表单
    $paymentForm = $icbcClient->buildForm($orderData);
    
    if (!empty($paymentForm)) {
        echo "    <div class='info success'>\n";
        echo "        <h3>✅ 支付表单生成成功</h3>\n";
        echo "        <p><strong>表单长度:</strong> " . strlen($paymentForm) . " 字符</p>\n";
        echo "        <p><strong>包含字段数:</strong> " . substr_count($paymentForm, 'name=') . " 个</p>\n";
        
        // 提取一些关键信息用于显示
        if (preg_match('/name="app_id" value="([^"]*)"/', $paymentForm, $matches)) {
            echo "        <p><strong>APP_ID:</strong> " . htmlspecialchars($matches[1]) . "</p>\n";
        }
        if (preg_match('/name="msg_id" value="([^"]*)"/', $paymentForm, $matches)) {
            echo "        <p><strong>MSG_ID:</strong> " . htmlspecialchars($matches[1]) . "</p>\n";
        }
        if (preg_match('/name="sign" value="([^"]*)"/', $paymentForm, $matches)) {
            echo "        <p><strong>签名:</strong> " . htmlspecialchars(substr($matches[1], 0, 50)) . "...</p>\n";
        }
        echo "    </div>\n";
        
        echo "    <div class='payment-form'>\n";
        echo "        <h3>🏦 工商银行支付</h3>\n";
        echo "        <p>点击下方按钮将跳转到工商银行支付页面:</p>\n";
        echo "        <div style='border: 1px solid #ddd; padding: 15px; background: #fff; border-radius: 5px;'>\n";
        
        // 输出支付表单并自动提交
        echo $paymentForm;
        
        echo "        </div>\n";
        echo "        <p style='color: #666; font-size: 12px; margin-top: 10px;'>💡 支付表单将自动提交到工商银行支付网关</p>\n";
        echo "    </div>\n";
        
        echo "    <div class='info'>\n";
        echo "        <h3>📄 表单源码</h3>\n";
        echo "        <p>以下是生成的支付表单HTML源码：</p>\n";
        echo "        <textarea style='width: 100%; height: 150px; font-family: monospace; font-size: 12px;'>" . htmlspecialchars($paymentForm) . "</textarea>\n";
        echo "    </div>\n";
        
    } else {
        echo "    <div class='info error'>\n";
        echo "        <h3>❌ 支付表单生成失败</h3>\n";
        echo "        <p>表单内容为空，请检查配置和密钥文件。</p>\n";
        echo "    </div>\n";
    }
    
} catch (Exception $e) {
    echo "    <div class='info error'>\n";
    echo "        <h3>❌ 发生错误</h3>\n";
    echo "        <p><strong>错误信息:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "        <p><strong>错误文件:</strong> " . htmlspecialchars($e->getFile()) . ":" . $e->getLine() . "</p>\n";
    
    // 提供故障排除建议
    echo "        <h4>🔧 故障排除建议：</h4>\n";
    echo "        <ul>\n";
    echo "            <li>运行配置检查：<code>php validate_icbc_config.php</code></li>\n";
    echo "            <li>运行签名修复：<code>php fix_sign_verify_failed.php</code></li>\n";
    echo "            <li>检查密钥文件：<code>storage/keys/icbc_private_key.pem</code></li>\n";
    echo "            <li>验证.env配置：<code>ICBC_APP_ID, ICBC_MER_ID, ICBC_MER_PRTCL_NO</code></li>\n";
    echo "        </ul>\n";
    echo "    </div>\n";
}

echo "    <div class='info'>\n";
echo "        <h3>🛠️ 测试工具</h3>\n";
echo "        <p>如果支付测试失败，可以使用以下命令进行诊断：</p>\n";
echo "        <ul>\n";
echo "            <li><strong>签名验证修复：</strong> <code>php fix_sign_verify_failed.php</code></li>\n";
echo "            <li><strong>配置验证：</strong> <code>php validate_icbc_config.php</code></li>\n";
echo "            <li><strong>密钥测试：</strong> <code>php setup_test_keys.php</code></li>\n";
echo "        </ul>\n";
echo "    </div>\n";

echo "</body>\n";
echo "</html>\n"; 