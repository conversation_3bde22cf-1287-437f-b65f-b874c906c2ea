<?php

require_once '../vendor/autoload.php';

try {
    $app = require_once '../bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🚦 工商银行支付频率限制状态检查\n";
    echo "=====================================\n\n";
    
    // 获取当前状态
    $status = \App\Services\IcbcRateLimiter::getStatus();
    
    echo "📊 当前限制状态：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // 总体状态
    if ($status['can_make_payment']) {
        echo "✅ 状态：可以发起支付请求\n";
    } else {
        echo "❌ 状态：暂时无法发起支付请求\n";
    }
    
    echo "\n";
    
    // 基本间隔限制
    echo "🕐 基本间隔限制（5分钟）：\n";
    if ($status['last_payment_time']) {
        echo "   最后支付时间：{$status['last_payment_time']}\n";
        echo "   距离上次支付：{$status['time_since_last']} 秒\n";
        echo "   剩余等待时间：{$status['basic_limit']['remaining_seconds']} 秒\n";
        
        if ($status['basic_limit']['remaining_seconds'] > 0) {
            $nextTime = time() + $status['basic_limit']['remaining_seconds'];
            echo "   下次可用时间：" . date('Y-m-d H:i:s', $nextTime) . "\n";
        } else {
            echo "   ✅ 基本间隔限制已满足\n";
        }
    } else {
        echo "   ✅ 尚未有支付记录，可以立即发起\n";
    }
    
    echo "\n";
    
    // 突发限制
    echo "⚡ 突发限制（1分钟窗口）：\n";
    echo "   当前窗口请求数：{$status['burst_limit']['current_count']}/{$status['burst_limit']['max_requests']}\n";
    echo "   窗口剩余时间：{$status['burst_limit']['window_remaining']} 秒\n";
    
    if ($status['burst_limit']['current_count'] >= $status['burst_limit']['max_requests']) {
        $nextWindow = time() + $status['burst_limit']['window_remaining'];
        echo "   ❌ 突发限制已达上限\n";
        echo "   下个窗口开始：" . date('Y-m-d H:i:s', $nextWindow) . "\n";
    } else {
        echo "   ✅ 突发限制正常\n";
    }
    
    echo "\n";
    
    // 每日限制
    echo "📅 每日限制：\n";
    echo "   今日请求数：{$status['daily_limit']['current_count']}/{$status['daily_limit']['max_requests']}\n";
    echo "   剩余请求数：{$status['daily_limit']['remaining_requests']}\n";
    echo "   日期：{$status['daily_limit']['date']}\n";
    
    if ($status['daily_limit']['current_count'] >= $status['daily_limit']['max_requests']) {
        echo "   ❌ 每日限制已达上限\n";
        echo "   明日重置时间：" . date('Y-m-d 00:00:00', strtotime('tomorrow')) . "\n";
    } else {
        echo "   ✅ 每日限制正常\n";
    }
    
    echo "\n";
    
    // 下次可用时间
    if ($status['next_available']) {
        echo "⏰ 下次可用时间信息：\n";
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
        echo "   下次可用时间：{$status['next_available']['next_time_formatted']}\n";
        echo "   需要等待：{$status['next_available']['wait_seconds']} 秒\n";
        echo "   等待分钟：" . round($status['next_available']['wait_seconds'] / 60, 1) . " 分钟\n";
        echo "   限制原因：" . implode(', ', $status['next_available']['reasons']) . "\n";
        
        if ($status['next_available']['current_limits']['last_payment']) {
            echo "   上次支付：{$status['next_available']['current_limits']['last_payment']}\n";
        }
    } else {
        echo "✅ 无限制，可以立即发起支付请求\n";
    }
    
    echo "\n";
    
    // 工行并发错误分析
    echo "🏦 工商银行并发错误分析：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "最新错误：500032 - concurrency out of range\n";
    echo "资源ID：10000000000000010847\n";
    echo "错误含义：并发量超出工行API限制范围\n";
    echo "修复状态：✅ 签名验证已通过（不再出现400017错误）\n";
    echo "当前问题：工行检测到频繁请求，触发了并发保护机制\n\n";
    
    // 建议操作
    echo "💡 建议操作：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    if ($status['can_make_payment']) {
        echo "✅ 1. 当前可以测试支付，但建议谨慎进行\n";
        echo "✅ 2. 如果测试，请确保使用不同的订单号\n";
        echo "⚠️  3. 测试后至少等待5分钟再进行下次测试\n";
        echo "📝 4. 观察工行响应，记录是否还有并发错误\n";
    } else {
        $waitMinutes = round($status['next_available']['wait_seconds'] / 60, 1);
        echo "⏰ 1. 请等待 {$waitMinutes} 分钟后再进行测试\n";
        echo "☕ 2. 利用等待时间完善支付流程其他功能\n";
        echo "🔧 3. 部署频率限制机制到生产环境\n";
        echo "📊 4. 准备监控和日志系统\n";
    }
    
    echo "\n📈 重大进展总结：\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    echo "✅ 签名验证问题已彻底解决\n";
    echo "✅ 时间戳格式、时区、编码等技术问题已修复\n";
    echo "✅ 工行能够正确验证我们的签名\n";
    echo "🔄 当前需要解决并发限制问题\n";
    echo "🎯 解决并发限制后，工行支付将完全可用\n\n";
    
    // 如果有重置选项
    if (isset($argv[1]) && $argv[1] === '--reset') {
        echo "🔄 正在重置频率限制...\n";
        \App\Services\IcbcRateLimiter::reset();
        echo "✅ 频率限制已重置，现在可以立即测试\n";
        echo "⚠️  注意：仅用于开发测试，生产环境请勿随意重置\n";
    } else {
        echo "💡 提示：如需重置频率限制进行测试，运行：php test_rate_limiter_status.php --reset\n";
    }
    
} catch (Exception $e) {
    echo "❌ 检查失败：\n";
    echo "错误：" . $e->getMessage() . "\n";
    echo "文件：" . $e->getFile() . ":" . $e->getLine() . "\n";
} 