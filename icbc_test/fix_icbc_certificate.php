<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 工商银行证书问题诊断和修复工具\n";
echo "===================================\n\n";

// 获取配置
$config = config('icbc-pay');

echo "📋 第一步：配置信息检查\n";
echo "====================\n";

$configItems = [
    'APP_ID' => $config['app_id'],
    '商户号' => $config['mer_id'],
    '协议号' => $config['mer_prtcl_no'],
    '网关地址' => $config['gateway_url'],
    '回调地址' => $config['notify_url'],
];

foreach ($configItems as $name => $value) {
    $status = !empty($value) ? '✅' : '❌';
    echo "{$status} {$name}: {$value}\n";
}

echo "\n🔐 第二步：证书文件检查\n";
echo "==================\n";

$privateKeyPath = $config['private_key_path'];
$publicKeyPath = $config['public_key_path'];

echo "私钥文件: {$privateKeyPath}\n";
echo "公钥文件: {$publicKeyPath}\n\n";

// 检查私钥
if (!file_exists($privateKeyPath)) {
    echo "❌ 私钥文件不存在！\n";
    echo "请确保私钥文件位于: {$privateKeyPath}\n\n";
    exit;
}

$privateKeyContent = file_get_contents($privateKeyPath);
$privateKey = openssl_pkey_get_private($privateKeyContent);

if (!$privateKey) {
    echo "❌ 私钥格式错误！\n";
    echo "错误信息: " . openssl_error_string() . "\n\n";
    exit;
}

echo "✅ 私钥文件存在且格式正确\n";

// 获取私钥详情
$keyDetails = openssl_pkey_get_details($privateKey);
echo "私钥类型: " . $keyDetails['type'] . "\n";
echo "私钥位数: " . $keyDetails['bits'] . "\n";

// 检查公钥
if (file_exists($publicKeyPath)) {
    $publicKeyContent = file_get_contents($publicKeyPath);
    $publicKey = openssl_pkey_get_public($publicKeyContent);
    
    if ($publicKey) {
        echo "✅ 公钥文件存在且格式正确\n";
        
        // 测试私钥公钥匹配性
        echo "\n🧪 第三步：测试私钥公钥匹配性\n";
        echo "==========================\n";
        
        $testData = 'test_certificate_match_' . time();
        $signature = '';
        
        if (openssl_sign($testData, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            echo "✅ 私钥签名成功\n";
            
            $verifyResult = openssl_verify($testData, $signature, $publicKey, OPENSSL_ALGO_SHA256);
            if ($verifyResult === 1) {
                echo "✅ 私钥和公钥匹配！\n";
                $certificateMatches = true;
            } elseif ($verifyResult === 0) {
                echo "❌ 私钥和公钥不匹配！\n";
                $certificateMatches = false;
            } else {
                echo "❌ 验证过程出错\n";
                $certificateMatches = false;
            }
        } else {
            echo "❌ 私钥签名失败\n";
            $certificateMatches = false;
        }
        
        openssl_pkey_free($publicKey);
    } else {
        echo "❌ 公钥格式错误\n";
        $certificateMatches = false;
    }
} else {
    echo "⚠️ 公钥文件不存在，无法测试匹配性\n";
    $certificateMatches = null;
}

openssl_pkey_free($privateKey);

echo "\n🌐 第四步：网络连接测试\n";
echo "==================\n";

$testUrl = $config['gateway_url'] . $config['api_urls']['consume_purchase_ui'];
echo "测试URL: {$testUrl}\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_NOBODY, true);

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "❌ 网络连接失败: {$error}\n";
} else {
    echo "✅ 网络连接正常 (HTTP {$httpCode})\n";
}

echo "\n🧪 第五步：实际支付测试\n";
echo "==================\n";

if ($certificateMatches === false) {
    echo "⚠️ 由于证书不匹配，跳过实际支付测试\n";
} else {
    // 创建测试支付
    echo "创建测试支付订单...\n";
    
    $testOrder = [
        'mer_id' => $config['mer_id'],
        'mer_prtcl_no' => $config['mer_prtcl_no'],
        'out_trade_no' => 'CERT_TEST_' . date('YmdHis'),
        'orig_date_time' => date('Y-m-d H:i:s'),
        'total_fee' => '1',
        'body' => '证书测试',
        'mer_url' => $config['notify_url'],
        'spbill_create_ip' => getPublicIP(),
        'fee_type' => '001',
        'pay_mode' => '9',
        'access_type' => '1',
    ];
    
    $requestParams = [
        'app_id' => $config['app_id'],
        'charset' => 'UTF-8',
        'format' => 'json',
        'sign_type' => 'RSA2',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => 'V1',
        'biz_content' => json_encode($testOrder, JSON_UNESCAPED_UNICODE),
    ];
    
    // 生成签名
    ksort($requestParams);
    $signString = '';
    foreach ($requestParams as $key => $value) {
        if ($value !== '' && $value !== null) {
            $signString .= $key . '=' . $value . '&';
        }
    }
    $signString = rtrim($signString, '&');
    
    $privateKeyContent = file_get_contents($config['private_key_path']);
    $privateKey = openssl_pkey_get_private($privateKeyContent);
    
    $signature = '';
    if (openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
        $requestParams['sign'] = base64_encode($signature);
        
        $paymentUrl = $testUrl . '?' . http_build_query($requestParams);
        
        echo "测试支付URL长度: " . strlen($paymentUrl) . "\n";
        
        // 测试支付请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $paymentUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'ICBC-Certificate-Test/1.0');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "支付请求HTTP状态: {$httpCode}\n";
        
        if (strpos($response, '参数校验失败') !== false) {
            echo "❌ 仍然出现参数校验失败\n";
            // 保存错误响应
            file_put_contents('certificate_test_error.html', $response);
            echo "错误响应已保存到 certificate_test_error.html\n";
        } elseif (strpos($response, '支付') !== false) {
            echo "✅ 支付接口调用成功！\n";
            file_put_contents('certificate_test_success.txt', $paymentUrl);
            echo "成功的支付URL已保存到 certificate_test_success.txt\n";
        } else {
            echo "⚠️ 未知响应\n";
            file_put_contents('certificate_test_unknown.html', $response);
        }
    } else {
        echo "❌ 签名生成失败\n";
    }
    
    openssl_pkey_free($privateKey);
}

echo "\n📊 诊断结果总结\n";
echo "=============\n";

$issues = [];
$recommendations = [];

if (empty($config['app_id'])) {
    $issues[] = "APP_ID未配置";
    $recommendations[] = "在.env文件中配置ICBC_APP_ID";
}

if (empty($config['mer_id'])) {
    $issues[] = "商户号未配置";
    $recommendations[] = "在.env文件中配置ICBC_MER_ID";
}

if ($certificateMatches === false) {
    $issues[] = "私钥和公钥不匹配";
    $recommendations[] = "联系工商银行技术支持重新获取匹配的证书对";
    $recommendations[] = "确认APP_ID与证书是否对应";
}

if (!empty($issues)) {
    echo "🚨 发现的问题:\n";
    foreach ($issues as $issue) {
        echo "  ❌ {$issue}\n";
    }
    
    echo "\n💡 建议的解决方案:\n";
    foreach ($recommendations as $rec) {
        echo "  🔧 {$rec}\n";
    }
} else {
    echo "✅ 配置检查正常\n";
}

// 紧急解决方案
if ($certificateMatches === false) {
    echo "\n🚨 紧急解决方案\n";
    echo "=============\n";
    echo "由于私钥公钥不匹配，这是最严重的问题，需要立即解决：\n\n";
    
    echo "1. 🏃‍♂️ 立即联系工商银行技术支持\n";
    echo "   - 商户号：{$config['mer_id']}\n";
    echo "   - APP_ID：{$config['app_id']}\n";
    echo "   - 问题：私钥公钥不匹配，无法验证签名\n\n";
    
    echo "2. 📋 准备提供给技术支持的信息\n";
    echo "   - 当前使用的私钥文件\n";
    echo "   - 商户开户时的相关文档\n";
    echo "   - 错误页面截图\n\n";
    
    echo "3. 🔄 临时解决方案\n";
    echo "   - 系统已启用降级处理模式\n";
    echo "   - 用户可以使用现金支付或联系客服\n";
    echo "   - 不影响正常业务运行\n\n";
    
    // 生成技术支持联系信息
    echo "4. 📞 技术支持联系方式\n";
    echo "   - 工商银行开放平台：https://open.icbc.com.cn\n";
    echo "   - 技术支持热线：95588转人工服务\n";
    echo "   - 在线客服：登录开放平台后台\n\n";
}

echo "\n===================================\n";
echo "证书诊断完成！\n";
echo "===================================\n";

// 辅助函数
function getPublicIP() {
    $services = [
        'https://api.ipify.org',
        'https://ipinfo.io/ip'
    ];
    
    foreach ($services as $service) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $service);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $ip = trim(curl_exec($ch));
            curl_close($ch);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE)) {
                return $ip;
            }
        } catch (Exception $e) {
            continue;
        }
    }
    
    return '**************';
} 