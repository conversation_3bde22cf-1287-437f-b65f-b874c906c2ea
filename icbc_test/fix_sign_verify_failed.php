<?php

require_once 'vendor/autoload.php';

use IcbcPay\SDK\IcbcSignature;
use IcbcPay\SDK\IcbcConstants;
use IcbcPay\SDK\WebUtils;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🔐 工商银行支付签名验证失败修复工具\n";
    echo "========================================\n\n";
    
    echo "📋 错误分析：\n";
    echo "错误码: 400017\n";
    echo "错误信息: sign verify failed\n";
    echo "原因: 签名生成或验证过程中出现问题\n\n";
    
    // 1. 检查配置文件
    echo "🔍 第一步：检查配置文件\n";
    echo "========================\n";
    
    $config = config('icbc-pay');
    if (empty($config)) {
        echo "❌ 配置文件不存在或为空\n";
        exit(1);
    }
    
    echo "✅ 配置文件存在\n";
    
    // 检查必要的配置项
    $requiredConfigs = ['app_id', 'mer_id', 'mer_prtcl_no', 'private_key_path'];
    $missingConfigs = [];
    
    foreach ($requiredConfigs as $configKey) {
        if (empty($config[$configKey])) {
            $missingConfigs[] = $configKey;
        }
    }
    
    if (!empty($missingConfigs)) {
        echo "❌ 缺少必要配置项: " . implode(', ', $missingConfigs) . "\n";
        echo "请在 .env 文件中配置相应的环境变量\n\n";
    } else {
        echo "✅ 必要配置项完整\n";
    }
    
    // 2. 检查密钥文件
    echo "\n🔑 第二步：检查密钥文件\n";
    echo "=======================\n";
    
    $privateKeyPath = $config['private_key_path'];
    $publicKeyPath = $config['icbc_public_key_path'] ?? '';
    
    echo "私钥路径: {$privateKeyPath}\n";
    echo "私钥存在: " . (file_exists($privateKeyPath) ? '✅ 是' : '❌ 否') . "\n";
    
    if (!file_exists($privateKeyPath)) {
        echo "❌ 私钥文件不存在，请检查路径配置\n";
        exit(1);
    }
    
    // 读取并验证私钥
    $privateKeyContent = file_get_contents($privateKeyPath);
    echo "私钥长度: " . strlen($privateKeyContent) . " 字节\n";
    
    if (IcbcSignature::validatePrivateKey($privateKeyContent)) {
        echo "✅ 私钥格式正确\n";
    } else {
        echo "❌ 私钥格式错误或无法加载\n";
        echo "OpenSSL错误: " . openssl_error_string() . "\n";
        exit(1);
    }
    
    // 检查公钥文件
    if (!empty($publicKeyPath) && file_exists($publicKeyPath)) {
        echo "公钥路径: {$publicKeyPath}\n";
        echo "公钥存在: ✅ 是\n";
        
        $publicKeyContent = file_get_contents($publicKeyPath);
        echo "公钥长度: " . strlen($publicKeyContent) . " 字节\n";
        
        if (IcbcSignature::validatePublicKey($publicKeyContent)) {
            echo "✅ 公钥格式正确\n";
            
            // 测试密钥匹配性
            if (IcbcSignature::testKeyPair($privateKeyContent, $publicKeyContent)) {
                echo "✅ 私钥和公钥匹配\n";
            } else {
                echo "❌ 私钥和公钥不匹配\n";
                echo "⚠️  这可能是导致签名验证失败的原因\n";
            }
        } else {
            echo "❌ 公钥格式错误\n";
        }
    } else {
        echo "⚠️  公钥文件未配置或不存在\n";
    }
    
    // 3. 测试签名生成
    echo "\n🧪 第三步：测试签名生成\n";
    echo "======================\n";
    
    // 模拟真实的支付参数
    $testParams = [
        'app_id' => $config['app_id'],
        'msg_id' => date('YmdHis') . sprintf('%010d', mt_rand(0, 9999999999)),
        'format' => 'json',
        'charset' => 'UTF-8',
        'sign_type' => 'RSA2',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0',
        'biz_content' => json_encode([
            'mer_id' => $config['mer_id'],
            'mer_prtcl_no' => $config['mer_prtcl_no'],
            'out_trade_no' => 'TEST_SIGN_' . time(),
            'order_amt' => '0.01',
            'pay_mode' => '9',
            'access_type' => '1',
            'goods_body' => '签名测试订单',
            'currency' => 'CNY',
        ], JSON_UNESCAPED_UNICODE),
    ];
    
    echo "✅ 测试参数准备完成\n";
    echo "参数数量: " . count($testParams) . "\n";
    
    // 构建签名字符串（按照工行规范）
    echo "\n📝 第四步：构建签名字符串\n";
    echo "========================\n";
    
    // 按照工行API规范构建签名字符串
    $apiPath = '/api/cardbusiness/aggregatepay/consumepurchase';
    $signString = WebUtils::buildOrderedSignStr($apiPath, $testParams);
    
    echo "✅ 签名字符串构建完成\n";
    echo "签名字符串长度: " . strlen($signString) . " 字符\n";
    echo "签名字符串预览: " . substr($signString, 0, 200) . "...\n\n";
    
    // 使用新的IcbcSignature类生成签名
    try {
        $signature = IcbcSignature::sign(
            $signString,
            'RSA2',
            $privateKeyContent,
            'UTF-8'
        );
        
        echo "✅ 签名生成成功\n";
        echo "签名长度: " . strlen($signature) . " 字符\n";
        echo "签名预览: " . substr($signature, 0, 50) . "...\n";
        
        // 验证签名格式
        $decodedSig = base64_decode($signature);
        if ($decodedSig !== false) {
            echo "✅ 签名Base64编码正确\n";
            echo "解码后长度: " . strlen($decodedSig) . " 字节\n";
        } else {
            echo "❌ 签名Base64编码错误\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 签名生成失败: " . $e->getMessage() . "\n";
        exit(1);
    }
    
    // 5. 构建完整的请求URL进行测试
    echo "\n🌐 第五步：测试支付请求\n";
    echo "======================\n";
    
    $testParams['sign'] = $signature;
    
    // 使用工行沙箱环境进行测试
    $gatewayUrl = 'https://gw.open.icbc.com.cn/sandbox' . $apiPath;
    $testUrl = $gatewayUrl . '?' . http_build_query($testParams);
    
    echo "测试URL长度: " . strlen($testUrl) . " 字符\n";
    
    // 发送HTTP请求测试
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'ICBC-SignTest/1.0');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "HTTP状态码: {$httpCode}\n";
    
    if ($curlError) {
        echo "❌ 网络请求失败: {$curlError}\n";
    } else {
        if (strpos($response, 'sign verify failed') !== false) {
            echo "❌ 仍然出现签名验证失败\n";
            echo "这通常意味着以下问题之一：\n";
            echo "1. 使用的APP_ID和商户号不匹配\n";
            echo "2. 私钥与工行系统中的公钥不对应\n";
            echo "3. 签名字符串构建规则不正确\n";
            echo "4. 使用了错误的环境（沙箱vs生产）\n\n";
            
            // 保存错误页面用于分析
            file_put_contents('sign_verify_failed_response.html', $response);
            echo "错误响应已保存到 sign_verify_failed_response.html\n";
            
        } elseif (strpos($response, '参数校验失败') !== false) {
            echo "⚠️  出现参数校验失败（这比签名失败是进步）\n";
            echo "签名验证可能已经通过，但其他参数有问题\n";
            
        } elseif (strpos($response, '支付') !== false || strpos($response, 'form') !== false) {
            echo "✅ 签名验证成功！到达了支付页面\n";
            file_put_contents('sign_test_success.html', $response);
            echo "成功页面已保存到 sign_test_success.html\n";
            
        } else {
            echo "⚠️  收到未知响应\n";
            echo "响应前100字符: " . substr(strip_tags($response), 0, 100) . "\n";
            file_put_contents('sign_test_unknown.html', $response);
        }
    }
    
    // 6. 修复建议
    echo "\n💡 修复建议和后续步骤\n";
    echo "====================\n";
    
    if (strpos($response, 'sign verify failed') !== false) {
        echo "如果仍然出现签名验证失败，请按以下步骤处理：\n\n";
        
        echo "1. 联系工商银行技术支持\n";
        echo "   - 提供您的APP_ID: " . $config['app_id'] . "\n";
        echo "   - 提供您的商户号: " . $config['mer_id'] . "\n";
        echo "   - 请求重新获取正确的证书对\n\n";
        
        echo "2. 检查环境配置\n";
        echo "   - 确认使用的是沙箱环境还是生产环境\n";
        echo "   - 确认APP_ID和证书的环境一致\n\n";
        
        echo "3. 重新生成密钥对\n";
        echo "   - 生成新的RSA密钥对\n";
        echo "   - 将公钥提交给工商银行\n";
        echo "   - 等待工行确认配置完成\n\n";
        
        echo "4. 临时解决方案（仅用于开发测试）\n";
        echo "   - 在.env中设置 ICBC_MOCK_ENABLED=true\n";
        echo "   - 这将使用模拟签名进行功能测试\n\n";
    } else {
        echo "✅ 签名问题已解决！\n";
        echo "支付系统现在应该可以正常工作了。\n\n";
        
        echo "后续步骤：\n";
        echo "1. 在生产环境中测试\n";
        echo "2. 配置正确的回调URL\n";
        echo "3. 实现完整的支付流程\n";
        echo "4. 添加日志记录和错误处理\n";
    }
    
    echo "\n🔧 快速测试命令：\n";
    echo "# 重新运行完整的配置验证\n";
    echo "php validate_icbc_config.php\n\n";
    echo "# 测试支付表单生成\n";
    echo "php test_payment_form.php\n\n";
    echo "# 运行完整的支付测试\n";
    echo "php test_icbc_payment.php\n\n";
    
    echo "✅ 签名验证失败修复工具运行完成！\n";
    
} catch (Exception $e) {
    echo "❌ 修复过程中出现错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "\n请检查：\n";
    echo "1. Laravel应用是否正确启动\n";
    echo "2. 所有依赖是否已安装\n";
    echo "3. 配置文件是否存在\n";
    echo "4. 密钥文件路径是否正确\n";
    echo "5. PHP OpenSSL扩展是否已启用\n";
} 