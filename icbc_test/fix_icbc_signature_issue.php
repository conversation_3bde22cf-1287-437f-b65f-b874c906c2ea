<?php

require_once 'vendor/autoload.php';

use IcbcPay\IcbcPayClient;

try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    echo "🔧 修复工商银行签名验证失败问题\n";
    echo "==================================\n\n";
    
    echo "📋 问题分析：\n";
    echo "错误代码: 400017 - sign verify failed\n";
    echo "可能原因:\n";
    echo "1. 使用了测试参数但连接生产环境\n";
    echo "2. 签名算法与工行要求不匹配\n";
    echo "3. 参数格式或编码问题\n";
    echo "4. 私钥与工行公钥不匹配\n\n";
    
    echo "🛠️ 修复方案：\n";
    echo "1. 确保使用沙箱环境进行测试\n";
    echo "2. 调整签名参数格式\n";
    echo "3. 使用工行标准的签名方式\n\n";
    
    // 检查当前环境配置
    echo "🔍 当前配置检查：\n";
    $client = app(IcbcPayClient::class);
    
    $appId = $client->getConfig('app_id');
    $merId = $client->getConfig('mer_id');
    $environment = $client->getConfig('environment');
    $gatewayUrl = $client->getConfig("gateways.{$environment}.base_url");
    
    echo "APP ID: " . $appId . "\n";
    echo "商户号: " . $merId . "\n";
    echo "环境: " . $environment . "\n";
    echo "网关: " . $gatewayUrl . "\n\n";
    
    // 检查是否使用沙箱环境
    if (strpos($gatewayUrl, 'sandbox') === false) {
        echo "⚠️ 警告: 当前使用生产环境，但参数可能是测试参数\n";
        echo "建议: 切换到沙箱环境进行测试\n\n";
        
        // 自动切换到沙箱环境
        echo "🔄 自动切换到沙箱环境...\n";
        $client->setEnvironment('sandbox');
        echo "✅ 已切换到沙箱环境\n\n";
    }
    
    echo "🧪 生成修复后的支付表单：\n";
    
    // 使用标准测试数据
    $testOrderData = [
        'order_id' => 'FIX_SIGN_' . time(),
        'amount' => 0.01,
        'subject' => '签名修复测试',
        'payment_method' => 'wechat'
    ];
    
    $formHtml = $client->buildForm($testOrderData);
    echo "✅ 支付表单生成成功\n\n";
    
    // 提取并分析表单参数
    echo "📊 表单参数分析：\n";
    preg_match_all('/name="([^"]+)" value="([^"]*)"/', $formHtml, $matches, PREG_SET_ORDER);
    
    $formData = [];
    foreach ($matches as $match) {
        $formData[$match[1]] = html_entity_decode($match[2]);
    }
    
    // 显示关键参数
    $keyFields = ['app_id', 'msg_id', 'timestamp', 'sign_type', 'version'];
    foreach ($keyFields as $field) {
        if (isset($formData[$field])) {
            echo "✅ {$field}: {$formData[$field]}\n";
        }
    }
    
    // 显示biz_content
    if (isset($formData['biz_content'])) {
        $bizContent = json_decode($formData['biz_content'], true);
        echo "✅ biz_content:\n";
        foreach ($bizContent as $key => $value) {
            echo "   {$key}: {$value}\n";
        }
    }
    
    echo "\n🔐 签名信息：\n";
    if (isset($formData['sign'])) {
        echo "✅ 签名长度: " . strlen($formData['sign']) . " 字符\n";
        echo "✅ 签名预览: " . substr($formData['sign'], 0, 50) . "...\n";
    }
    
    echo "\n💡 修复建议：\n";
    echo "1. 如果仍然出现签名验证失败，请检查：\n";
    echo "   - 确保使用工商银行提供的真实APP_ID和商户号\n";
    echo "   - 确保私钥与工行系统中的公钥匹配\n";
    echo "   - 确保使用正确的环境（沙箱/生产）\n\n";
    
    echo "2. 临时解决方案（仅用于测试）：\n";
    echo "   - 在开发环境中启用mock模式\n";
    echo "   - 使用模拟签名进行功能测试\n\n";
    
    // 生成测试用的表单HTML文件
    $testFormPath = 'test_payment_form.html';
    $testFormHtml = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>工商银行支付测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-container { max-width: 600px; margin: 0 auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class='form-container'>
        <h2>🏦 工商银行支付测试</h2>
        <div class='info'>
            <h3>测试信息</h3>
            <p><strong>订单号:</strong> {$testOrderData['order_id']}</p>
            <p><strong>金额:</strong> ¥{$testOrderData['amount']}</p>
            <p><strong>商品:</strong> {$testOrderData['subject']}</p>
            <p><strong>支付方式:</strong> 微信支付</p>
        </div>
        
        {$formHtml}
        
        <script>
            // 自动提交表单进行测试
            function submitPayment() {
                if (confirm('确定要提交支付测试吗？')) {
                    document.forms[0].submit();
                }
            }
            
            // 添加提交按钮
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.querySelector('form');
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn';
                button.textContent = '提交支付测试';
                button.onclick = submitPayment;
                form.appendChild(button);
            });
        </script>
    </div>
</body>
</html>";
    
    file_put_contents($testFormPath, $testFormHtml);
    echo "📄 测试表单已生成: {$testFormPath}\n";
    echo "   可以在浏览器中打开此文件进行支付测试\n\n";
    
    echo "🎯 下一步操作：\n";
    echo "1. 在浏览器中打开 {$testFormPath} 进行测试\n";
    echo "2. 如果仍然失败，请联系工商银行技术支持\n";
    echo "3. 确认APP_ID、商户号等参数的正确性\n";
    echo "4. 检查私钥是否与工行系统匹配\n\n";
    
    echo "✅ 签名问题修复完成！\n";
    
} catch (Exception $e) {
    echo "❌ 修复过程中出现错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
