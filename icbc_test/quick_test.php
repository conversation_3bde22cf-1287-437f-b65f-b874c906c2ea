<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use IcbcPay\Models\PaymentRecord;
use IcbcPay\Services\IcbcPayService;

echo "快速测试工商银行支付...\n";

try {
    $payment = PaymentRecord::create([
        'out_trade_no' => 'QUICK_' . time(),
        'total_amount' => 0.01,
        'subject' => '快速测试',
        'payment_method' => 'wechat',
        'car_number' => '测试A123',
        'status' => 'pending'
    ]);
    
    $service = new IcbcPayService();
    $result = $service->createPayment($payment);
    
    echo "成功: " . ($result['success'] ? 'YES' : 'NO') . "\n";
    echo "支付URL长度: " . strlen($result['payment_url'] ?? '') . "\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
} 