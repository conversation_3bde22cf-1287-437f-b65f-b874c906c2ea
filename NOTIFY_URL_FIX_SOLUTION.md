# 🔧 工行支付 notify_url 必输问题解决方案

## 📋 问题描述

在调用工行支付接口时出现以下错误：
```
提示：自选校验失败：字段notify_url的值必输
```

## 🔍 问题分析

经过详细分析和测试，发现问题的根本原因是：

1. **字段缺失**：工行支付接口要求必须包含 `notify_url` 字段
2. **字段重复要求**：某些工行接口同时需要 `notify_url` 和 `mer_url` 字段
3. **配置正确但传递有误**：虽然配置文件中有正确的回调地址，但在构建支付请求时可能没有正确传递

## ✅ 解决方案

### 1. 修复支付请求构建逻辑

在 `packages/icbc-pay/src/IcbcPayClient.php` 中，确保同时设置 `notify_url` 和 `mer_url` 字段：

```php
// 准备业务内容
$bizContent = [
    'mer_id' => (string)($this->getConfig('mer_id') ?? ''),
    'mer_prtcl_no' => (string)($this->getConfig('mer_prtcl_no') ?? ''),
    'out_trade_no' => (string)($orderData['order_id'] ?? ''),
    'order_amt' => $this->convertToFen($orderData['amount'] ?? '0'),
    'pay_mode' => $this->getPayModeByMethod($orderData['payment_method'] ?? 'wechat'),
    'access_type' => '1',
    'notify_url' => (string)($this->getConfig('notify_url') ?? ''), // ✅ 添加notify_url
    'mer_url' => (string)($this->getConfig('notify_url') ?? ''),     // ✅ 保留mer_url
    'goods_body' => (string)($orderData['subject'] ?? ''),
    'goods_detail' => (string)($orderData['body'] ?? $orderData['subject'] ?? ''),
    'expire_time' => $this->getIcbcTimestamp(time() + 1800),
    'page_url' => (string)($this->getConfig('return_url') ?? ''),
    'return_url' => (string)($this->getConfig('return_url') ?? ''), // ✅ 添加return_url
    'currency' => 'CNY',
];
```

### 2. 验证配置文件

确保 `.env` 文件中配置了正确的回调地址：

```env
# 工行支付回调配置
ICBC_NOTIFY_URL=https://icbc.dev.hiwsoft.com/icbc-pay/notify
ICBC_RETURN_URL=https://icbc.dev.hiwsoft.com/icbc-pay/return
```

### 3. 验证配置加载

```bash
php artisan tinker --execute="
echo 'notify_url: ' . config('icbc-pay.notify_url');
echo 'return_url: ' . config('icbc-pay.return_url');
"
```

## 🧪 测试验证

### 1. 创建测试支付订单

```bash
php artisan tinker --execute="
\$orderData = [
    'car_number' => '测试A12345',
    'amount' => '1.00',
    'payment_method' => 'wechat',
    'parking_duration' => 60
];

\$response = app('App\Http\Controllers\ParkingController')->createPayment(
    new Illuminate\Http\Request(\$orderData)
);

echo \$response->getContent();
"
```

### 2. 验证生成的业务内容

从测试结果可以看到，生成的 `biz_content` 包含了所有必要字段：

```json
{
  "mer_id": "301059620104",
  "mer_prtcl_no": "3010596201040201",
  "out_trade_no": "PARK20250829125336658804065",
  "order_amt": "100",
  "pay_mode": "9",
  "access_type": "1",
  "notify_url": "https://icbc.dev.hiwsoft.com/icbc-pay/notify",
  "mer_url": "https://icbc.dev.hiwsoft.com/icbc-pay/notify",
  "goods_body": "停车费支付",
  "goods_detail": "停车费支付 - 车牌：测试A12345",
  "expire_time": "2025-08-29 13:23:36",
  "page_url": "https://icbc.dev.hiwsoft.com/icbc-pay/return",
  "return_url": "https://icbc.dev.hiwsoft.com/icbc-pay/return",
  "currency": "CNY"
}
```

## ✅ 修复结果

### 修复前
- ❌ 只有 `mer_url` 字段
- ❌ 缺少 `notify_url` 字段
- ❌ 可能缺少 `return_url` 字段

### 修复后
- ✅ 同时包含 `notify_url` 和 `mer_url` 字段
- ✅ 同时包含 `page_url` 和 `return_url` 字段
- ✅ 所有回调地址都正确配置
- ✅ 支付订单创建成功

## 🎯 关键要点

1. **字段兼容性**：工行不同接口对回调字段的要求可能不同，同时提供多个字段确保兼容性
2. **配置验证**：确保环境变量正确配置并能被正确加载
3. **测试验证**：通过实际测试验证修复效果

## 📝 相关文件

- `packages/icbc-pay/src/IcbcPayClient.php` - 主要修复文件
- `config/icbc-pay.php` - 配置文件
- `.env` - 环境变量配置

## 🚀 部署建议

1. **生产环境**：确保生产环境的回调地址可以被工行服务器访问
2. **HTTPS要求**：工行支付要求回调地址必须使用HTTPS
3. **域名配置**：确保域名解析正确，工行服务器能够访问回调地址
4. **防火墙设置**：确保服务器防火墙允许工行服务器的回调请求

修复完成后，工行支付的 notify_url 必输问题已经解决，支付流程可以正常进行。
