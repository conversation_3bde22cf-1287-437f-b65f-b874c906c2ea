# 工商银行支付SDK - PHP 8.2版本

现代化的工商银行支付SDK，完全兼容PHP 8.2，提供简洁易用的API接口。

## 特性

- ✅ **PHP 8.2兼容**: 使用现代PHP特性，严格类型声明
- ✅ **现代化架构**: 面向对象设计，清晰的代码结构
- ✅ **完整的JSDoc注释**: 详细的文档和类型提示
- ✅ **多种支付方式**: 微信支付、支付宝支付、银联支付
- ✅ **多种支付类型**: 二维码、H5、APP支付
- ✅ **安全可靠**: RSA2签名，AES加密支持
- ✅ **易于使用**: 简洁的API设计，链式调用
- ✅ **环境切换**: 沙箱/生产环境轻松切换
- ✅ **错误处理**: 完善的异常处理机制
- ✅ **示例代码**: 丰富的使用示例

## 系统要求

- PHP >= 8.2
- OpenSSL 扩展
- cURL 扩展
- JSON 扩展

## 安装

### Composer安装（推荐）

```bash
composer require icbc-pay/php-sdk
```

### 手动安装

1. 下载SDK包
2. 解压到项目目录
3. 引入自动加载文件

```php
require_once 'path/to/icbc-pay/src/IcbcPayClient.php';
```

## 快速开始

### 1. 配置

```php
<?php
use IcbcPay\IcbcPayClient;

$config = [
    'app_id' => '你的应用ID',
    'merchant_id' => '你的商户号',
    'merchant_protocol_no' => '你的商户协议号',
    'environment' => 'sandbox', // 或 'production'
    'private_key_path' => '/path/to/private_key.pem',
    'icbc_public_key_path' => '/path/to/icbc_public_key.pem',
    'notify_url' => 'https://your-domain.com/icbc/notify',
    'return_url' => 'https://your-domain.com/icbc/return',
];

$client = new IcbcPayClient($config);
```

### 2. 创建支付

```php
$orderData = [
    'order_id' => 'ORDER_' . time(),
    'amount' => '0.01',
    'subject' => '测试商品',
    'merchant_order_no' => 'MERCHANT_' . time(),
];

// 创建支付订单
$result = $client->pay($orderData);

// 或者使用特定支付方式
$result = $client->wechatPay($orderData);  // 微信支付
$result = $client->alipay($orderData);     // 支付宝支付
$result = $client->qrPay($orderData);      // 二维码支付
```

### 3. 查询支付状态

```php
$result = $client->query('ORDER_123456');
$status = $client->getStatus('ORDER_123456');
$isPaid = $client->isPaid('ORDER_123456');
```

### 4. 处理回调

```php
$notifyData = $_POST; // 接收回调数据
$result = $client->notify($notifyData);

if ($result['success']) {
    // 处理支付成功逻辑
    echo "支付成功，订单号：" . $result['order_id'];
} else {
    // 处理支付失败逻辑
    echo "支付失败";
}
```

## 详细使用

### 配置参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| app_id | string | 是 | 工行开放平台应用ID |
| merchant_id | string | 是 | 商户号 |
| merchant_protocol_no | string | 否 | 商户协议号 |
| environment | string | 否 | 环境：sandbox/production |
| private_key_path | string | 是 | 应用私钥文件路径 |
| icbc_public_key_path | string | 否 | 工行公钥文件路径 |
| sign_type | string | 否 | 签名类型：RSA/RSA2 |
| charset | string | 否 | 字符编码：UTF-8 |
| format | string | 否 | 数据格式：json |
| notify_url | string | 否 | 异步回调地址 |
| return_url | string | 否 | 同步跳转地址 |

### 支付方式

#### 微信支付

```php
$result = $client->wechatPay([
    'order_id' => 'ORDER_123',
    'amount' => '100.00',
    'subject' => '商品名称',
    'merchant_order_no' => 'MERCHANT_123',
]);
```

#### 支付宝支付

```php
$result = $client->alipay([
    'order_id' => 'ORDER_123',
    'amount' => '100.00',
    'subject' => '商品名称',
    'merchant_order_no' => 'MERCHANT_123',
]);
```

#### 二维码支付

```php
$result = $client->qrPay([
    'order_id' => 'ORDER_123',
    'amount' => '100.00',
    'subject' => '商品名称',
    'merchant_order_no' => 'MERCHANT_123',
]);

// 获取二维码
if (isset($result['qr_code'])) {
    echo "二维码: " . $result['qr_code'];
}
```

#### H5支付

```php
$result = $client->h5Pay([
    'order_id' => 'ORDER_123',
    'amount' => '100.00',
    'subject' => '商品名称',
    'merchant_order_no' => 'MERCHANT_123',
]);
```

#### APP支付

```php
$result = $client->appPay([
    'order_id' => 'ORDER_123',
    'amount' => '100.00',
    'subject' => '商品名称',
    'merchant_order_no' => 'MERCHANT_123',
]);
```

### 生成支付表单

```php
$formHtml = $client->buildForm([
    'order_id' => 'ORDER_123',
    'amount' => '100.00',
    'subject' => '商品名称',
    'merchant_order_no' => 'MERCHANT_123',
]);

echo $formHtml; // 输出自动提交的HTML表单
```

### 环境切换

```php
// 切换到沙箱环境
$client->sandbox();

// 切换到生产环境
$client->production();

// 或者直接设置
$client->setEnvironment('production');
```

### 配置管理

```php
// 设置配置
$client->setConfig('timeout', 60);

// 获取配置
$timeout = $client->getConfig('timeout');

// 获取所有配置
$allConfig = $client->getConfig();
```

## 证书配置

### 1. 生成RSA密钥对

```bash
# 生成私钥
openssl genrsa -out private_key.pem 2048

# 生成公钥
openssl rsa -in private_key.pem -pubout -out public_key.pem
```

### 2. 密钥文件存放

```
storage/keys/
├── icbc_private_key.pem     # 应用私钥
├── icbc_public_key.pem      # 工行公钥
└── icbc_gateway_key.pem     # 网关密钥（可选）
```

### 3. 密钥格式

私钥格式：
```
-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...
-----END PRIVATE KEY-----
```

公钥格式：
```
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----
```

## 错误处理

```php
try {
    $result = $client->pay($orderData);
} catch (InvalidArgumentException $e) {
    // 参数错误
    echo "参数错误: " . $e->getMessage();
} catch (Exception $e) {
    // 其他错误
    echo "支付错误: " . $e->getMessage();
}
```

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 参数错误 | 检查必填参数 |
| 1002 | 签名错误 | 检查密钥配置 |
| 1003 | 商户不存在 | 检查商户号 |
| 1004 | 订单重复 | 使用唯一订单号 |
| 2001 | 网络超时 | 重试或检查网络 |
| 3001 | 证书错误 | 检查证书配置 |

## 回调处理

### 1. 接收回调

```php
// notify.php
<?php
use IcbcPay\IcbcPayClient;

$client = new IcbcPayClient($config);

try {
    $notifyData = $_POST;
    $result = $client->notify($notifyData);
    
    if ($result['success']) {
        // 更新订单状态
        updateOrderStatus($result['order_id'], 'paid');
        
        // 返回成功响应
        echo 'SUCCESS';
    } else {
        // 记录失败日志
        error_log('Payment failed: ' . json_encode($result));
        echo 'FAIL';
    }
} catch (Exception $e) {
    error_log('Notify error: ' . $e->getMessage());
    echo 'FAIL';
}
```

### 2. 验证回调签名

```php
if ($client->verifyNotify($_POST)) {
    echo "签名验证通过";
} else {
    echo "签名验证失败";
}
```

## 日志记录

建议在生产环境中启用日志记录：

```php
// 记录请求日志
error_log('ICBC Payment Request: ' . json_encode($orderData));

// 记录响应日志
error_log('ICBC Payment Response: ' . json_encode($result));

// 记录错误日志
error_log('ICBC Payment Error: ' . $e->getMessage());
```

## 测试

### 单元测试

```bash
# 运行测试
vendor/bin/phpunit tests/

# 运行特定测试
vendor/bin/phpunit tests/IcbcPayClientTest.php
```

### 沙箱测试

```php
// 使用沙箱环境进行测试
$client = new IcbcPayClient([
    'environment' => 'sandbox',
    // 其他配置...
]);

// 使用测试金额
$result = $client->pay([
    'amount' => '0.01', // 1分钱测试
    // 其他参数...
]);
```

## 最佳实践

### 1. 安全建议

- 私钥文件权限设置为600
- 不要将私钥提交到代码仓库
- 使用HTTPS传输敏感数据
- 验证回调来源IP
- 记录所有支付相关操作

### 2. 性能优化

- 使用连接池复用HTTP连接
- 合理设置超时时间
- 缓存配置信息
- 异步处理回调

### 3. 错误处理

- 实现重试机制
- 记录详细的错误日志
- 提供友好的错误提示
- 监控支付成功率

## 更新日志

### v2.0.0 (2024-01-01)
- 重构为PHP 8.2兼容版本
- 添加现代化的类型声明
- 改进错误处理机制
- 优化API设计
- 添加完整的JSDoc注释

### v1.0.0 (2023-12-01)
- 初始版本发布
- 支持基本支付功能

## 许可证

MIT License

## 技术支持

如有问题，请通过以下方式联系：

- 提交Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>
- QQ群: 123456789

## 贡献

欢迎提交PR和Issue，请遵循以下规范：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 相关链接

- [工商银行开放平台](https://open.icbc.com.cn/)
- [API文档](https://open.icbc.com.cn/docs)
- [开发者社区](https://bbs.icbc.com.cn/) 