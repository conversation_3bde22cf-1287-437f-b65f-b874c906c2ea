<?php

declare(strict_types=1);

/**
 * 工商银行支付SDK密钥设置脚本
 * 
 * 用于帮助用户设置和验证证书配置
 */

class IcbcKeySetup
{
    private string $keysPath;
    
    public function __construct(string $keysPath = null)
    {
        $this->keysPath = $keysPath ?? __DIR__ . '/../../storage/keys';
    }
    
    /**
     * 运行设置向导
     */
    public function run(): void
    {
        echo "=== 工商银行支付SDK密钥设置向导 ===\n\n";
        
        $this->checkPhpVersion();
        $this->checkExtensions();
        $this->createKeysDirectory();
        $this->setupKeys();
        $this->verifyKeys();
        $this->generateExampleConfig();
        
        echo "\n=== 设置完成 ===\n";
        echo "请根据生成的示例配置文件更新您的配置。\n";
    }
    
    /**
     * 检查PHP版本
     */
    private function checkPhpVersion(): void
    {
        echo "检查PHP版本...\n";
        
        if (version_compare(PHP_VERSION, '8.2.0', '<')) {
            echo "❌ 错误: 需要PHP 8.2或更高版本，当前版本: " . PHP_VERSION . "\n";
            exit(1);
        }
        
        echo "✅ PHP版本检查通过: " . PHP_VERSION . "\n\n";
    }
    
    /**
     * 检查必需的扩展
     */
    private function checkExtensions(): void
    {
        echo "检查PHP扩展...\n";
        
        $requiredExtensions = ['openssl', 'curl', 'json'];
        
        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                echo "❌ 错误: 缺少必需的PHP扩展: {$extension}\n";
                exit(1);
            }
            echo "✅ {$extension} 扩展已加载\n";
        }
        
        echo "\n";
    }
    
    /**
     * 创建密钥目录
     */
    private function createKeysDirectory(): void
    {
        echo "创建密钥目录...\n";
        
        if (!is_dir($this->keysPath)) {
            if (!mkdir($this->keysPath, 0755, true)) {
                echo "❌ 错误: 无法创建密钥目录: {$this->keysPath}\n";
                exit(1);
            }
            echo "✅ 创建密钥目录: {$this->keysPath}\n";
        } else {
            echo "✅ 密钥目录已存在: {$this->keysPath}\n";
        }
        
        // 设置目录权限
        chmod($this->keysPath, 0755);
        echo "\n";
    }
    
    /**
     * 设置密钥
     */
    private function setupKeys(): void
    {
        echo "设置密钥文件...\n\n";
        
        $this->setupPrivateKey();
        $this->setupIcbcPublicKey();
        $this->setupGatewayKey();
    }
    
    /**
     * 设置应用私钥
     */
    private function setupPrivateKey(): void
    {
        $privateKeyPath = $this->keysPath . '/icbc_private_key.pem';
        
        if (file_exists($privateKeyPath)) {
            echo "应用私钥文件已存在，是否要重新设置？(y/n): ";
            $input = trim(fgets(STDIN));
            if (strtolower($input) !== 'y') {
                echo "跳过应用私钥设置\n\n";
                return;
            }
        }
        
        echo "请选择应用私钥设置方式:\n";
        echo "1. 自动生成新的RSA密钥对\n";
        echo "2. 手动输入现有私钥\n";
        echo "3. 从文件复制私钥\n";
        echo "请选择 (1-3): ";
        
        $choice = trim(fgets(STDIN));
        
        switch ($choice) {
            case '1':
                $this->generateNewKeyPair();
                break;
            case '2':
                $this->inputPrivateKey();
                break;
            case '3':
                $this->copyPrivateKeyFromFile();
                break;
            default:
                echo "无效选择，跳过私钥设置\n\n";
        }
    }
    
    /**
     * 生成新的密钥对
     */
    private function generateNewKeyPair(): void
    {
        echo "\n正在生成RSA密钥对...\n";
        
        $config = [
            "digest_alg" => "sha256",
            "private_key_bits" => 2048,
            "private_key_type" => OPENSSL_KEYTYPE_RSA,
        ];
        
        $res = openssl_pkey_new($config);
        if (!$res) {
            echo "❌ 错误: 无法生成密钥对\n";
            return;
        }
        
        // 导出私钥
        openssl_pkey_export($res, $privateKey);
        
        // 获取公钥
        $publicKeyDetails = openssl_pkey_get_details($res);
        $publicKey = $publicKeyDetails['key'];
        
        // 保存私钥
        $privateKeyPath = $this->keysPath . '/icbc_private_key.pem';
        file_put_contents($privateKeyPath, $privateKey);
        chmod($privateKeyPath, 0600);
        
        // 保存公钥
        $publicKeyPath = $this->keysPath . '/app_public_key.pem';
        file_put_contents($publicKeyPath, $publicKey);
        chmod($publicKeyPath, 0644);
        
        echo "✅ 成功生成密钥对\n";
        echo "   私钥: {$privateKeyPath}\n";
        echo "   公钥: {$publicKeyPath}\n";
        echo "\n⚠️  请将公钥提交给工商银行进行配置！\n\n";
        
        // 显示公钥内容
        echo "您的公钥内容（请提交给工商银行）:\n";
        echo "=" . str_repeat("=", 60) . "\n";
        echo $publicKey;
        echo "=" . str_repeat("=", 60) . "\n\n";
    }
    
    /**
     * 手动输入私钥
     */
    private function inputPrivateKey(): void
    {
        echo "\n请粘贴您的RSA私钥（包含BEGIN和END行）:\n";
        echo "输入完成后，在新行输入 'END' 并回车:\n\n";
        
        $privateKey = '';
        while (true) {
            $line = fgets(STDIN);
            if (trim($line) === 'END') {
                break;
            }
            $privateKey .= $line;
        }
        
        if (empty(trim($privateKey))) {
            echo "❌ 错误: 私钥为空\n\n";
            return;
        }
        
        // 验证私钥格式
        $key = openssl_pkey_get_private($privateKey);
        if (!$key) {
            echo "❌ 错误: 私钥格式无效\n\n";
            return;
        }
        
        // 保存私钥
        $privateKeyPath = $this->keysPath . '/icbc_private_key.pem';
        file_put_contents($privateKeyPath, $privateKey);
        chmod($privateKeyPath, 0600);
        
        echo "✅ 私钥保存成功: {$privateKeyPath}\n\n";
    }
    
    /**
     * 从文件复制私钥
     */
    private function copyPrivateKeyFromFile(): void
    {
        echo "\n请输入私钥文件的完整路径: ";
        $sourceFile = trim(fgets(STDIN));
        
        if (!file_exists($sourceFile)) {
            echo "❌ 错误: 文件不存在: {$sourceFile}\n\n";
            return;
        }
        
        $privateKey = file_get_contents($sourceFile);
        
        // 验证私钥格式
        $key = openssl_pkey_get_private($privateKey);
        if (!$key) {
            echo "❌ 错误: 私钥格式无效\n\n";
            return;
        }
        
        // 保存私钥
        $privateKeyPath = $this->keysPath . '/icbc_private_key.pem';
        file_put_contents($privateKeyPath, $privateKey);
        chmod($privateKeyPath, 0600);
        
        echo "✅ 私钥复制成功: {$privateKeyPath}\n\n";
    }
    
    /**
     * 设置工行公钥
     */
    private function setupIcbcPublicKey(): void
    {
        $publicKeyPath = $this->keysPath . '/icbc_public_key.pem';
        
        if (file_exists($publicKeyPath)) {
            echo "工行公钥文件已存在，是否要重新设置？(y/n): ";
            $input = trim(fgets(STDIN));
            if (strtolower($input) !== 'y') {
                echo "跳过工行公钥设置\n\n";
                return;
            }
        }
        
        echo "请输入工商银行提供的公钥（包含BEGIN和END行）:\n";
        echo "输入完成后，在新行输入 'END' 并回车:\n\n";
        
        $publicKey = '';
        while (true) {
            $line = fgets(STDIN);
            if (trim($line) === 'END') {
                break;
            }
            $publicKey .= $line;
        }
        
        if (empty(trim($publicKey))) {
            echo "工行公钥为空，跳过设置\n\n";
            return;
        }
        
        // 验证公钥格式
        $key = openssl_pkey_get_public($publicKey);
        if (!$key) {
            echo "❌ 错误: 工行公钥格式无效\n\n";
            return;
        }
        
        // 保存公钥
        file_put_contents($publicKeyPath, $publicKey);
        chmod($publicKeyPath, 0644);
        
        echo "✅ 工行公钥保存成功: {$publicKeyPath}\n\n";
    }
    
    /**
     * 设置网关密钥
     */
    private function setupGatewayKey(): void
    {
        echo "是否需要设置网关密钥？(通常不需要) (y/n): ";
        $input = trim(fgets(STDIN));
        
        if (strtolower($input) !== 'y') {
            echo "跳过网关密钥设置\n\n";
            return;
        }
        
        $gatewayKeyPath = $this->keysPath . '/icbc_gateway_key.pem';
        
        echo "请输入网关密钥:\n";
        echo "输入完成后，在新行输入 'END' 并回车:\n\n";
        
        $gatewayKey = '';
        while (true) {
            $line = fgets(STDIN);
            if (trim($line) === 'END') {
                break;
            }
            $gatewayKey .= $line;
        }
        
        if (empty(trim($gatewayKey))) {
            echo "网关密钥为空，跳过设置\n\n";
            return;
        }
        
        // 保存网关密钥
        file_put_contents($gatewayKeyPath, $gatewayKey);
        chmod($gatewayKeyPath, 0644);
        
        echo "✅ 网关密钥保存成功: {$gatewayKeyPath}\n\n";
    }
    
    /**
     * 验证密钥
     */
    private function verifyKeys(): void
    {
        echo "验证密钥文件...\n";
        
        $privateKeyPath = $this->keysPath . '/icbc_private_key.pem';
        $publicKeyPath = $this->keysPath . '/icbc_public_key.pem';
        
        // 验证私钥
        if (file_exists($privateKeyPath)) {
            $privateKey = file_get_contents($privateKeyPath);
            $key = openssl_pkey_get_private($privateKey);
            if ($key) {
                echo "✅ 应用私钥验证通过\n";
            } else {
                echo "❌ 应用私钥验证失败\n";
            }
        } else {
            echo "⚠️  应用私钥文件不存在\n";
        }
        
        // 验证工行公钥
        if (file_exists($publicKeyPath)) {
            $publicKey = file_get_contents($publicKeyPath);
            $key = openssl_pkey_get_public($publicKey);
            if ($key) {
                echo "✅ 工行公钥验证通过\n";
            } else {
                echo "❌ 工行公钥验证失败\n";
            }
        } else {
            echo "⚠️  工行公钥文件不存在\n";
        }
        
        echo "\n";
    }
    
    /**
     * 生成示例配置
     */
    private function generateExampleConfig(): void
    {
        echo "生成示例配置文件...\n";
        
        $configContent = '<?php

return [
    // 基本配置
    \'app_id\' => \'请填入您的应用ID\',
    \'merchant_id\' => \'请填入您的商户号\',
    \'merchant_protocol_no\' => \'请填入您的商户协议号\',
    
    // 环境设置
    \'environment\' => \'sandbox\', // 沙箱环境，生产环境请改为 \'production\'
    
    // 密钥文件路径
    \'private_key_path\' => \'' . $this->keysPath . '/icbc_private_key.pem\',
    \'icbc_public_key_path\' => \'' . $this->keysPath . '/icbc_public_key.pem\',
    
    // 回调地址
    \'notify_url\' => \'https://your-domain.com/icbc/notify\',
    \'return_url\' => \'https://your-domain.com/icbc/return\',
    
    // 其他配置
    \'sign_type\' => \'RSA2\',
    \'charset\' => \'UTF-8\',
    \'format\' => \'json\',
];
';
        
        $configPath = $this->keysPath . '/example_config.php';
        file_put_contents($configPath, $configContent);
        
        echo "✅ 示例配置文件已生成: {$configPath}\n";
    }
}

// 运行设置向导
if (php_sapi_name() === 'cli') {
    $setup = new IcbcKeySetup();
    $setup->run();
} else {
    echo "请在命令行环境中运行此脚本\n";
}
?> 