<!DOCTYPE html>
<html>
<head>
    <title>停车费支付</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        .payment-container { max-width: 400px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input, .form-group select { width: 100%; padding: 8px; }
        .payment-methods { display: flex; gap: 10px; margin: 15px 0; }
        .payment-method { flex: 1; padding: 15px; border: 2px solid #ddd; text-align: center; cursor: pointer; }
        .payment-method.active { border-color: #007cff; background: #f0f8ff; }
        .btn-pay { width: 100%; padding: 12px; background: #007cff; color: white; border: none; font-size: 16px; }
    </style>
</head>
<body>
    <div class="payment-container">
        <h2>停车费支付</h2>
        
        @if ($errors->any())
            <div style="color: red; margin-bottom: 15px;">
                @foreach ($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif

        <form action="{{ route('icbc-pay.create') }}" method="POST">
            @csrf
            
            <div class="form-group">
                <label>车牌号码</label>
                <input type="text" name="car_number" value="{{ $car_number }}" required>
            </div>
            
            <div class="form-group">
                <label>停车时长(小时)</label>
                <input type="number" name="parking_duration" value="{{ $parking_duration }}" step="0.5">
            </div>
            
            <div class="form-group">
                <label>支付金额(元)</label>
                <input type="number" name="total_amount" value="{{ $amount }}" step="0.01" required>
            </div>
            
            <div class="form-group">
                <label>选择支付方式</label>
                <div class="payment-methods">
                    @foreach($payment_methods as $key => $method)
                        <div class="payment-method" data-method="{{ $key }}">
                            {{ $key == 'alipay' ? '支付宝' : '微信支付' }}
                        </div>
                    @endforeach
                </div>
                <input type="hidden" name="payment_method" required>
            </div>
            
            <button type="submit" class="btn-pay">立即支付</button>
        </form>
    </div>

    <script>
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('active'));
                this.classList.add('active');
                document.querySelector('input[name="payment_method"]').value = this.dataset.method;
            });
        });
    </script>
</body>
</html>
