<?php

declare(strict_types=1);

require_once __DIR__ . '/../src/IcbcPayClient.php';

use IcbcPay\IcbcPayClient;

/**
 * 工商银行支付SDK基本用法示例
 */

try {
    // 初始化配置
    $config = [
        'app_id' => '你的应用ID',
        'merchant_id' => '你的商户号',
        'merchant_protocol_no' => '你的商户协议号',
        'environment' => 'sandbox', // 或 'production'
        'private_key_path' => __DIR__ . '/../../storage/keys/icbc_private_key.pem',
        'icbc_public_key_path' => __DIR__ . '/../../storage/keys/icbc_public_key.pem',
        'notify_url' => 'https://your-domain.com/icbc/notify',
        'return_url' => 'https://your-domain.com/icbc/return',
    ];

    // 创建客户端实例
    $client = new IcbcPayClient($config);

    // 示例1：创建支付订单
    echo "=== 示例1：创建支付订单 ===\n";
    $orderData = [
        'order_id' => 'ORDER_' . time(),
        'amount' => '0.01', // 1分钱测试
        'subject' => '测试商品',
        'body' => '这是一个测试订单',
        'merchant_order_no' => 'MERCHANT_' . time(),
    ];

    $paymentResult = $client->pay($orderData);
    echo "支付结果: " . json_encode($paymentResult, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

    // 示例2：生成支付表单
    echo "=== 示例2：生成支付表单 ===\n";
    $paymentForm = $client->buildForm($orderData);
    echo "支付表单HTML:\n" . $paymentForm . "\n\n";

    // 示例3：创建微信支付
    echo "=== 示例3：创建微信支付 ===\n";
    $wechatResult = $client->wechatPay($orderData);
    echo "微信支付结果: " . json_encode($wechatResult, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

    // 示例4：创建支付宝支付
    echo "=== 示例4：创建支付宝支付 ===\n";
    $alipayResult = $client->alipay($orderData);
    echo "支付宝支付结果: " . json_encode($alipayResult, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

    // 示例5：创建二维码支付
    echo "=== 示例5：创建二维码支付 ===\n";
    $qrResult = $client->qrPay($orderData);
    echo "二维码支付结果: " . json_encode($qrResult, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

    // 示例6：查询支付状态
    echo "=== 示例6：查询支付状态 ===\n";
    $queryResult = $client->query($orderData['order_id']);
    echo "查询结果: " . json_encode($queryResult, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

    // 示例7：检查支付状态
    echo "=== 示例7：检查支付状态 ===\n";
    $status = $client->getStatus($orderData['order_id']);
    $isPaid = $client->isPaid($orderData['order_id']);
    echo "支付状态: {$status}\n";
    echo "是否已支付: " . ($isPaid ? '是' : '否') . "\n\n";

    // 示例8：处理支付回调
    echo "=== 示例8：处理支付回调 ===\n";
    $notifyData = [
        'orderid' => $orderData['order_id'],
        'merordernum' => $orderData['merchant_order_no'],
        'payment_amount' => $orderData['amount'],
        'payment_currency' => 'CNY',
        'payment_status' => '1', // 支付成功
        'trade_no' => 'ICBC_' . time(),
        'pay_time' => date('Y-m-d H:i:s'),
        'sign' => 'mock_signature',
    ];

    $notifyResult = $client->notify($notifyData);
    echo "回调处理结果: " . json_encode($notifyResult, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

    // 示例9：验证回调签名
    echo "=== 示例9：验证回调签名 ===\n";
    $isValid = $client->verifyNotify($notifyData);
    echo "回调签名验证: " . ($isValid ? '通过' : '失败') . "\n\n";

    // 示例10：切换环境
    echo "=== 示例10：切换环境 ===\n";
    $client->sandbox(); // 切换到沙箱环境
    echo "当前环境: " . $client->getConfig('environment') . "\n";
    
    $client->production(); // 切换到生产环境
    echo "当前环境: " . $client->getConfig('environment') . "\n\n";

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

/**
 * HTML页面中的使用示例
 */
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>工商银行支付示例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>工商银行支付示例</h1>
        
        <form id="paymentForm" method="post">
            <div class="form-group">
                <label for="amount">支付金额（元）:</label>
                <input type="number" id="amount" name="amount" step="0.01" min="0.01" value="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="subject">商品名称:</label>
                <input type="text" id="subject" name="subject" value="测试商品" required>
            </div>
            
            <div class="form-group">
                <label for="payment_method">支付方式:</label>
                <select id="payment_method" name="payment_method">
                    <option value="wechat">微信支付</option>
                    <option value="alipay">支付宝支付</option>
                    <option value="unionpay">银联支付</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="payment_type">支付类型:</label>
                <select id="payment_type" name="payment_type">
                    <option value="qrcode">二维码支付</option>
                    <option value="h5">H5支付</option>
                    <option value="app">APP支付</option>
                </select>
            </div>
            
            <button type="button" onclick="createPayment()">创建支付</button>
            <button type="button" onclick="generateForm()">生成支付表单</button>
        </form>
        
        <div id="result" class="result" style="display: none;">
            <h3>支付结果</h3>
            <pre id="resultContent"></pre>
        </div>
    </div>

    <script>
        function createPayment() {
            const formData = new FormData(document.getElementById('paymentForm'));
            const orderData = {
                order_id: 'ORDER_' + Date.now(),
                merchant_order_no: 'MERCHANT_' + Date.now(),
                amount: formData.get('amount'),
                subject: formData.get('subject'),
                payment_method: formData.get('payment_method'),
                payment_type: formData.get('payment_type')
            };
            
            // 这里应该发送AJAX请求到后端处理
            showResult('模拟支付创建成功', orderData);
        }
        
        function generateForm() {
            const formData = new FormData(document.getElementById('paymentForm'));
            const orderData = {
                order_id: 'ORDER_' + Date.now(),
                merchant_order_no: 'MERCHANT_' + Date.now(),
                amount: formData.get('amount'),
                subject: formData.get('subject'),
                payment_method: formData.get('payment_method'),
                payment_type: formData.get('payment_type')
            };
            
            // 这里应该发送AJAX请求到后端生成支付表单
            const mockForm = `
                <form action="https://gw.open.icbc.com.cn/sandbox/api/cardbusiness/aggregatepay/consumepurchase" method="post">
                    <input type="hidden" name="app_id" value="your_app_id">
                    <input type="hidden" name="order_id" value="${orderData.order_id}">
                    <input type="hidden" name="amount" value="${orderData.amount}">
                    <input type="submit" value="立即支付" style="background: #1aad19; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
                </form>
            `;
            
            showResult('支付表单生成成功', mockForm);
        }
        
        function showResult(title, content) {
            document.getElementById('resultContent').textContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
            document.getElementById('result').style.display = 'block';
        }
    </script>
</body>
</html> 