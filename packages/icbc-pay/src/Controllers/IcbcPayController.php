<?php

namespace IcbcPay\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use IcbcPay\Services\IcbcPayService;

class IcbcPayController extends Controller
{
    protected $payService;

    public function __construct(IcbcPayService $payService)
    {
        $this->payService = $payService;
    }

    /**
     * 显示支付页面
     */
    public function showPaymentForm(Request $request)
    {
        $paymentMethods = config('icbc-pay.payment_methods');
        $enabledMethods = array_filter($paymentMethods, function($method) {
            return $method['enabled'];
        });

        return view('icbc-pay::payment-form', [
            'payment_methods' => $enabledMethods,
            'amount' => $request->get('amount', ''),
            'car_number' => $request->get('car_number', ''),
            'parking_duration' => $request->get('parking_duration', '')
        ]);
    }

    /**
     * 创建支付
     */
    public function createPayment(Request $request)
    {
        $request->validate([
            'total_amount' => 'required|numeric|min:0.01',
            'car_number' => 'required|string',
            'payment_method' => 'required|in:alipay,wechat'
        ]);

        try {
            $params = [
                'out_trade_no' => 'PARK_' . date('YmdHis') . mt_rand(1000, 9999),
                'total_amount' => $request->total_amount,
                'subject' => '停车费支付 - 车牌号：' . $request->car_number,
                'body' => '停车费用，车牌：' . $request->car_number,
                'payment_method' => $request->payment_method,
                'car_number' => $request->car_number,
                'parking_duration' => $request->parking_duration ?? 0
            ];

            $result = $this->payService->createPayment($params);

            // 返回支付页面，包含自动提交表单
            return view('icbc-pay::payment-redirect', [
                'gateway_url' => $result['gateway_url'],
                'params' => $result['request_params']
            ]);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => '支付创建失败：' . $e->getMessage()]);
        }
    }

    /**
     * 支付回调通知
     */
    public function paymentNotify(Request $request)
    {
        $params = $request->all();
        
        try {
            $result = $this->payService->handleNotify($params);
            
            if ($result['success']) {
                return response('success');
            } else {
                return response('fail');
            }
        } catch (\Exception $e) {
            \Log::error('支付回调处理失败：' . $e->getMessage(), $params);
            return response('fail');
        }
    }

    /**
     * 支付返回页面
     */
    public function paymentReturn(Request $request)
    {
        $outTradeNo = $request->get('out_trade_no');
        $tradeStatus = $request->get('trade_status');
        
        return view('icbc-pay::payment-result', [
            'status' => $tradeStatus,
            'out_trade_no' => $outTradeNo,
            'message' => $this->getStatusMessage($tradeStatus)
        ]);
    }

    /**
     * 查询支付状态
     */
    public function queryPayment(Request $request)
    {
        $outTradeNo = $request->get('out_trade_no');
        
        if (!$outTradeNo) {
            return response()->json(['error' => '订单号不能为空'], 400);
        }

        try {
            $result = $this->payService->queryPayment($outTradeNo);
            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json(['error' => '查询失败：' . $e->getMessage()], 500);
        }
    }

    private function getStatusMessage($status)
    {
        $messages = [
            'TRADE_SUCCESS' => '支付成功',
            'TRADE_FINISHED' => '交易完成',
            'TRADE_CLOSED' => '交易关闭',
            'WAIT_BUYER_PAY' => '等待支付'
        ];

        return $messages[$status] ?? '未知状态';
    }
}
