<?php

declare(strict_types=1);

namespace IcbcPay\Services;

use Exception;
use InvalidArgumentException;

use Icbc<PERSON>ay\SDK\DefaultIcbcClient;

/**
 * 工商银行支付服务类
 *
 * @package IcbcPay\Services
 * <AUTHOR> Name
 * @version 2.0.0
 */
class IcbcPayService
{
    private DefaultIcbcClient $client;
    
    /**
     * 构造函数
     *
     * @param array $config 配置参数
     */
    public function __construct(array $config)
    {
        $this->validateConfig($config);
        
        $this->client = new \DefaultIcbcClient(
            appId: $config['app_id'],
            privateKey: $this->loadKey($config['private_key_path']),
            signType: $config['sign_type'] ?? 'RSA2',
            charset: $config['charset'] ?? 'UTF-8',
            format: $config['format'] ?? 'json',
            icbcPublicKey: isset($config['icbc_public_key_path']) ? $this->loadKey($config['icbc_public_key_path']) : null,
            encryptKey: $config['encrypt_key'] ?? null,
            encryptType: $config['encrypt_type'] ?? null,
            ca: $config['ca'] ?? null,
            password: $config['password'] ?? null
        );
    }

    /**
     * 创建支付订单
     * 
     * @param array $orderData 订单数据
     * @return array 支付结果
     * @throws Exception 当创建失败时抛出异常
     */
    public function createPayment(array $orderData): array
    {
        $this->validateOrderData($orderData);
        
        $request = [
            'serviceUrl' => $orderData['gateway_url'],
            'method' => 'POST',
            'biz_content' => [
                'orderid' => $orderData['order_id'],
                'payment_amount' => $orderData['amount'],
                'payment_currency' => $orderData['currency'] ?? 'CNY',
                'merordernum' => $orderData['merchant_order_no'],
                'orderdate' => $orderData['order_date'] ?? date('Y-m-d H:i:s'),
                'mer_id' => $orderData['merchant_id'],
                'notify_url' => $orderData['notify_url'] ?? '',
                'return_url' => $orderData['return_url'] ?? '',
                'subject' => $orderData['subject'] ?? '工行支付',
                'body' => $orderData['body'] ?? '',
                'attach' => $orderData['attach'] ?? '',
                'payment_type' => $orderData['payment_type'] ?? '1',
                'order_timeout' => $orderData['timeout'] ?? '30m'
            ],
            'isNeedEncrypt' => $orderData['need_encrypt'] ?? false,
            'extraParams' => $orderData['extra_params'] ?? []
        ];

        try {
            $response = $this->client->execute($request, $this->generateMsgId(), null);
            return $this->parsePaymentResponse($response);
        } catch (Exception $e) {
            throw new Exception("创建支付订单失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 查询支付状态
     *
     * @param string $orderId 订单ID
     * @param string $gatewayUrl 网关URL
     * @return array 查询结果
     * @throws Exception 当查询失败时抛出异常
     */
    public function queryPayment(string $orderId, string $gatewayUrl): array
    {
        $request = [
            'serviceUrl' => $gatewayUrl,
            'method' => 'POST',
            'biz_content' => [
                'orderid' => $orderId
            ],
            'isNeedEncrypt' => false
        ];

        try {
            $response = $this->client->execute($request, $this->generateMsgId(), null);
            return $this->parseQueryResponse($response);
        } catch (Exception $e) {
            throw new Exception("查询支付状态失败: " . $e->getMessage(), 0, $e);
        }
    }

    /**
     * 处理支付回调
     *
     * @param array $notifyData 回调数据
     * @return array 处理结果
     * @throws Exception 当处理失败时抛出异常
     */
    public function handleNotify(array $notifyData): array
    {
        // 验证回调数据的签名
        if (!$this->verifyNotifySignature($notifyData)) {
            throw new Exception("回调数据签名验证失败");
        }

        return [
            'success' => true,
            'order_id' => $notifyData['orderid'] ?? '',
            'merchant_order_no' => $notifyData['merordernum'] ?? '',
            'amount' => $notifyData['payment_amount'] ?? '0',
            'currency' => $notifyData['payment_currency'] ?? 'CNY',
            'status' => $this->mapPaymentStatus($notifyData['payment_status'] ?? ''),
            'trade_no' => $notifyData['trade_no'] ?? '',
            'pay_time' => $notifyData['pay_time'] ?? '',
            'raw_data' => $notifyData
        ];
    }

    /**
     * 生成支付表单
     *
     * @param array $orderData 订单数据
     * @return string HTML表单
     * @throws Exception 当生成失败时抛出异常
     */
    public function buildPaymentForm(array $orderData): string
    {
        $paymentResult = $this->createPayment($orderData);
        
        if (isset($paymentResult['form_html'])) {
            return $paymentResult['form_html'];
        }
        
        // 如果没有直接返回表单HTML，则构建表单
        if (isset($paymentResult['payment_url']) && isset($paymentResult['params'])) {
            return \WebUtils::buildForm($paymentResult['payment_url'], $paymentResult['params']);
        }
        
        throw new Exception("无法生成支付表单");
    }

    /**
     * 验证配置参数
     *
     * @param array $config 配置参数
     * @throws InvalidArgumentException 当配置无效时抛出异常
     */
    private function validateConfig(array $config): void
    {
        $required = ['app_id', 'private_key_path'];
        
        foreach ($required as $key) {
            if (empty($config[$key])) {
                throw new InvalidArgumentException("配置参数 {$key} 不能为空");
            }
        }

        if (!file_exists($config['private_key_path'])) {
            throw new InvalidArgumentException("私钥文件不存在: " . $config['private_key_path']);
        }

        if (isset($config['icbc_public_key_path']) && !file_exists($config['icbc_public_key_path'])) {
            throw new InvalidArgumentException("工行公钥文件不存在: " . $config['icbc_public_key_path']);
        }
    }

    /**
     * 验证订单数据
     *
     * @param array $orderData 订单数据
     * @throws InvalidArgumentException 当订单数据无效时抛出异常
     */
    private function validateOrderData(array $orderData): void
    {
        $required = ['order_id', 'amount', 'merchant_order_no', 'merchant_id', 'gateway_url'];
        
        foreach ($required as $key) {
            if (empty($orderData[$key])) {
                throw new InvalidArgumentException("订单数据 {$key} 不能为空");
            }
        }

        if (!is_numeric($orderData['amount']) || $orderData['amount'] <= 0) {
            throw new InvalidArgumentException("订单金额必须为正数");
        }

        if (!filter_var($orderData['gateway_url'], FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException("网关URL格式无效");
        }
    }

    /**
     * 加载密钥文件
     *
     * @param string $keyPath 密钥文件路径
     * @return string 密钥内容
     * @throws Exception 当加载失败时抛出异常
     */
    private function loadKey(string $keyPath): string
    {
        if (!file_exists($keyPath)) {
            throw new Exception("密钥文件不存在: {$keyPath}");
        }

        $content = file_get_contents($keyPath);
        if ($content === false) {
            throw new Exception("无法读取密钥文件: {$keyPath}");
        }

        return trim($content);
    }

    /**
     * 解析支付响应
     *
     * @param string $response 响应数据
     * @return array 解析结果
     */
    private function parsePaymentResponse(string $response): array
    {
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("支付响应解析失败: " . json_last_error_msg());
        }
            
            return [
                'success' => true,
            'order_id' => $data['orderid'] ?? '',
            'payment_url' => $data['payment_url'] ?? '',
            'qr_code' => $data['qr_code'] ?? '',
            'form_html' => $data['form_html'] ?? '',
            'params' => $data['params'] ?? [],
            'raw_response' => $data
        ];
    }

    /**
     * 解析查询响应
     *
     * @param string $response 响应数据
     * @return array 解析结果
     */
    private function parseQueryResponse(string $response): array
    {
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("查询响应解析失败: " . json_last_error_msg());
        }

        return [
            'success' => true,
            'order_id' => $data['orderid'] ?? '',
            'merchant_order_no' => $data['merordernum'] ?? '',
            'amount' => $data['payment_amount'] ?? '0',
            'status' => $this->mapPaymentStatus($data['payment_status'] ?? ''),
            'trade_no' => $data['trade_no'] ?? '',
            'pay_time' => $data['pay_time'] ?? '',
            'raw_response' => $data
        ];
    }

    /**
     * 验证回调签名
     * 
     * @param array $notifyData 回调数据
     * @return bool 验证结果
     */
    private function verifyNotifySignature(array $notifyData): bool
    {
        // TODO: 实现回调签名验证逻辑
        return true;
    }

    /**
     * 映射支付状态
     *
     * @param string $status 原始状态
     * @return string 标准状态
     */
    private function mapPaymentStatus(string $status): string
    {
        $statusMap = [
            '0' => 'pending',   // 待支付
            '1' => 'success',   // 支付成功
            '2' => 'failed',    // 支付失败
            '3' => 'cancelled', // 已取消
            '4' => 'refunded'   // 已退款
        ];

        return $statusMap[$status] ?? 'unknown';
    }

    /**
     * 生成消息ID
     *
     * @return string 消息ID
     */
    private function generateMsgId(): string
    {
        return date('YmdHis') . str_pad((string)mt_rand(0, 9999), 4, '0', STR_PAD_LEFT);
    }
}
