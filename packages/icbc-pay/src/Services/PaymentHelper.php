<?php

namespace IcbcPay\Services;

class PaymentHelper
{
    /**
     * 生成签名
     */
    public function generateSign($params, $privateKeyPath)
    {
        // 移除sign参数
        unset($params['sign']);
        
        // 排序参数
        ksort($params);
        
        // 构建签名字符串
        $signString = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString = rtrim($signString, '&');
        
        // 读取私钥
        $privateKey = file_get_contents($privateKeyPath);
        $privateKey = openssl_pkey_get_private($privateKey);
        
        // 生成签名
        openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        
        return base64_encode($signature);
    }

    /**
     * 验证签名
     */
    public function verifySign($params, $publicKeyPath)
    {
        $sign = $params['sign'];
        unset($params['sign']);
        
        ksort($params);
        
        $signString = '';
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null) {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString = rtrim($signString, '&');
        
        $publicKey = file_get_contents($publicKeyPath);
        $publicKey = openssl_pkey_get_public($publicKey);
        
        $result = openssl_verify($signString, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);
        
        return $result === 1;
    }

    /**
     * HTTP请求
     */
    public function httpRequest($url, $params)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return json_decode($response, true);
    }
}
