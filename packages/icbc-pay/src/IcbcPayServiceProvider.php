<?php

declare(strict_types=1);

namespace IcbcPay;

use Illuminate\Support\ServiceProvider;
use IcbcPay\IcbcPayClient;

/**
 * 工商银行支付服务提供者
 *
 * @package IcbcPay
 * <AUTHOR> Name
 * @version 2.0.0
 */
class IcbcPayServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register(): void
    {
        // 注册配置文件
        $this->mergeConfigFrom(__DIR__ . '/config/icbc-pay.php', 'icbc-pay');

        // 注册ICBC支付客户端
        $this->app->singleton(IcbcPayClient::class, function ($app) {
            $config = $app['config']['icbc-pay'];
            
            // 确保必要的配置参数存在
            $this->validateConfig($config);
            
            return new IcbcPayClient($config);
        });

        // 注册命令
        if ($this->app->runningInConsole()) {
            $this->commands([
                // 可以在这里添加自定义命令
            ]);
        }
    }

    /**
     * 引导服务
     *
     * @return void
     */
    public function boot(): void
    {
        // 发布配置文件
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__ . '/config/icbc-pay.php' => config_path('icbc-pay.php'),
            ], 'icbc-pay-config');

            // 发布数据库迁移文件
            $this->publishes([
                __DIR__ . '/database/migrations/' => database_path('migrations'),
            ], 'icbc-pay-migrations');

            // 发布视图文件
            $this->publishes([
                __DIR__ . '/resources/views/' => resource_path('views/icbc-pay'),
            ], 'icbc-pay-views');
        }

        // 注册视图
        $this->loadViewsFrom(__DIR__ . '/resources/views', 'icbc-pay');

        // 注册路由
        if (file_exists(__DIR__ . '/routes/web.php')) {
            $this->loadRoutesFrom(__DIR__ . '/routes/web.php');
        }

        // 注册数据库迁移
        if (is_dir(__DIR__ . '/database/migrations')) {
            $this->loadMigrationsFrom(__DIR__ . '/database/migrations');
        }
    }

    /**
     * 验证配置参数
     *
     * @param array $config 配置数组
     * @throws \InvalidArgumentException 当配置无效时抛出异常
     */
    private function validateConfig(array $config): void
    {
        $requiredKeys = ['app_id'];

        foreach ($requiredKeys as $key) {
            if (empty($config[$key])) {
                // 在测试环境中不要求严格的配置验证
                if (!app()->environment(['testing', 'local'])) {
                    throw new \InvalidArgumentException("ICBC支付配置缺少必需参数: {$key}");
                }
            }
        }

        // 检查私钥文件是否存在（仅在非测试环境）
        if (!app()->environment(['testing', 'local']) && !empty($config['private_key_path'])) {
            if (!file_exists($config['private_key_path'])) {
                throw new \InvalidArgumentException("ICBC私钥文件不存在: {$config['private_key_path']}");
            }
        }

        // 检查工行公钥文件（如果配置了，仅在非测试环境）
        if (!app()->environment(['testing', 'local']) && !empty($config['icbc_public_key_path'])) {
            if (!file_exists($config['icbc_public_key_path'])) {
                throw new \InvalidArgumentException("ICBC公钥文件不存在: {$config['icbc_public_key_path']}");
            }
        }
    }

    /**
     * 获取提供的服务
     *
     * @return array
     */
    public function provides(): array
    {
        return [
            IcbcPayClient::class,
        ];
    }
}
