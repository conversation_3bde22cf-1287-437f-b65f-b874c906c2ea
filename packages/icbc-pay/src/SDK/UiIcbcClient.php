<?php
declare(strict_types=1);

namespace IcbcPay\SDK;

/**
 * 工商银行支付SDK UI客户端
 * 
 * @package IcbcPay\SDK
 */
class UiIcbcClient extends DefaultIcbcClient {
	/**
	 * 构造函数
	 *
	 * @param string $appId 应用ID
	 * @param string $privateKey 应用私钥
	 * @param string $signType 签名类型
	 * @param string $charset 字符集
	 * @param string $format 数据格式
	 * @param string $icbcPublicKey 工行公钥
	 * @param string $encryptKey 加密密钥
	 * @param string $encryptType 加密类型
	 * @param string $ca 证书
	 * @param string $password 密码
	 */
	function __construct($appId, $privateKey, $signType, $charset, $format, $icbcPublicKey,
			$encryptKey, $encryptType, $ca, $password)
	{
		parent::__construct($appId, $privateKey, $signType, $charset, $format, $icbcPub<PERSON>Key,
			$encryptKey, $encryptType, $ca, $password);
	}

	/**
	 * 构建POST表单
	 *
	 * @param array $request 请求参数
	 * @param string $msgId 消息ID
	 * @param string $appAuthToken 应用授权令牌
	 * @return string 表单HTML
	 */
	function buildPostForm($request, $msgId, $appAuthToken)
	{
		$params = $this->prepareParams($request, $msgId, null);

		$urlQueryParams = $this->buildUrlQueryParams($params);

		$url = WebUtils::buildGetUrl($request["serviceUrl"], $urlQueryParams, $this->charset);

		return WebUtils::buildForm($url, $this->buildBodyParams($params));
	}

	/**
	 * 构建URL查询参数
	 *
	 * @param array $params 参数数组
	 * @return array URL查询参数
	 */
	function buildUrlQueryParams($params)
	{
        $apiParamNames[] = IcbcConstants::SIGN;
        $apiParamNames[] = IcbcConstants::APP_ID;
        $apiParamNames[] = IcbcConstants::SIGN_TYPE;
        $apiParamNames[] = IcbcConstants::CHARSET;
        $apiParamNames[] = IcbcConstants::FORMAT;
        $apiParamNames[] = IcbcConstants::ENCRYPT_TYPE;
        $apiParamNames[] = IcbcConstants::TIMESTAMP;
        $apiParamNames[] = IcbcConstants::MSG_ID;

		foreach ($params as $key => $value) {
			if(in_array($key, $apiParamNames)){
				$urlQueryParams[$key] = $value;
			}
	   	}
	   	return $urlQueryParams;
	}

	/**
	 * 构建请求体参数
	 *
	 * @param array $params 参数数组
	 * @return array 请求体参数
	 */
	function buildBodyParams($params)
	{
        $apiParamNames[] = IcbcConstants::SIGN;
        $apiParamNames[] = IcbcConstants::APP_ID;
        $apiParamNames[] = IcbcConstants::SIGN_TYPE;
        $apiParamNames[] = IcbcConstants::CHARSET;
        $apiParamNames[] = IcbcConstants::FORMAT;
        $apiParamNames[] = IcbcConstants::ENCRYPT_TYPE;
        $apiParamNames[] = IcbcConstants::TIMESTAMP;
        $apiParamNames[] = IcbcConstants::MSG_ID;

		foreach ($params as $key => $value) {
			if(in_array($key, $apiParamNames)){
				continue;
			}
			$urlQueryParams[$key] = $value;
	   	}

	   	return $urlQueryParams;
	}
}
?>