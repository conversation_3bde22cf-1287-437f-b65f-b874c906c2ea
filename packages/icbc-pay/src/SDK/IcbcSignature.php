<?php
declare(strict_types=1);

namespace IcbcPay\SDK;

use Exception;

/**
 * 工商银行支付SDK签名工具类
 *
 * @package IcbcPay\SDK
 * <AUTHOR> Name
 * @version 2.0.0
 */
class IcbcSignature
{
    /**
     * 生成签名
     *
     * @param string $content 待签名字符串
     * @param string $signType 签名类型 (RSA/RSA2/CA)
     * @param string $privateKey 私钥内容
     * @param string $charset 字符编码
     * @param string|null $password 私钥密码（CA签名时使用）
     * @return string 签名结果
     * @throws Exception 当签名失败时抛出异常
     */
    public static function sign(
        string $content, 
        string $signType, 
        string $privateKey, 
        string $charset = 'UTF-8',
        ?string $password = null
    ): string {
        if (empty($content)) {
            throw new Exception("待签名内容不能为空");
        }

        if (empty($privateKey)) {
            throw new Exception("私钥不能为空");
        }

        // 根据签名类型选择不同的签名方式
        switch (strtoupper($signType)) {
            case IcbcConstants::SIGN_TYPE_RSA:
                return self::rsaSign($content, $privateKey, IcbcConstants::SIGN_SHA1RSA_ALGORITHMS);
                
            case IcbcConstants::SIGN_TYPE_RSA2:
                return self::rsaSign($content, $privateKey, IcbcConstants::SIGN_SHA256RSA_ALGORITHMS);
                
            case IcbcConstants::SIGN_TYPE_CA:
                if (empty($password)) {
                    throw new Exception("CA签名需要提供证书密码");
                }
                return IcbcCa::sign($content, $privateKey, $password);
                
            default:
                throw new Exception("不支持的签名类型: {$signType}");
        }
    }

    /**
     * 验证签名
     *
     * @param string $content 原始内容
     * @param string $signType 签名类型
     * @param string $publicKey 公钥内容
     * @param string $charset 字符编码
     * @param string $sign 签名值
     * @return bool 验证结果
     * @throws Exception 当验证失败时抛出异常
     */
    public static function verify(
        string $content,
        string $signType,
        string $publicKey,
        string $charset = 'UTF-8',
        string $sign = ''
    ): bool {
        if (empty($content)) {
            throw new Exception("待验证内容不能为空");
        }

        if (empty($publicKey)) {
            throw new Exception("公钥不能为空");
        }

        if (empty($sign)) {
            throw new Exception("签名值不能为空");
        }

        // 根据签名类型选择不同的验证方式
        switch (strtoupper($signType)) {
            case IcbcConstants::SIGN_TYPE_RSA:
                return self::rsaVerify($content, $sign, $publicKey, IcbcConstants::SIGN_SHA1RSA_ALGORITHMS);
                
            case IcbcConstants::SIGN_TYPE_RSA2:
                return self::rsaVerify($content, $sign, $publicKey, IcbcConstants::SIGN_SHA256RSA_ALGORITHMS);
                
            case IcbcConstants::SIGN_TYPE_CA:
                return IcbcCa::verify($content, $publicKey, $sign);
                
            default:
                throw new Exception("不支持的签名类型: {$signType}");
        }
    }

    /**
     * RSA签名
     *
     * @param string $content 待签名内容
     * @param string $privateKey 私钥
     * @param string $algorithm 算法
     * @return string 签名结果
     * @throws Exception 当签名失败时抛出异常
     */
    private static function rsaSign(string $content, string $privateKey, string $algorithm): string
    {
        // 格式化私钥
        $formattedPrivateKey = self::formatPrivateKey($privateKey);
        
        // 获取私钥资源
        $privateKeyResource = openssl_pkey_get_private($formattedPrivateKey);
        if (!$privateKeyResource) {
            $error = openssl_error_string();
            throw new Exception("私钥格式错误或无法读取: {$error}");
        }

        // 选择签名算法
        $opensslAlgo = ($algorithm === IcbcConstants::SIGN_SHA256RSA_ALGORITHMS) 
            ? OPENSSL_ALGO_SHA256 
            : OPENSSL_ALGO_SHA1;

        // 执行签名
        $signature = '';
        $result = openssl_sign($content, $signature, $privateKeyResource, $opensslAlgo);
        
        // 释放私钥资源
        openssl_pkey_free($privateKeyResource);

        if (!$result) {
            $error = openssl_error_string();
            throw new Exception("RSA签名失败: {$error}");
        }

        return base64_encode($signature);
    }

    /**
     * RSA验签
     *
     * @param string $content 原始内容
     * @param string $sign 签名值
     * @param string $publicKey 公钥
     * @param string $algorithm 算法
     * @return bool 验证结果
     * @throws Exception 当验证失败时抛出异常
     */
    private static function rsaVerify(string $content, string $sign, string $publicKey, string $algorithm): bool
    {
        // 格式化公钥
        $formattedPublicKey = self::formatPublicKey($publicKey);
        
        // 获取公钥资源
        $publicKeyResource = openssl_pkey_get_public($formattedPublicKey);
        if (!$publicKeyResource) {
            $error = openssl_error_string();
            throw new Exception("公钥格式错误或无法读取: {$error}");
        }

        // 解码签名
        $decodedSignature = base64_decode($sign);
        if ($decodedSignature === false) {
            throw new Exception("签名数据base64解码失败");
        }

        // 选择验证算法
        $opensslAlgo = ($algorithm === IcbcConstants::SIGN_SHA256RSA_ALGORITHMS) 
            ? OPENSSL_ALGO_SHA256 
            : OPENSSL_ALGO_SHA1;

        // 执行验证
        $result = openssl_verify($content, $decodedSignature, $publicKeyResource, $opensslAlgo);
        
        // 释放公钥资源
        openssl_pkey_free($publicKeyResource);

        if ($result === -1) {
            $error = openssl_error_string();
            throw new Exception("RSA验签过程出错: {$error}");
        }

        return $result === 1;
    }

    /**
     * 格式化私钥
     *
     * @param string $privateKey 原始私钥
     * @return string 格式化后的私钥
     */
    private static function formatPrivateKey(string $privateKey): string
    {
        $privateKey = trim($privateKey);
        
        // 如果已经包含完整的PEM格式，直接返回
        if (str_starts_with($privateKey, '-----BEGIN') && str_ends_with($privateKey, '-----')) {
            return $privateKey;
        }
        
        // 移除可能存在的头尾标识
        $privateKey = str_replace([
            '-----BEGIN PRIVATE KEY-----', 
            '-----END PRIVATE KEY-----',
            '-----BEGIN RSA PRIVATE KEY-----', 
            '-----END RSA PRIVATE KEY-----'
        ], '', $privateKey);
        
        // 移除所有空白字符
        $privateKey = preg_replace('/\s+/', '', $privateKey);
        
        // 重新格式化为标准PEM格式
        return "-----BEGIN PRIVATE KEY-----\n" . 
               chunk_split($privateKey, 64, "\n") . 
               "-----END PRIVATE KEY-----";
    }

    /**
     * 格式化公钥
     *
     * @param string $publicKey 原始公钥
     * @return string 格式化后的公钥
     */
    private static function formatPublicKey(string $publicKey): string
    {
        $publicKey = trim($publicKey);
        
        // 如果已经包含完整的PEM格式，直接返回
        if (str_starts_with($publicKey, '-----BEGIN') && str_ends_with($publicKey, '-----')) {
            return $publicKey;
        }
        
        // 移除可能存在的头尾标识
        $publicKey = str_replace([
            '-----BEGIN PUBLIC KEY-----', 
            '-----END PUBLIC KEY-----',
            '-----BEGIN RSA PUBLIC KEY-----', 
            '-----END RSA PUBLIC KEY-----'
        ], '', $publicKey);
        
        // 移除所有空白字符
        $publicKey = preg_replace('/\s+/', '', $publicKey);
        
        // 重新格式化为标准PEM格式
        return "-----BEGIN PUBLIC KEY-----\n" . 
               chunk_split($publicKey, 64, "\n") . 
               "-----END PUBLIC KEY-----";
    }

    /**
     * 验证私钥格式是否正确
     *
     * @param string $privateKey 私钥内容
     * @return bool 验证结果
     */
    public static function validatePrivateKey(string $privateKey): bool
    {
        try {
            $formattedKey = self::formatPrivateKey($privateKey);
            $keyResource = openssl_pkey_get_private($formattedKey);
            
            if ($keyResource) {
                openssl_pkey_free($keyResource);
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 验证公钥格式是否正确
     *
     * @param string $publicKey 公钥内容
     * @return bool 验证结果
     */
    public static function validatePublicKey(string $publicKey): bool
    {
        try {
            $formattedKey = self::formatPublicKey($publicKey);
            $keyResource = openssl_pkey_get_public($formattedKey);
            
            if ($keyResource) {
                openssl_pkey_free($keyResource);
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 测试私钥和公钥是否匹配
     *
     * @param string $privateKey 私钥
     * @param string $publicKey 公钥
     * @param string $algorithm 算法（默认SHA256）
     * @return bool 匹配结果
     */
    public static function testKeyPair(
        string $privateKey, 
        string $publicKey, 
        string $algorithm = IcbcConstants::SIGN_SHA256RSA_ALGORITHMS
    ): bool {
        try {
            $testData = 'test_key_pair_' . time() . '_' . mt_rand(1000, 9999);
            
            // 使用私钥签名
            $signature = self::rsaSign($testData, $privateKey, $algorithm);
            
            // 使用公钥验证
            return self::rsaVerify($testData, $signature, $publicKey, $algorithm);
            
        } catch (Exception $e) {
            return false;
        }
    }
} 