<?php
declare(strict_types=1);

namespace IcbcPay\SDK;

use Exception;

/**
 * 工商银行支付SDK网络工具类
 *
 * @package IcbcPay\SDK
 * <AUTHOR> Name
 * @version 2.0.0
 */
class WebUtils
{
    private static string $version = "v2_20231201";

    /**
     * 执行GET请求
     *
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @param string $charset 字符编码
     * @return string 响应内容
     * @throws Exception 当请求失败时抛出异常
     */
    public static function doGet(string $url, array $params, string $charset): string
    {
        $headers = [
            IcbcConstants::VERSION_HEADER_NAME . ': ' . self::$version,
            'Content-Type: application/json;charset=' . $charset,
            'User-Agent: ICBC-PHP-SDK/' . self::$version
        ];
        
        $getUrl = self::buildGetUrl($url, $params, $charset);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $getUrl);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, IcbcConstants::DEFAULT_CONNECT_TIMEOUT);
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, IcbcConstants::DEFAULT_TIMEOUT);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            throw new Exception("cURL执行失败: {$error}");
        }

        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }

        return $response;
    }

    /**
     * 执行POST请求
     *
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @param string $charset 字符编码
     * @return string 响应内容
     * @throws Exception 当请求失败时抛出异常
     */
    public static function doPost(string $url, array $params, string $charset): string
    {
        $headers = [
            'Expect:',
            IcbcConstants::VERSION_HEADER_NAME . ': ' . self::$version,
            'Content-Type: application/x-www-form-urlencoded;charset=' . $charset,
            'User-Agent: ICBC-PHP-SDK/' . self::$version
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, IcbcConstants::DEFAULT_CONNECT_TIMEOUT);
        curl_setopt($ch, CURLOPT_TIMEOUT_MS, IcbcConstants::DEFAULT_TIMEOUT);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 3);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            throw new Exception("cURL执行失败: {$error}");
        }

        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }

        return $response;
    }

    /**
     * 构建GET请求URL
     *
     * @param string $strUrl 基础URL
     * @param array $params 参数数组
     * @param string $charset 字符编码
     * @return string 完整的GET请求URL
     */
    public static function buildGetUrl(string $strUrl, array $params, string $charset): string
    {
        if (empty($params)) {
            return $strUrl;
        }

        $buildUrlParams = http_build_query($params, '', '&', PHP_QUERY_RFC3986);
        $separator = str_contains($strUrl, '?') ? '&' : '?';
        
        return $strUrl . $separator . $buildUrlParams;
    }

    /**
     * 构建有序的签名字符串
     *
     * @param string $path URL路径
     * @param array $params 参数数组
     * @return string 签名字符串
     */
    public static function buildOrderedSignStr(string $path, array $params): string
    {
        ksort($params);
        $comSignStr = $path . '?';
        $hasParam = false;

        foreach ($params as $key => $value) {
            if ($key === null || $key === '' || $value === null || $value === '') {
                continue;
            }

            if ($hasParam) {
                $comSignStr .= '&';
            } else {
                $hasParam = true;
            }
            $comSignStr .= $key . '=' . $value;
        }

        return $comSignStr;
    }

    /**
     * 构建自动提交表单
     *
     * @param string $url 提交URL
     * @param array $params 表单参数
     * @return string HTML表单字符串
     */
    public static function buildForm(string $url, array $params): string
    {
        $buildedFields = self::buildHiddenFields($params);
        return '<form name="auto_submit_form" method="post" action="' . htmlspecialchars($url, ENT_QUOTES) . '">' . "\n"
            . $buildedFields 
            . '<input type="submit" value="立刻提交" style="display:none">' . "\n"
            . '</form>' . "\n"
            . '<script>document.forms[0].submit();</script>';
    }

    /**
     * 构建隐藏字段
     *
     * @param array $params 参数数组
     * @return string 隐藏字段HTML
     */
    public static function buildHiddenFields(array $params): string
    {
        if (empty($params)) {
            return '';
        }

        $result = '';
        foreach ($params as $key => $value) {
            if ($key === null || $value === null) {
                continue;
            }
            $result .= self::buildHiddenField($key, $value);
        }
        
        return $result;
    }

    /**
     * 构建单个隐藏字段
     *
     * @param string $key 字段名
     * @param string $value 字段值
     * @return string 隐藏字段HTML
     */
    public static function buildHiddenField(string $key, string $value): string
    {
        return '<input type="hidden" name="' . htmlspecialchars($key, ENT_QUOTES) . '" value="' . htmlspecialchars($value, ENT_QUOTES) . '">' . "\n";
    }
} 