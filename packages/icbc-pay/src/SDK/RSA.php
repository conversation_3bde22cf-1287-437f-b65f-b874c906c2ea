<?php
declare(strict_types=1);

namespace IcbcPay\SDK;

use Exception;

/**
 * RSA签名和验签工具类
 *
 * @package IcbcPay\SDK
 * <AUTHOR> Name
 * @version 2.0.0
 */
class RSA
{
	/**
	 * RSA签名
	 *
	 * @param string $content 待签名内容
	 * @param string $privateKey 私钥
	 * @param string $algorithm 签名算法
	 * @return string 签名结果
	 * @throws Exception 当签名失败时抛出异常
	 */
	public static function sign(string $content, string $privateKey, string $algorithm): string
	{
		$signature = '';
		$formattedPrivateKey = self::formatPrivateKey($privateKey);
		
		if ($algorithm === IcbcConstants::SIGN_SHA1RSA_ALGORITHMS) {
			$result = openssl_sign($content, $signature, $formattedPrivateKey, OPENSSL_ALGO_SHA1);
		} elseif ($algorithm === IcbcConstants::SIGN_SHA256RSA_ALGORITHMS) {
			$result = openssl_sign($content, $signature, $formattedPrivateKey, OPENSSL_ALGO_SHA256);
		} else {
			throw new Exception("不支持的签名算法: {$algorithm}，仅支持 SHA1WithRSA 或 SHA256WithRSA");
		}

		if (!$result) {
			$error = openssl_error_string();
			throw new Exception("RSA签名失败: {$error}");
		}

		return base64_encode($signature);
	}

	/**
	 * RSA验签
	 *
	 * @param string $content 待验签内容
	 * @param string $signature 签名数据
	 * @param string $publicKey 公钥
	 * @param string $algorithm 签名算法
	 * @return bool 验签结果
	 * @throws Exception 当验签失败时抛出异常
	 */
	public static function verify(string $content, string $signature, string $publicKey, string $algorithm): bool
	{
		$formattedPublicKey = self::formatPublicKey($publicKey);
		$decodedSignature = base64_decode($signature);
		
		if ($decodedSignature === false) {
			throw new Exception("签名数据base64解码失败");
		}

		if ($algorithm === IcbcConstants::SIGN_SHA1RSA_ALGORITHMS) {
			$result = openssl_verify($content, $decodedSignature, $formattedPublicKey, OPENSSL_ALGO_SHA1);
		} elseif ($algorithm === IcbcConstants::SIGN_SHA256RSA_ALGORITHMS) {
			$result = openssl_verify($content, $decodedSignature, $formattedPublicKey, OPENSSL_ALGO_SHA256);
		} else {
			throw new Exception("不支持的验签算法: {$algorithm}，仅支持 SHA1WithRSA 或 SHA256WithRSA");
		}

		if ($result === -1) {
			$error = openssl_error_string();
			throw new Exception("RSA验签过程出错: {$error}");
		}

		return $result === 1;
	}

	/**
	 * 格式化私钥
	 *
	 * @param string $privateKey 原始私钥
	 * @return string 格式化后的私钥
	 */
	private static function formatPrivateKey(string $privateKey): string
	{
		$privateKey = trim($privateKey);
		
		// 如果已经包含完整的PEM格式，直接返回
		if (str_starts_with($privateKey, '-----BEGIN') && str_ends_with($privateKey, '-----')) {
			return $privateKey;
		}
		
		// 移除可能存在的头尾标识
		$privateKey = str_replace(['-----BEGIN PRIVATE KEY-----', '-----END PRIVATE KEY-----'], '', $privateKey);
		$privateKey = str_replace(['-----BEGIN RSA PRIVATE KEY-----', '-----END RSA PRIVATE KEY-----'], '', $privateKey);
		$privateKey = preg_replace('/\s+/', '', $privateKey);
		
		return "-----BEGIN PRIVATE KEY-----\n" . chunk_split($privateKey, 64, "\n") . "-----END PRIVATE KEY-----";
	}

	/**
	 * 格式化公钥
	 *
	 * @param string $publicKey 原始公钥
	 * @return string 格式化后的公钥
	 */
	private static function formatPublicKey(string $publicKey): string
	{
		$publicKey = trim($publicKey);
		
		// 如果已经包含完整的PEM格式，直接返回
		if (str_starts_with($publicKey, '-----BEGIN') && str_ends_with($publicKey, '-----')) {
			return $publicKey;
		}
		
		// 移除可能存在的头尾标识
		$publicKey = str_replace(['-----BEGIN PUBLIC KEY-----', '-----END PUBLIC KEY-----'], '', $publicKey);
		$publicKey = str_replace(['-----BEGIN RSA PUBLIC KEY-----', '-----END RSA PUBLIC KEY-----'], '', $publicKey);
		$publicKey = preg_replace('/\s+/', '', $publicKey);
		
		return "-----BEGIN PUBLIC KEY-----\n" . chunk_split($publicKey, 64, "\n") . "-----END PUBLIC KEY-----";
	}
} 