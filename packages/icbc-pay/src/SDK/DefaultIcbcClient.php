<?php
declare(strict_types=1);

namespace IcbcPay\SDK;

use Exception;
use InvalidArgumentException;

/**
 * 工商银行支付SDK默认客户端
 *
 * @package IcbcPay\SDK
 * <AUTHOR> Name
 * @version 2.0.0
 */
class DefaultIcbcClient
{
	/**
	 * 构造函数
	 *
	 * @param string $appId 应用ID
	 * @param string $privateKey 应用私钥
	 * @param string|null $signType 签名类型
	 * @param string|null $charset 字符编码
	 * @param string|null $format 数据格式
	 * @param string|null $icbcPublicKey 工行公钥
	 * @param string|null $encryptKey 加密密钥
	 * @param string|null $encryptType 加密类型
	 * @param string|null $ca 证书
	 * @param string|null $password 密码
	 */
	public function __construct(
		public readonly string $appId,
		public readonly string $privateKey,
		public readonly string $signType = IcbcConstants::SIGN_TYPE_RSA,
		public readonly string $charset = IcbcConstants::CHARSET_UTF8,
		public readonly string $format = IcbcConstants::FORMAT_JSON,
		public readonly ?string $icbcPublicKey = null,
		public readonly ?string $encryptKey = null,
		public readonly ?string $encryptType = null,
		public readonly ?string $ca = null,
		public readonly ?string $password = null
	) {
		// 验证必要参数
		if (empty($appId)) {
			throw new InvalidArgumentException("应用ID不能为空");
		}
		if (empty($privateKey)) {
			throw new InvalidArgumentException("应用私钥不能为空");
		}
	}

	/**
	 * 执行API请求
	 *
	 * @param array $request 请求参数
	 * @param string|null $msgId 消息ID
	 * @param string|null $appAuthToken 应用授权令牌
	 * @return string 响应结果
	 * @throws Exception 当请求失败时抛出异常
	 */
	public function execute(array $request, ?string $msgId = null, ?string $appAuthToken = null): string
	{
		// 验证请求参数
		$this->validateRequest($request);
		
		// 准备请求参数
		$params = $this->prepareParams($request, $msgId, $appAuthToken);

		// 发送请求
		try {
			if (($request["method"] ?? "POST") === "GET") {
				$respStr = WebUtils::doGet($request["serviceUrl"], $params, $this->charset);
			} else {
				$respStr = WebUtils::doPost($request["serviceUrl"], $params, $this->charset);
			}
		} catch (Exception $e) {
			throw new Exception("网络请求失败: " . $e->getMessage(), 0, $e);
		}

		// 解析响应
		return $this->parseResponse($respStr, $request["isNeedEncrypt"] ?? false);
	}

	/**
	 * 验证请求参数
	 *
	 * @param array $request 请求参数
	 * @throws InvalidArgumentException 当参数无效时抛出异常
	 */
	private function validateRequest(array $request): void
	{
		if (empty($request["serviceUrl"])) {
			throw new InvalidArgumentException("服务URL不能为空");
		}
		
		if (!filter_var($request["serviceUrl"], FILTER_VALIDATE_URL)) {
			throw new InvalidArgumentException("服务URL格式无效");
		}
		
		$method = $request["method"] ?? "POST";
		if (!in_array($method, ["GET", "POST"], true)) {
			throw new InvalidArgumentException("仅支持GET或POST请求方法");
		}
	}

	/**
	 * 解析响应数据
	 *
	 * @param string $respStr 响应字符串
	 * @param bool $isNeedEncrypt 是否需要解密
	 * @return string 解析后的响应内容
	 * @throws Exception 当解析失败时抛出异常
	 */
	private function parseResponse(string $respStr, bool $isNeedEncrypt): string
	{
		$respData = json_decode($respStr, true);
		if (json_last_error() !== JSON_ERROR_NONE) {
			throw new Exception("响应数据JSON解析失败: " . json_last_error_msg());
		}

		if (!isset($respData[IcbcConstants::RESPONSE_BIZ_CONTENT])) {
			throw new Exception("响应数据缺少业务内容字段");
		}

		if (!isset($respData[IcbcConstants::SIGN])) {
			throw new Exception("响应数据缺少签名字段");
		}

		// 增加了对传回报文中含有中文字符以及反斜杠的转换
		$respBizContentStr = json_encode(
			$respData[IcbcConstants::RESPONSE_BIZ_CONTENT],
			JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
		);
		
		$sign = $respData[IcbcConstants::SIGN];

		// 验证签名
		if ($this->icbcPublicKey) {
			$passed = IcbcSignature::verify(
				$respBizContentStr,
				$this->signType,
				$this->icbcPublicKey,
				$this->charset,
				$sign
			);

			if (!$passed) {
				throw new Exception("工行签名验证失败");
			}
		}

		// 解密处理
		if ($isNeedEncrypt && $this->encryptKey && $this->encryptType) {
			$respBizContentStr = IcbcEncrypt::decryptContent(
				substr($respBizContentStr, 1, strlen($respBizContentStr) - 2),
				$this->encryptType,
				$this->encryptKey,
				$this->charset
			);
		}

		return $respBizContentStr;
	}

	/**
	 * 准备请求参数
	 *
	 * @param array $request 请求配置
	 * @param string|null $msgId 消息ID
	 * @param string|null $appAuthToken 应用授权令牌
	 * @return array 准备好的参数
	 */
	protected function prepareParams(array $request, ?string $msgId, ?string $appAuthToken): array
	{
		$bizContentStr = json_encode($request["biz_content"] ?? [], JSON_UNESCAPED_UNICODE);
		$path = parse_url($request["serviceUrl"], PHP_URL_PATH);

		$params = [];

		// 合并额外参数
		if (!empty($request["extraParams"])) {
			$params = array_merge($params, $request["extraParams"]);
		}

		// 基础参数
		$params[IcbcConstants::APP_ID] = $this->appId;
		$params[IcbcConstants::SIGN_TYPE] = $this->signType;
		$params[IcbcConstants::CHARSET] = $this->charset;
		$params[IcbcConstants::FORMAT] = $this->format;
		
		if ($this->ca) {
			$params[IcbcConstants::CA] = $this->ca;
		}
		
		if ($appAuthToken) {
			$params[IcbcConstants::APP_AUTH_TOKEN] = $appAuthToken;
		}
		
		if ($msgId) {
			$params[IcbcConstants::MSG_ID] = $msgId;
		}

		// 设置时间戳 - 使用北京时间并保证时区正确性
		$originalTimezone = date_default_timezone_get();
		date_default_timezone_set(IcbcConstants::DATE_TIMEZONE);
		$params[IcbcConstants::TIMESTAMP] = date(IcbcConstants::DATE_TIME_FORMAT);
		date_default_timezone_set($originalTimezone);

		// 处理业务内容
		if (($request["isNeedEncrypt"] ?? false) && $this->encryptKey && $this->encryptType) {
			$params[IcbcConstants::ENCRYPT_TYPE] = $this->encryptType;
			$params[IcbcConstants::BIZ_CONTENT_KEY] = IcbcEncrypt::encryptContent(
				$bizContentStr,
				$this->encryptType,
				$this->encryptKey,
				$this->charset
			);
		} else {
			$params[IcbcConstants::BIZ_CONTENT_KEY] = $bizContentStr;
		}

		// 生成签名
		$strToSign = WebUtils::buildOrderedSignStr($path, $params);
		$signedStr = IcbcSignature::sign(
			$strToSign,
			$this->signType,
			$this->privateKey,
			$this->charset,
			$this->password
		);

		$params[IcbcConstants::SIGN] = $signedStr;

		return $params;
	}

	/**
	 * JSON转换（保留兼容性）
	 *
	 * @param array $array 数组数据
	 * @return string JSON字符串
	 * @deprecated 建议使用标准的json_encode
	 */
	public function jsonTranslate(array $array): string
	{
		foreach ($array as $key => $value) {
			$array[$key] = urlencode($value);
		}
		return json_encode($array);
	}

	/**
	 * 编码操作（保留兼容性）
	 *
	 * @param array $array 数组数据
	 * @return array 编码后的数组
	 * @deprecated 建议使用现代的字符编码处理方法
	 */
	public function encodeOperations(array $array): array
	{
		foreach ($array as $key => $value) {
			$array[$key] = urlencode($value);
		}
		return $array;
	}
} 