<?php
declare(strict_types=1);

namespace IcbcPay\SDK;

use Exception;

/**
 * ICBC 加密解密工具类
 */
class IcbcEncrypt {
    /**
     * 加密内容
     *
     * @param string $content 要加密的内容
     * @param string $encryptType 加密类型
     * @param string $encryptKey 加密密钥
     * @param string $charset 字符集
     * @return string 加密后的内容
     * @throws Exception
     */
    public static function encryptContent($content, $encryptType, $encryptKey, $charset) {
        if (IcbcConstants::ENCRYPT_TYPE_AES == $encryptType) {
            return AES::AesEncrypt($content, base64_decode($encryptKey));
        } else {
            throw new Exception("Only support AES encrypt!");
        }
    }

    /**
     * 解密内容
     *
     * @param string $encryptedContent 要解密的内容
     * @param string $encryptType 加密类型
     * @param string $encryptKey 加密密钥
     * @param string $charset 字符集
     * @return string 解密后的内容
     * @throws Exception
     */
    public static function decryptContent($encryptedContent, $encryptType, $encryptKey, $charset) {
        if (IcbcConstants::ENCRYPT_TYPE_AES == $encryptType) {
            return AES::AesDecrypt($encryptedContent, base64_decode($encryptKey));
        } else {
            throw new Exception("Only support AES decrypt!");
        }
    }
}
?>