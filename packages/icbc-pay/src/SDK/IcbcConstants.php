<?php
declare(strict_types=1);

namespace IcbcPay\SDK;

/**
 * 工商银行支付SDK常量定义类
 *
 * @package IcbcPay\SDK
 * <AUTHOR> Name
 * @version 2.0.0
 */
class IcbcConstants
{
	/** 签名类型相关常量 */
	public const SIGN_TYPE = "sign_type";
	public const SIGN_TYPE_RSA = "RSA";
	public const SIGN_TYPE_RSA2 = "RSA2";
	public const SIGN_TYPE_SM2 = "SM2";
	public const SIGN_TYPE_CA = "CA";

	/** 签名算法常量 */
	public const SIGN_SHA1RSA_ALGORITHMS = "SHA1WithRSA";
	public const SIGN_SHA256RSA_ALGORITHMS = "SHA256WithRSA";

	/** 加密类型常量 */
	public const ENCRYPT_TYPE_AES = "AES";

	/** 请求参数常量 */
	public const APP_ID = "app_id";
	public const FORMAT = "format";
	public const TIMESTAMP = "timestamp";
	public const SIGN = "sign";
	public const APP_AUTH_TOKEN = "app_auth_token";
	public const CHARSET = "charset";
	public const NOTIFY_URL = "notify_url";
	public const RETURN_URL = "return_url";
	public const ENCRYPT_TYPE = "encrypt_type";
	public const BIZ_CONTENT_KEY = "biz_content";
	public const CA = "ca";
	public const PASSWORD = "password";
	public const RESPONSE_BIZ_CONTENT = "response_biz_content";
	public const MSG_ID = "msg_id";

	/** 时间格式常量 */
	public const DATE_TIME_FORMAT = "Y-m-d H:i:s";
	public const DATE_TIMEZONE = "Asia/Shanghai";

	/** 字符集常量 */
	public const CHARSET_UTF8 = "UTF-8";
	public const CHARSET_GBK = "GBK";

	/** 数据格式常量 */
	public const FORMAT_JSON = "json";
	public const FORMAT_XML = "xml";

	/** 版本头信息 */
	public const VERSION_HEADER_NAME = "APIGW-VERSION";
	public const SDK_VERSION = "v2_20231201";

	/** 默认配置值 */
	public const DEFAULT_TIMEOUT = 30000;
	public const DEFAULT_CONNECT_TIMEOUT = 8000;
}
?>