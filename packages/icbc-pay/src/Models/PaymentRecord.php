<?php

namespace IcbcPay\Models;

use Illuminate\Database\Eloquent\Model;

class PaymentRecord extends Model
{
    /**
     * 指定模型对应的数据表名
     * 
     * @var string
     */
    protected $table = 'icbc_payment_records';

    protected $fillable = [
        'out_trade_no',
        'trade_no',
        'total_amount',
        'subject',
        'payment_method',
        'car_number',
        'parking_duration',
        'status',
        'paid_at',
        'notify_params'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'notify_params' => 'array'
    ];

    public function getTableName()
    {
        return config('icbc-pay.payment_table', 'icbc_payment_records');
    }

    /**
     * 生成唯一的支付订单号
     * 符合工商银行要求：只能包含数字、大小写字母，长度不超过50字符
     * 
     * @param string $prefix 订单号前缀，默认为 'PARK'（不含下划线）
     * @return string
     */
    public static function generateUniqueOrderNo($prefix = 'PARK')
    {
        $maxRetries = 5;
        $attempts = 0;
        
        // 确保前缀符合工商银行格式要求（只包含字母数字）
        $prefix = preg_replace('/[^A-Za-z0-9]/', '', $prefix);
        
        do {
            // 生成订单号：前缀 + 时间戳 + 微秒 + 随机数
            // 格式：PARK + YmdHis(14位) + 微秒(6位) + 随机数(3位) = 最多27位
            $timestamp = now()->format('YmdHis');
            $microseconds = str_pad(substr(microtime(), 2, 6), 6, '0', STR_PAD_LEFT);
            $random = str_pad(mt_rand(0, 999), 3, '0', STR_PAD_LEFT);
            $orderNo = "{$prefix}{$timestamp}{$microseconds}{$random}";
            
            // 检查长度是否超过50字符
            if (strlen($orderNo) > 50) {
                // 如果超长，缩短前缀或使用更短的格式
                $shortPrefix = substr($prefix, 0, 4); // 最多4位前缀
                $orderNo = "{$shortPrefix}{$timestamp}{$microseconds}{$random}";
            }
            
            // 检查是否已存在
            if (!static::where('out_trade_no', $orderNo)->exists()) {
                return $orderNo;
            }
            
            $attempts++;
            
            // 如果重复，稍作延迟再重试
            if ($attempts < $maxRetries) {
                usleep(1000); // 延迟1毫秒
            }
            
        } while ($attempts < $maxRetries);
        
        // 如果多次重试仍然重复，使用UUID作为后备方案（移除所有非字母数字字符）
        $uuid = str_replace('-', '', \Illuminate\Support\Str::uuid());
        $shortPrefix = substr($prefix, 0, 4);
        $fallbackOrderNo = $shortPrefix . strtoupper(substr($uuid, 0, 46 - strlen($shortPrefix))); // 确保总长度不超过50
        
        return $fallbackOrderNo;
    }
}
