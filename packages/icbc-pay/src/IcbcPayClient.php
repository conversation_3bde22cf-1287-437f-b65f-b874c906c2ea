<?php

declare(strict_types=1);

namespace IcbcPay;

use Exception;
use InvalidArgumentException;
use Icbc<PERSON>ay\SDK\UiIcbcClient;
use IcbcPay\SDK\DefaultIcbcClient;

/**
 * 工商银行支付客户端
 *
 * @package IcbcPay
 * <AUTHOR> Name
 * @version 2.0.0
 */
class IcbcPayClient
{
    private array $config;
    private ?UiIcbcClient $uiClient = null;
    private ?DefaultIcbcClient $apiClient = null;

    // 支付模式常量
    public const MODE_UI = 'ui';           // 有界面模式
    public const MODE_API = 'api';         // 无界面模式

    /**
     * 构造函数
     *
     * @param array $config 配置参数
     */
    public function __construct(array $config = [])
    {
        $this->config = $this->mergeDefaultConfig($config);
        // 延迟初始化客户端，根据实际使用的模式来决定
    }

    /**
     * 初始化UI客户端（有界面模式）
     * 用于页面类型API，会跳转到工行支付页面
     */
    private function initializeUiClient(): void
    {
        if ($this->uiClient !== null) {
            return;
        }

        try {
            $privateKey = $this->loadPrivateKey();
            $this->uiClient = new UiIcbcClient(
                $this->getConfig('app_id') ?? '',
                $privateKey,
                $this->getConfig('sign_type') ?? 'RSA2',
                $this->getConfig('charset') ?? 'UTF-8',
                $this->getConfig('format') ?? 'json',
                null, // 页面类型API不需要网关公钥
                $this->getConfig('encrypt_key') ?? '',
                $this->getConfig('encrypt_type') ?? '',
                null, // CA证书
                null  // CA密码
            );

            \Log::info('🔧 ICBC UI Client initialized for UI mode');
        } catch (Exception $e) {
            \Log::error('Failed to initialize UiIcbcClient: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 初始化API客户端（无界面模式）
     * 用于数据类型API，直接返回支付结果
     */
    private function initializeApiClient(): void
    {
        if ($this->apiClient !== null) {
            return;
        }

        try {
            $privateKey = $this->loadPrivateKey();
            $gatewayPublicKey = $this->loadGatewayPublicKey();

            $this->apiClient = new DefaultIcbcClient(
                $this->getConfig('app_id') ?? '',
                $privateKey,
                $this->getConfig('sign_type') ?? 'RSA2',
                $this->getConfig('charset') ?? 'UTF-8',
                $this->getConfig('format') ?? 'json',
                $gatewayPublicKey, // 无界面模式需要网关公钥验证响应
                $this->getConfig('encrypt_key') ?? '',
                $this->getConfig('encrypt_type') ?? '',
                null, // CA证书
                null  // CA密码
            );

            \Log::info('🔧 ICBC API Client initialized for API mode');
        } catch (Exception $e) {
            \Log::error('Failed to initialize DefaultIcbcClient: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 创建支付订单（自动选择模式）
     * 根据配置自动选择UI模式或API模式
     *
     * @param array $orderData 订单数据
     * @return array 支付结果
     * @throws Exception 当支付创建失败时抛出异常
     */
    public function pay(array $orderData): array
    {
        $mode = $this->getConfig('payment_mode', self::MODE_UI);
        return $this->payWithMode($orderData, $mode);
    }

    /**
     * 使用UI模式创建支付（有界面）
     * 生成支付表单，用户将跳转到工行支付页面
     *
     * @param array $orderData 订单数据
     * @return array 支付结果，包含form_html
     * @throws Exception 当支付创建失败时抛出异常
     */
    public function payWithUi(array $orderData): array
    {
        return $this->payWithMode($orderData, self::MODE_UI);
    }

    /**
     * 使用API模式创建支付（无界面）
     * 直接调用工行API，返回支付结果
     *
     * @param array $orderData 订单数据
     * @return array 支付结果，包含支付状态和跳转信息
     * @throws Exception 当支付创建失败时抛出异常
     */
    public function payWithApi(array $orderData): array
    {
        return $this->payWithMode($orderData, self::MODE_API);
    }

    /**
     * 使用指定模式创建支付
     *
     * @param array $orderData 订单数据
     * @param string $mode 支付模式 (ui|api)
     * @return array 支付结果
     * @throws Exception 当支付创建失败时抛出异常
     */
    private function payWithMode(array $orderData, string $mode): array
    {
        $payId = uniqid('PAY_');

        \Log::info("💳 ICBC PAY: Starting payment creation with {$mode} mode", [
            'pay_id' => $payId,
            'mode' => $mode,
            'order_data' => $orderData,
            'timestamp' => now()->format('Y-m-d H:i:s.u'),
        ]);

        $normalized = $this->normalizeOrderData($orderData);

        \Log::info('🔄 ICBC PAY: Order data normalized', [
            'pay_id' => $payId,
            'normalized_data' => $normalized,
        ]);

        // 根据模式选择不同的处理方式
        if ($mode === self::MODE_UI) {
            $result = $this->processUiPayment($normalized, $payId);
        } else {
            $result = $this->processApiPayment($normalized, $payId);
        }

        \Log::info("✅ ICBC PAY: Payment created successfully with {$mode} mode", [
            'pay_id' => $payId,
            'order_id' => $result['order_id'],
            'payment_completed' => now()->format('Y-m-d H:i:s.u'),
        ]);

        return $result;
    }

    /**
     * 处理UI模式支付（有界面）
     * 生成支付表单，用户跳转到工行支付页面
     *
     * @param array $orderData 标准化的订单数据
     * @param string $payId 支付ID
     * @return array 支付结果
     * @throws Exception 当处理失败时抛出异常
     */
    private function processUiPayment(array $orderData, string $payId): array
    {
        $this->initializeUiClient();

        \Log::info('🖥️ ICBC UI: Processing UI payment', [
            'pay_id' => $payId,
            'order_id' => $orderData['order_id'],
        ]);

        // 构建UI模式的支付表单
        $formHtml = $this->buildUiForm($orderData);

        return [
            'success' => true,
            'mode' => self::MODE_UI,
            'order_id' => $orderData['order_id'],
            'payment_url' => $this->buildPaymentUrl($orderData),
            'qr_code' => $this->generateQrCode($orderData),
            'form_html' => $formHtml,
            'redirect_required' => true,
            'message' => '请在跳转的工行支付页面完成支付',
        ];
    }

    /**
     * 处理API模式支付（无界面）
     * 直接调用工行API，返回支付结果
     *
     * @param array $orderData 标准化的订单数据
     * @param string $payId 支付ID
     * @return array 支付结果
     * @throws Exception 当处理失败时抛出异常
     */
    private function processApiPayment(array $orderData, string $payId): array
    {
        $this->initializeApiClient();

        \Log::info('🔌 ICBC API: Processing API payment', [
            'pay_id' => $payId,
            'order_id' => $orderData['order_id'],
        ]);

        // 构建API模式的支付请求
        $apiResult = $this->callApiPayment($orderData);

        return [
            'success' => true,
            'mode' => self::MODE_API,
            'order_id' => $orderData['order_id'],
            'trade_no' => $apiResult['trade_no'] ?? null,
            'payment_status' => $apiResult['status'] ?? 'pending',
            'payment_url' => $apiResult['payment_url'] ?? null,
            'qr_code' => $apiResult['qr_code'] ?? null,
            'redirect_required' => false,
            'message' => '支付请求已提交，请查看支付状态',
            'api_response' => $apiResult,
        ];
    }

    /**
     * 查询支付状态
     *
     * @param string $orderId 订单ID
     * @return array 查询结果
     * @throws Exception 当查询失败时抛出异常
     */
    public function query(string $orderId): array
    {
        // 模拟查询结果（在实际环境中这里会调用工行查询API）
        return [
            'success' => true,
            'order_id' => $orderId,
            'status' => 'pending', // pending, success, failed
            'trade_no' => null,
            'amount' => '0.01',
            'paid_at' => null,
        ];
    }

    /**
     * 处理支付回调
     *
     * @param array $notifyData 回调数据
     * @return array 处理结果
     * @throws Exception 当处理失败时抛出异常
     */
    public function notify(array $notifyData): array
    {
        // 模拟回调处理（在实际环境中这里会验证签名）
        return [
            'success' => true,
            'order_id' => $notifyData['orderid'] ?? '',
            'trade_no' => $notifyData['trade_no'] ?? 'MOCK_' . time(),
            'amount' => $notifyData['payment_amount'] ?? '0.01',
            'status' => 'success',
        ];
    }

    /**
     * 生成支付表单（兼容方法，默认使用UI模式）
     *
     * @param array $orderData 订单数据
     * @return string HTML表单
     * @throws Exception 当生成失败时抛出异常
     */
    public function buildForm(array $orderData): string
    {
        return $this->buildUiForm($orderData);
    }

    /**
     * 构建UI模式支付表单
     * 使用UiIcbcClient构建页面类型API表单
     *
     * @param array $orderData 订单数据
     * @return string HTML表单
     * @throws Exception 当生成失败时抛出异常
     */
    private function buildUiForm(array $orderData): string
    {
        $this->initializeUiClient();

        $buildId = uniqid('UI_BUILD_');

        \Log::info('🏗️ ICBC UI FORM: Starting UI payment form build', [
            'build_id' => $buildId,
            'order_data' => $orderData,
            'timestamp' => now()->format('Y-m-d H:i:s.u'),
        ]);

        $normalized = $this->normalizeOrderData($orderData);

        // 构建UI模式的请求参数
        $request = $this->buildUiApiRequest($normalized);
        $msgId = $this->generateMsgId();

        \Log::info('� ICBC UI FORM: UI API request prepared', [
            'build_id' => $buildId,
            'msg_id' => $msgId,
            'service_url' => $request['serviceUrl'],
        ]);

        // 使用UiIcbcClient构建表单
        $form = $this->uiClient->buildPostForm($request, $msgId, null);

        \Log::info('✅ ICBC UI FORM: UI payment form built successfully', [
            'build_id' => $buildId,
            'form_length' => strlen($form),
            'build_completed' => now()->format('Y-m-d H:i:s.u'),
        ]);

        return $form;
    }

    /**
     * 调用API模式支付
     * 使用DefaultIcbcClient直接调用工行API
     *
     * @param array $orderData 订单数据
     * @return array API响应结果
     * @throws Exception 当调用失败时抛出异常
     */
    private function callApiPayment(array $orderData): array
    {
        $this->initializeApiClient();

        $callId = uniqid('API_CALL_');

        \Log::info('🔌 ICBC API CALL: Starting API payment call', [
            'call_id' => $callId,
            'order_data' => $orderData,
            'timestamp' => now()->format('Y-m-d H:i:s.u'),
        ]);

        // 构建API模式的请求参数
        $request = $this->buildDirectApiRequest($orderData);
        $msgId = $this->generateMsgId();

        \Log::info('📋 ICBC API CALL: Direct API request prepared', [
            'call_id' => $callId,
            'msg_id' => $msgId,
            'service_url' => $request['serviceUrl'],
        ]);

        try {
            // 使用DefaultIcbcClient调用API
            $response = $this->apiClient->execute($request, $msgId, null);

            \Log::info('✅ ICBC API CALL: API payment call successful', [
                'call_id' => $callId,
                'response_code' => $response['return_code'] ?? 'unknown',
                'call_completed' => now()->format('Y-m-d H:i:s.u'),
            ]);

            return $this->parseApiResponse($response);

        } catch (Exception $e) {
            \Log::error('❌ ICBC API CALL: API payment call failed', [
                'call_id' => $callId,
                'error' => $e->getMessage(),
                'error_file' => $e->getFile() . ':' . $e->getLine(),
            ]);

            throw $e;
        }
    }

    /**
     * 构建API请求参数（兼容方法，默认使用UI模式）
     *
     * @param array $orderData 订单数据
     * @return array API请求参数
     */
    private function buildApiRequest(array $orderData): array
    {
        return $this->buildUiApiRequest($orderData);
    }

    /**
     * 构建UI模式API请求参数
     * 用于页面类型API（/ui路径）
     *
     * @param array $orderData 订单数据
     * @return array UI API请求参数
     */
    private function buildUiApiRequest(array $orderData): array
    {
        // UI模式使用/ui路径
        $gatewayUrl = $this->getGatewayUrl('ui_payment_url', 'payment_url');

        // 准备UI模式业务内容
        $bizContent = $this->buildCommonBizContent($orderData);

        // UI模式特有字段
        $bizContent['result_type'] = '1'; // UI模式返回页面

        return [
            'serviceUrl' => $gatewayUrl,
            'method' => 'POST',
            'biz_content' => $bizContent,
        ];
    }

    /**
     * 构建API模式请求参数
     * 用于数据类型API（/api路径）
     *
     * @param array $orderData 订单数据
     * @return array API请求参数
     */
    private function buildDirectApiRequest(array $orderData): array
    {
        // API模式使用/api路径
        $gatewayUrl = $this->getGatewayUrl('api_payment_url', 'payment_url');

        // 准备API模式业务内容
        $bizContent = $this->buildCommonBizContent($orderData);

        // API模式特有字段
        $bizContent['result_type'] = '0'; // API模式返回JSON数据

        // API模式可能需要额外的字段
        if ($orderData['payment_method'] === 'wechat') {
            $bizContent['shop_appid'] = $this->getConfig('wechat_app_id', '');
        } elseif ($orderData['payment_method'] === 'alipay') {
            $bizContent['shop_appid'] = $this->getConfig('alipay_app_id', '');
        }

        return [
            'serviceUrl' => $gatewayUrl,
            'method' => 'POST',
            'biz_content' => $bizContent,
        ];
    }

    /**
     * 构建通用业务内容
     * 两种模式共用的字段
     *
     * @param array $orderData 订单数据
     * @return array 业务内容
     */
    private function buildCommonBizContent(array $orderData): array
    {
        return [
            'mer_id' => (string)($this->getConfig('mer_id') ?? ''),
            'mer_prtcl_no' => (string)($this->getConfig('mer_prtcl_no') ?? ''),
            'out_trade_no' => (string)($orderData['order_id'] ?? ''),
            'order_amt' => $this->convertToFen($orderData['amount'] ?? '0'),
            'pay_mode' => $this->getPayModeByMethod($orderData['payment_method'] ?? 'wechat'),
            'access_type' => '1',
            'notify_url' => (string)($this->getConfig('notify_url') ?? ''),
            'mer_url' => (string)($this->getConfig('notify_url') ?? ''),
            'goods_body' => (string)($orderData['subject'] ?? ''),
            'goods_detail' => (string)($orderData['body'] ?? $orderData['subject'] ?? ''),
            'expire_time' => $this->getIcbcTimestamp(time() + 1800),
            'page_url' => (string)($this->getConfig('return_url') ?? ''),
            'return_url' => (string)($this->getConfig('return_url') ?? ''),
            'currency' => 'CNY',
            // 工行必填字段
            'saledepname' => (string)($orderData['subject'] ?? '停车费支付'),
            'body' => (string)($orderData['body'] ?? $orderData['subject'] ?? '停车费支付'),
            'subject' => (string)($orderData['subject'] ?? '停车费支付'),
            'notify_type' => 'HS',
        ];
    }

    /**
     * 生成消息ID
     * 格式：yyyyMMddHHmmss + 10位随机数
     *
     * @return string 消息ID
     */
    private function generateMsgId(): string
    {
        $currentTime = $this->getSyncTime();
        return $this->getMsgIdTimestamp($currentTime) . sprintf('%010d', mt_rand(0, 9999999999));
    }

    /**
     * 解析API响应结果
     *
     * @param array $response API响应
     * @return array 解析后的结果
     */
    private function parseApiResponse(array $response): array
    {
        $returnCode = $response['return_code'] ?? '';
        $returnMsg = $response['return_msg'] ?? '';

        if ($returnCode === '0') {
            // 支付成功
            return [
                'status' => 'success',
                'trade_no' => $response['trade_no'] ?? null,
                'payment_url' => $response['payment_url'] ?? null,
                'qr_code' => $response['qr_code'] ?? null,
                'message' => $returnMsg,
                'raw_response' => $response,
            ];
        } elseif ($returnCode === '1') {
            // 支付处理中
            return [
                'status' => 'pending',
                'trade_no' => $response['trade_no'] ?? null,
                'payment_url' => $response['payment_url'] ?? null,
                'qr_code' => $response['qr_code'] ?? null,
                'message' => $returnMsg,
                'raw_response' => $response,
            ];
        } else {
            // 支付失败
            return [
                'status' => 'failed',
                'error_code' => $returnCode,
                'error_message' => $returnMsg,
                'raw_response' => $response,
            ];
        }
    }

    /**
     * 加载私钥
     *
     * @return string 私钥内容
     * @throws Exception 当私钥加载失败时抛出异常
     */
    private function loadPrivateKey(): string
    {
        $keyPath = $this->getConfig('private_key_path');
        if (!file_exists($keyPath)) {
            throw new Exception("私钥文件不存在: {$keyPath}");
        }

        $keyContent = file_get_contents($keyPath);
        if ($keyContent === false) {
            throw new Exception("无法读取私钥文件: {$keyPath}");
        }

        return $keyContent;
    }

    /**
     * 加载网关公钥
     *
     * @return string 网关公钥内容
     * @throws Exception 当公钥加载失败时抛出异常
     */
    private function loadGatewayPublicKey(): string
    {
        $keyPath = $this->getConfig('gateway_public_key_path');
        if (!$keyPath || !file_exists($keyPath)) {
            // 如果没有配置网关公钥，返回空字符串（某些场景下可能不需要）
            \Log::warning('Gateway public key not configured or file not found', [
                'key_path' => $keyPath
            ]);
            return '';
        }

        $keyContent = file_get_contents($keyPath);
        if ($keyContent === false) {
            throw new Exception("无法读取网关公钥文件: {$keyPath}");
        }

        return $keyContent;
    }

    /**
     * 创建二维码支付
     *
     * @param array $orderData 订单数据
     * @return array 包含二维码的支付结果
     * @throws Exception 当创建失败时抛出异常
     */
    public function qrPay(array $orderData): array
    {
        $orderData['payment_type'] = 'qrcode';
        return $this->pay($orderData);
    }

    /**
     * 创建H5支付
     *
     * @param array $orderData 订单数据
     * @return array H5支付结果
     * @throws Exception 当创建失败时抛出异常
     */
    public function h5Pay(array $orderData): array
    {
        $orderData['payment_type'] = 'h5';
        return $this->pay($orderData);
    }

    /**
     * 创建APP支付
     *
     * @param array $orderData 订单数据
     * @return array APP支付结果
     * @throws Exception 当创建失败时抛出异常
     */
    public function appPay(array $orderData): array
    {
        $orderData['payment_type'] = 'app';
        return $this->pay($orderData);
    }

    /**
     * 创建微信支付
     *
     * @param array $orderData 订单数据
     * @return array 微信支付结果
     * @throws Exception 当创建失败时抛出异常
     */
    public function wechatPay(array $orderData): array
    {
        $orderData['payment_method'] = 'wechat';
        return $this->pay($orderData);
    }

    /**
     * 创建支付宝支付
     *
     * @param array $orderData 订单数据
     * @return array 支付宝支付结果
     * @throws Exception 当创建失败时抛出异常
     */
    public function alipay(array $orderData): array
    {
        $orderData['payment_method'] = 'alipay';
        return $this->pay($orderData);
    }

    /**
     * 验证回调签名
     *
     * @param array $notifyData 回调数据
     * @return bool 验证结果
     */
    public function verifyNotify(array $notifyData): bool
    {
        try {
            $result = $this->notify($notifyData);
            return $result['success'] ?? false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 获取支付状态
     *
     * @param string $orderId 订单ID
     * @return string 支付状态
     */
    public function getStatus(string $orderId): string
    {
        try {
            $result = $this->query($orderId);
            return $result['status'] ?? 'unknown';
        } catch (Exception $e) {
            return 'error';
        }
    }

    /**
     * 检查订单是否已支付
     *
     * @param string $orderId 订单ID
     * @return bool 是否已支付
     */
    public function isPaid(string $orderId): bool
    {
        return $this->getStatus($orderId) === 'success';
    }

    /**
     * 构建支付表单
     *
     * @param array $orderData 订单数据
     * @param string $buildId 构建ID
     * @return string HTML表单
     */
    private function buildPaymentForm(array $orderData, string $buildId = null): string
    {
        $formId = $buildId ?: uniqid('FORM_');
        
        \Log::info('🎯 ICBC FORM BUILD: Starting form construction', [
            'form_id' => $formId,
            'order_data' => $orderData,
            'start_time' => now()->format('Y-m-d H:i:s.u'),
        ]);

        $gatewayUrl = $this->getGatewayUrl('payment_url');
        $htmlFormId = 'icbcPayForm_' . uniqid();
        
        \Log::info('🌐 ICBC FORM BUILD: Gateway URL obtained', [
            'form_id' => $formId,
            'gateway_url' => $gatewayUrl,
            'html_form_id' => $htmlFormId,
        ]);
        
        $html = '<form id="' . $htmlFormId . '" method="POST" action="' . htmlspecialchars($gatewayUrl) . '">';
        
        // 获取同步时间戳
        $currentTime = $this->getSyncTime();
        
        \Log::info('⏰ ICBC FORM BUILD: Time synchronization', [
            'form_id' => $formId,
            'sync_time' => $currentTime,
            'sync_time_readable' => date('Y-m-d H:i:s', $currentTime),
            'timezone_before' => date_default_timezone_get(),
        ]);
        
        // 使用北京时间格式生成MSG ID (工行要求的格式：yyyyMMddHHmmss + 10位随机数)
        $msgId = $this->getMsgIdTimestamp($currentTime) . sprintf('%010d', mt_rand(0, 9999999999));
        
        // 使用工行专用的北京时间戳格式
        $timestamp = $this->getIcbcTimestamp($currentTime);
        
        \Log::info('🆔 ICBC FORM BUILD: Timestamps generated', [
            'form_id' => $formId,
            'msg_id' => $msgId,
            'msg_id_time_part' => substr($msgId, 0, 14),
            'timestamp' => $timestamp,
            'expire_time' => $this->getIcbcTimestamp($currentTime + 1800),
            'time_generation_method' => 'getSyncTime + getIcbcTimestamp',
        ]);
        
        // 准备业务内容
        $bizContent = [
            'mer_id' => (string)($this->getConfig('mer_id') ?? ''),
            'mer_prtcl_no' => (string)($this->getConfig('mer_prtcl_no') ?? ''),
            'out_trade_no' => (string)($orderData['order_id'] ?? ''),
            'order_amt' => $this->convertToFen($orderData['amount'] ?? '0'),
            'pay_mode' => $this->getPayModeByMethod($orderData['payment_method'] ?? 'wechat'),
            'access_type' => '1',
            'notify_url' => (string)($this->getConfig('notify_url') ?? ''), // 修复：使用notify_url字段
            'mer_url' => (string)($this->getConfig('notify_url') ?? ''), // 保留mer_url以兼容
            'goods_body' => (string)($orderData['subject'] ?? ''),
            'goods_detail' => (string)($orderData['body'] ?? $orderData['subject'] ?? ''),
            'expire_time' => $this->getIcbcTimestamp($currentTime + 1800), // 30分钟后过期
            'page_url' => (string)($this->getConfig('return_url') ?? ''),
            'return_url' => (string)($this->getConfig('return_url') ?? ''), // 添加return_url字段
            'currency' => 'CNY',
            // 工行必填字段
            'saledepname' => (string)($orderData['subject'] ?? '停车费支付'), // 销售部门名称
            'body' => (string)($orderData['body'] ?? $orderData['subject'] ?? '停车费支付'), // 订单描述
            'subject' => (string)($orderData['subject'] ?? '停车费支付'), // 订单标题
            'notify_type' => 'HS', // 回调类型
            'result_type' => '1', // 结果类型
        ];

        \Log::info('📋 ICBC FORM BUILD: Business content prepared', [
            'form_id' => $formId,
            'biz_content' => $bizContent,
        ]);
        
        // 添加表单字段 - 按照工商银行API文档要求
        $fields = [
            'app_id' => (string)($this->getConfig('app_id') ?? ''),
            'msg_id' => $msgId,
            'format' => 'json',
            'charset' => 'UTF-8',
            'sign_type' => 'RSA2',
            'timestamp' => $timestamp,
            'version' => '1.0.0',
            'biz_content' => json_encode($bizContent, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
        ];
        
        \Log::info('📤 ICBC FORM BUILD: Request fields prepared', [
            'form_id' => $formId,
            'fields' => $fields,
            'biz_content_length' => strlen($fields['biz_content']),
        ]);
        
        foreach ($fields as $name => $value) {
            if ($value !== '') {
                $html .= '<input type="hidden" name="' . htmlspecialchars($name) . '" value="' . htmlspecialchars($value) . '">';
            }
        }
        
        // 添加签名
        \Log::info('🔐 ICBC FORM BUILD: Starting signature generation', [
            'form_id' => $formId,
            'sign_start' => now()->format('Y-m-d H:i:s.u'),
        ]);

        $signature = $this->generateSignature($fields);
        
        \Log::info('✅ ICBC FORM BUILD: Signature generated successfully', [
            'form_id' => $formId,
            'signature' => substr($signature, 0, 50) . '...',
            'signature_length' => strlen($signature),
            'sign_end' => now()->format('Y-m-d H:i:s.u'),
        ]);

        $html .= '<input type="hidden" name="sign" value="' . htmlspecialchars($signature) . '">';
        
        $html .= '</form>';
        
        \Log::info('🎉 ICBC FORM BUILD: Form construction completed', [
            'form_id' => $formId,
            'html_length' => strlen($html),
            'completion_time' => now()->format('Y-m-d H:i:s.u'),
            'gateway_url' => $gatewayUrl,
            'total_fields' => count($fields) + 1, // +1 for signature
        ]);
        
        return $html;
    }

    /**
     * 根据支付方式获取pay_mode
     *
     * @param string $paymentMethod 支付方式
     * @return string pay_mode值
     */
    private function getPayModeByMethod(string $paymentMethod): string
    {
        $payModes = [
            'wechat' => '9',      // 微信支付
            'alipay' => '10',     // 支付宝支付
            'unionpay' => '8',    // 银联支付
            'icbc' => '1',        // 工行支付
        ];
        
        return $payModes[$paymentMethod] ?? '9'; // 默认微信支付
    }

    /**
     * 将金额从元转换为分
     *
     * @param string|float|int $amount 金额（元为单位）
     * @return string 金额（分为单位）
     * @throws InvalidArgumentException 当金额格式无效时抛出异常
     */
    private function convertToFen($amount): string
    {
        // 确保金额是有效的数字
        if (!is_numeric($amount)) {
            throw new InvalidArgumentException("无效的金额格式: {$amount}");
        }
        
        // 转换为浮点数
        $amountFloat = (float)$amount;
        
        // 检查金额是否为负数
        if ($amountFloat < 0) {
            throw new InvalidArgumentException("金额不能为负数: {$amount}");
        }
        
        // 检查金额精度（最多两位小数）
        $amountStr = (string)$amount;
        $decimalPos = strpos($amountStr, '.');
        $decimalPlaces = $decimalPos !== false ? strlen(substr($amountStr, $decimalPos + 1)) : 0;
        if ($decimalPlaces > 2) {
            throw new InvalidArgumentException("金额精度不能超过两位小数: {$amount}");
        }
        
        // 转换为分（乘以100并四舍五入到整数）
        $fenAmount = round($amountFloat * 100);
        
        \Log::info('💰 ICBC AMOUNT CONVERSION: Yuan to Fen', [
            'original_amount_yuan' => $amount,
            'converted_amount_fen' => $fenAmount,
            'amount_type' => gettype($amount),
            'precision_check' => $decimalPlaces <= 2 ? 'passed' : 'failed',
        ]);
        
        return (string)$fenAmount;
    }

    /**
     * 构建支付URL
     *
     * @param array $orderData 订单数据
     * @return string 支付URL
     */
    private function buildPaymentUrl(array $orderData): string
    {
        $gatewayUrl = $this->getGatewayUrl('payment_url');
        $params = [
            'app_id' => $this->getConfig('app_id'),
            'out_trade_no' => $orderData['order_id'],
            'total_amount' => $orderData['amount'],
            'subject' => $orderData['subject'],
        ];
        
        return $gatewayUrl . '?' . http_build_query($params);
    }

    /**
     * 生成二维码
     *
     * @param array $orderData 订单数据
     * @return string 二维码数据
     */
    private function generateQrCode(array $orderData): string
    {
        // 模拟二维码生成
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }

    /**
     * 生成签名
     * 按照工商银行RSA2签名规范
     * 根据官方文档：需要在待签名字符串前面加上请求的URI
     *
     * @param array $data 待签名数据
     * @return string 签名
     */
    private function generateSignature(array $data): string
    {
        try {
            // 1. 过滤空值和sign字段
            $filteredData = [];
            foreach ($data as $key => $value) {
                if ($key !== 'sign' && $value !== '' && $value !== null) {
                    $filteredData[$key] = $value;
                }
            }
            
            // 2. 按照ASCII码从小到大排序
            ksort($filteredData);
            
            // 3. 构建查询字符串 - 工行格式：key=value&key=value
            $queryParts = [];
            foreach ($filteredData as $key => $value) {
                // 确保值是字符串类型，并且处理中文字符编码
                $valueStr = is_string($value) ? $value : json_encode($value, JSON_UNESCAPED_UNICODE);
                $queryParts[] = $key . '=' . $valueStr;
            }
            $queryString = implode('&', $queryParts);
            
            // 4. 获取请求URI（工商银行的关键要求）
            $requestUri = $this->getRequestUri();
            
            // 5. 构建完整的待签名字符串：URI + "?" + 查询参数
            $signString = $requestUri . '?' . $queryString;
            
            // 记录签名字符串用于调试
            error_log("工行待签名字符串: " . $signString);
            error_log("工行请求URI: " . $requestUri);
            error_log("工行查询字符串: " . $queryString);
            error_log("签名字符串长度: " . strlen($signString));
            error_log("签名字符串字符编码: " . mb_detect_encoding($signString));
            
            // 6. 获取私钥
            $privateKeyPath = $this->getConfig('private_key_path');
            if (!file_exists($privateKeyPath)) {
                throw new Exception("私钥文件不存在: {$privateKeyPath}");
            }
            
            $privateKeyContent = file_get_contents($privateKeyPath);
            error_log("私钥文件已读取，长度: " . strlen($privateKeyContent));
            
            // 处理私钥格式 - 确保包含完整的PEM头尾
            if (strpos($privateKeyContent, '-----BEGIN') === false) {
                $privateKeyContent = "-----BEGIN RSA PRIVATE KEY-----\n" . 
                                   chunk_split($privateKeyContent, 64, "\n") . 
                                   "-----END RSA PRIVATE KEY-----\n";
            }
            
            $privateKey = openssl_pkey_get_private($privateKeyContent);
            
            if (!$privateKey) {
                throw new Exception("私钥格式错误或无法读取: " . openssl_error_string());
            }
            
            // 7. 使用RSA2(SHA256withRSA)算法生成签名
            $signature = '';
            if (!openssl_sign($signString, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
                throw new Exception("签名生成失败: " . openssl_error_string());
            }
            
            openssl_pkey_free($privateKey);
            
            // 8. Base64编码
            $base64Signature = base64_encode($signature);
            
            error_log("工行签名结果: " . $base64Signature . "，长度: " . strlen($base64Signature));
            
            return $base64Signature;
            
        } catch (Exception $e) {
            error_log("工行RSA签名失败: " . $e->getMessage());
            
            // 如果是开发模式，返回模拟签名
            if ($this->getConfig('dev.mock_enabled', false)) {
                ksort($data);
                $mockString = http_build_query($data);
                return 'MOCK_' . md5($mockString);
            }
            
            throw $e;
        }
    }

    /**
     * 获取工商银行API请求URI
     * 根据配置的环境和API类型返回对应的URI路径
     * 
     * @return string 请求URI路径
     */
    private function getRequestUri(): string
    {
        $environment = $this->getConfig('environment');
        $gateway = $this->getConfig("gateways.{$environment}") ?? $this->getConfig('gateways.sandbox');
        
        // 获取支付接口的URI路径
        $paymentPath = $gateway['payment_url'] ?? '/api/cardbusiness/aggregatepay/consumepurchase';
        
        // 确保路径格式正确
        return ltrim($paymentPath, '/') ? '/' . ltrim($paymentPath, '/') : '/';
    }

    /**
     * 验证工行回调签名
     *
     * @param array $data 回调数据
     * @return bool 验证结果
     */
    public function verifyIcbcSignature(array $data): bool
    {
        try {
            if (!isset($data['sign'])) {
                error_log("签名验证失败: 缺少签名字段");
                return false;
            }
            
            $sign = $data['sign'];
            unset($data['sign']);
            
            // 过滤空值并排序
            $filteredData = [];
            foreach ($data as $key => $value) {
                if ($value !== '' && $value !== null) {
                    $filteredData[$key] = $value;
                }
            }
            ksort($filteredData);
            
            // 构建验签字符串
            $signParts = [];
            foreach ($filteredData as $key => $value) {
                $signParts[] = $key . '=' . $value;
            }
            $signString = implode('&', $signParts);
            error_log("验签字符串: " . $signString);
            
            // 尝试使用不同的公钥文件进行验证
            $publicKeyPaths = [
                $this->getConfig('icbc_public_key_path'),
                $this->getConfig('gateway_key_path'),
                storage_path('keys/icbc_apigw_public_key.pem')
            ];
            
            foreach ($publicKeyPaths as $path) {
                if (!file_exists($path)) {
                    error_log("公钥文件不存在: {$path}");
                    continue;
                }
                
                $publicKeyContent = file_get_contents($path);
                $publicKey = openssl_pkey_get_public($publicKeyContent);
                
                if (!$publicKey) {
                    error_log("公钥格式错误: {$path}");
                    continue;
                }
                
                // 验证签名
                $result = openssl_verify($signString, base64_decode($sign), $publicKey, OPENSSL_ALGO_SHA256);
                openssl_pkey_free($publicKey);
                
                error_log("使用公钥 {$path} 验证结果: " . ($result === 1 ? "成功" : "失败"));
                
                if ($result === 1) {
                    return true;
                }
            }
            
            error_log("所有公钥验证均失败");
            return false;
            
        } catch (Exception $e) {
            error_log("工行签名验证失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 标准化订单数据
     *
     * @param array $orderData 原始订单数据
     * @return array 标准化后的订单数据
     * @throws InvalidArgumentException 当数据无效时抛出异常
     */
    private function normalizeOrderData(array $orderData): array
    {
        // 必填字段映射
        $required = [
            'order_id' => ['order_id', 'out_trade_no', 'orderid'],
            'amount' => ['amount', 'total_amount', 'total_fee'],
            'merchant_order_no' => ['merchant_order_no', 'out_trade_no', 'order_id'],
            'subject' => ['subject', 'title', 'description', 'body'],
        ];

        $normalized = [];

        foreach ($required as $key => $aliases) {
            $value = null;
            foreach ($aliases as $alias) {
                if (isset($orderData[$alias]) && !empty($orderData[$alias])) {
                    $value = $orderData[$alias];
                    break;
                }
            }
            
            if ($value === null) {
                throw new InvalidArgumentException("缺少必填字段: {$key}");
            }
            
            $normalized[$key] = $value;
        }

        // 设置默认值
        $normalized['merchant_id'] = $orderData['merchant_id'] ?? $this->getConfig('merchant_id');
        $normalized['gateway_url'] = $orderData['gateway_url'] ?? $this->getGatewayUrl('payment_url');
        $normalized['currency'] = $orderData['currency'] ?? $this->getConfig('payment.currency');
        $normalized['order_date'] = $orderData['order_date'] ?? date('Y-m-d H:i:s');
        
        // 确保timeout是字符串类型
        $timeout = $orderData['timeout'] ?? $this->getConfig('payment.timeout');
        $timeout = is_numeric($timeout) ? (string)$timeout : '30';
        $normalized['timeout'] = $timeout . 'm';
        
        $normalized['payment_type'] = $orderData['payment_type'] ?? '1';
        $normalized['payment_method'] = $orderData['payment_method'] ?? 'wechat';
        $normalized['need_encrypt'] = $orderData['need_encrypt'] ?? false;

        // 回调URL
        $normalized['notify_url'] = $orderData['notify_url'] ?? $this->getConfig('notify_url');
        $normalized['return_url'] = $orderData['return_url'] ?? $this->getConfig('return_url');

        // 可选字段
        $optional = ['body', 'attach', 'extra_params'];
        foreach ($optional as $field) {
            if (isset($orderData[$field])) {
                $normalized[$field] = $orderData[$field];
            }
        }

        return $normalized;
    }

    /**
     * 获取网关URL
     *
     * @param string $type URL类型
     * @param string|null $fallback 备用URL类型
     * @return string 完整的网关URL
     */
    private function getGatewayUrl(string $type, ?string $fallback = null): string
    {
        $environment = $this->getConfig('environment');
        $gateway = $this->getConfig("gateways.{$environment}") ?? $this->getConfig('gateways.sandbox');

        // 确保gateway是数组
        if (!is_array($gateway)) {
            $gateway = [];
        }

        $baseUrl = $gateway['base_url'] ?? '';

        // 尝试获取指定类型的URL
        $typeUrl = $gateway[$type] ?? '';

        // 如果没有找到且有fallback，尝试使用fallback
        if (empty($typeUrl) && $fallback) {
            $typeUrl = $gateway[$fallback] ?? '';
        }

        // 确保都是字符串
        $baseUrl = is_string($baseUrl) ? $baseUrl : '';
        $typeUrl = is_string($typeUrl) ? $typeUrl : '';

        return rtrim($baseUrl, '/') . '/' . ltrim($typeUrl, '/');
    }

    /**
     * 合并默认配置
     *
     * @param array $config 用户配置
     * @return array 合并后的配置
     */
    private function mergeDefaultConfig(array $config): array
    {
        // 获取存储路径
        $storagePath = $this->getStoragePath();
        
        $defaultConfig = [
            'app_id' => '',
            'mer_id' => '',                    // 商户号
            'mer_prtcl_no' => '',             // 商户协议号
            'sign_type' => 'RSA2',
            'charset' => 'UTF-8',
            'format' => 'json',
            'version' => '1.0.0',
            'environment' => 'sandbox',
            'private_key_path' => $storagePath . '/keys/icbc_private_key.pem',
            'icbc_public_key_path' => $storagePath . '/keys/icbc_public_key.pem',
            'notify_url' => '',
            'return_url' => '',
            'gateways' => [
                'sandbox' => [
                    'base_url' => 'https://gw.open.icbc.com.cn/sandbox',
                    'payment_url' => '/api/cardbusiness/aggregatepay/consumepurchase',
                    'query_url' => '/api/cardbusiness/aggregatepay/orderquery',
                    'refund_url' => '/api/cardbusiness/aggregatepay/refund',
                ],
                'production' => [
                    'base_url' => 'https://gw.open.icbc.com.cn',
                    'payment_url' => '/api/cardbusiness/aggregatepay/consumepurchase',
                    'query_url' => '/api/cardbusiness/aggregatepay/orderquery',
                    'refund_url' => '/api/cardbusiness/aggregatepay/refund',
                ],
            ],
            'payment' => [
                'timeout' => 30,
                'currency' => 'CNY',
                'fee_type' => 'CNY',
                'device_info' => 'WEB',
            ],
            'http' => [
                'timeout' => 30,
                'connect_timeout' => 8,
                'retry_times' => 3,
                'verify_ssl' => true,
            ],
            'dev' => [
                'mock_enabled' => true,
                'debug_enabled' => true,
                'test_mode' => true,
            ],
        ];

        // 使用深度合并，但避免重复数组
        return $this->deepMerge($defaultConfig, $config);
    }

    /**
     * 深度合并配置，避免array_merge_recursive的问题
     *
     * @param array $default 默认配置
     * @param array $config 用户配置
     * @return array 合并后的配置
     */
    private function deepMerge(array $default, array $config): array
    {
        foreach ($config as $key => $value) {
            if (is_array($value) && isset($default[$key]) && is_array($default[$key])) {
                $default[$key] = $this->deepMerge($default[$key], $value);
            } else {
                $default[$key] = $value;
            }
        }
        
        return $default;
    }

    /**
     * 获取存储路径
     *
     * @return string 存储路径
     */
    private function getStoragePath(): string
    {
        // 如果在Laravel环境中，使用storage_path函数
        if (function_exists('storage_path')) {
            return storage_path();
        }
        
        // 否则使用相对路径
        return dirname(__DIR__, 3) . '/storage';
    }

    /**
     * 设置配置
     *
     * @param string $key 配置键
     * @param mixed $value 配置值
     * @return self
     */
    public function setConfig(string $key, $value): self
    {
        $this->setArrayValue($this->config, $key, $value);
        return $this;
    }

    /**
     * 获取配置
     *
     * @param string|null $key 配置键，为null时返回全部配置
     * @param mixed $default 默认值
     * @return mixed 配置值
     */
    public function getConfig(?string $key = null, $default = null)
    {
        if ($key === null) {
            return $this->config;
        }
        
        return $this->getArrayValue($this->config, $key, $default);
    }

    /**
     * 设置数组值（兼容Laravel的data_set函数）
     *
     * @param array &$array 目标数组
     * @param string $key 键名（支持点号分隔）
     * @param mixed $value 值
     * @return void
     */
    private function setArrayValue(array &$array, string $key, $value): void
    {
        if (function_exists('data_set')) {
            data_set($array, $key, $value);
            return;
        }
        
        $keys = explode('.', $key);
        $current = &$array;
        
        foreach ($keys as $k) {
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }

    /**
     * 获取数组值（兼容Laravel的data_get函数）
     *
     * @param array $array 源数组
     * @param string $key 键名（支持点号分隔）
     * @param mixed $default 默认值
     * @return mixed
     */
    private function getArrayValue(array $array, string $key, $default = null)
    {
        if (function_exists('data_get')) {
            return data_get($array, $key, $default);
        }
        
        $keys = explode('.', $key);
        $current = $array;
        
        foreach ($keys as $k) {
            if (!is_array($current) || !array_key_exists($k, $current)) {
                return $default;
            }
            $current = $current[$k];
        }
        
        return $current;
    }

    /**
     * 设置环境
     *
     * @param string $environment 环境名称（sandbox/production）
     * @return self
     */
    public function setEnvironment(string $environment): self
    {
        $this->config['environment'] = $environment;
        return $this;
    }

    /**
     * 设置沙箱模式
     *
     * @return self
     */
    public function sandbox(): self
    {
        return $this->setEnvironment('sandbox');
    }

    /**
     * 设置生产模式
     *
     * @return self
     */
    public function production(): self
    {
        return $this->setEnvironment('production');
    }

    /**
     * 获取服务器同步时间
     * 解决时间戳超时问题
     *
     * @return int Unix时间戳
     */
    private function getSyncTime(): int
    {
        // 获取当前时间
        $currentTime = time();
        
        // 检查配置中是否有时间偏移设置
        $timeOffset = $this->getConfig('time_sync.offset', 0);
        
        // 应用时间偏移
        return $currentTime + $timeOffset;
    }

    /**
     * 获取格式化的时间戳字符串
     * 工商银行API需要北京时间格式
     * 
     * @param int|null $timestamp Unix时间戳，为null时使用当前时间
     * @return string 格式化的时间戳
     */
    private function getFormattedTimestamp(?int $timestamp = null): string
    {
        $timestamp = $timestamp ?? $this->getSyncTime();
        
        // 工商银行使用北京时间，确保时区正确
        $originalTimezone = date_default_timezone_get();
        date_default_timezone_set('Asia/Shanghai');
        
        $formattedTime = date('Y-m-d H:i:s', $timestamp); // 确保使用北京时间格式
        
        // 恢复原时区
        date_default_timezone_set($originalTimezone);
        
        return $formattedTime;
    }

    /**
     * 获取工行API专用的时间戳格式
     * 根据错误信息分析，工行需要北京时间格式
     * 
     * @param int|null $timestamp Unix时间戳
     * @return string 工行API时间戳格式
     */
    private function getIcbcTimestamp(?int $timestamp = null): string
    {
        $timestamp = $timestamp ?? $this->getSyncTime();
        
        // 工商银行使用北京时间，不是UTC时间
        $originalTimezone = date_default_timezone_get();
        date_default_timezone_set('Asia/Shanghai');
        
        $formattedTime = date('Y-m-d H:i:s', $timestamp);
        
        // 恢复原时区
        date_default_timezone_set($originalTimezone);
        
        return $formattedTime;
    }

    /**
     * 获取MSG ID专用的时间戳格式
     * MSG ID需要yyyyMMddHHmmss格式的北京时间
     * 
     * @param int|null $timestamp Unix时间戳
     * @return string MSG ID时间戳格式
     */
    private function getMsgIdTimestamp(?int $timestamp = null): string
    {
        $timestamp = $timestamp ?? $this->getSyncTime();
        
        // 确保使用北京时间
        $originalTimezone = date_default_timezone_get();
        date_default_timezone_set('Asia/Shanghai');
        
        $formattedTime = date('YmdHis', $timestamp);
        
        // 恢复原时区
        date_default_timezone_set($originalTimezone);
        
        return $formattedTime;
    }
}
