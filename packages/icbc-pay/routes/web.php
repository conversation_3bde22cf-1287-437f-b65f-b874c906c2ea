<?php

use Illuminate\Support\Facades\Route;
use IcbcPay\Controllers\IcbcPayController;

Route::group(['prefix' => 'icbc-pay'], function () {
    Route::get('payment-form', [IcbcPayController::class, 'showPaymentForm'])->name('icbc-pay.form');
    Route::post('create-payment', [IcbcPayController::class, 'createPayment'])->name('icbc-pay.create');
    Route::post('notify', [IcbcPayController::class, 'paymentNotify'])->name('icbc-pay.notify');
    Route::get('return', [IcbcPayController::class, 'paymentReturn'])->name('icbc-pay.return');
    Route::get('query', [IcbcPayController::class, 'queryPayment'])->name('icbc-pay.query');
});
