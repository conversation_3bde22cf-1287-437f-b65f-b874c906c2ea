{"name": "hiworld/icbc-pay", "description": "工商银行支付SDK - PHP 8.2版本", "type": "library", "license": "MIT", "version": "dev-master", "keywords": ["icbc", "payment", "php", "sdk", "laravel"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.2", "ext-openssl": "*", "ext-curl": "*", "ext-json": "*", "laravel/framework": "^10.0|^11.0|^12.0"}, "require-dev": {"phpunit/phpunit": "^10.0", "laravel/framework": "^10.0|^11.0|^12.0"}, "autoload": {"psr-4": {"IcbcPay\\": "src/"}}, "autoload-dev": {"psr-4": {"IcbcPay\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["IcbcPay\\IcbcPayServiceProvider"]}}, "scripts": {"test": "phpunit", "psr": "phpcbf --standard=PSR2 src/", "check": "phpcs --standard=PSR2 src/"}, "minimum-stability": "dev", "prefer-stable": true}